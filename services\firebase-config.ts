import { initializeApp, getApps } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth, initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

const firebaseConfig = {
  apiKey: "AIzaSyDDKOb-qvTF2516cWV_9TEAgk5pukmozjc",
  authDomain: "kissandost-9570f.firebaseapp.com",
  databaseURL: "https://kissandost-9570f-default-rtdb.firebaseio.com",
  projectId: "kissandost-9570f",
  storageBucket: "kissandost-9570f.firebasestorage.app",
  messagingSenderId: "400828673471",
  appId: "1:400828673471:web:ef962bc0150b3bb0e3c50a",
  measurementId: "G-Z91G06JJVD"
};

// Initialize Firebase only if no apps exist
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

export const db = getFirestore(app);

// Initialize Auth with React Native persistence
let auth;
try {
  // Try to initialize auth with React Native persistence
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
} catch (error) {
  // If already initialized, get the existing instance
  auth = getAuth(app);
}

export { auth };
export const storage = getStorage(app);
export default app;