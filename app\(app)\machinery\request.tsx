import React, { useState, useEffect, useMemo } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator, KeyboardAvoidingView, Platform, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { createRequest, getRequestById, updateRequest } from "@/services/request-service";
import { getAvailableMachineryByFarm } from "@/services/machinery-service";
import { useLanguage } from "@/context/language-context";
import { Truck, Calendar, FileText, Info, AlertCircle } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import ModalDropdown from "@/components/ModalDropdown";
import DateTimePicker from '@react-native-community/datetimepicker';
import { useLookupStore } from '@/services/lookup_service';

const { width: screenWidth } = Dimensions.get('window');

const REQUEST_TYPES = [
  { key: 'machinery.requestUse', stored: 'use' },
  { key: 'machinery.requestMaintenance', stored: 'maintenance' },
];

const URGENCY_LEVELS = [
  { key: 'common.low', stored: 'low' },
  { key: 'common.medium', stored: 'medium' },
  { key: 'common.high', stored: 'high' },
  { key: 'common.emergency', stored: 'emergency' },
];

const MAINTENANCE_TYPES = [
  { key: 'machinery.routine', stored: 'routine' },
  { key: 'machinery.emergency', stored: 'emergency' },
  { key: 'machinery.repair', stored: 'repair' },
];

const ODOMETER_UNITS = [
  { key: 'machinery.hours', stored: 'hours' },
  { key: 'machinery.kilometers', stored: 'kms' },
];



export default function MachineryRequestScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { colors } = useTheme();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  // Memoize params to prevent infinite loops
  const stableParams = useMemo(() => ({
    machineryName: params.machineryName as string,
    machineryType: params.machineryType as string,
    requestSubType: params.requestSubType as string,
    requestId: params.requestId as string,
    farmId: params.farmId as string,
  }), [params.machineryName, params.machineryType, params.requestSubType, params.requestId, params.farmId]);
  
  const [formData, setFormData] = useState({
    machineryName: "",
    machineryType: "",
    requestSubType: "use",
    reason: "",
    startDate: "",
    endDate: "",
    fuelAmount: "",
    litterPrice: "",
    totalPrice: "",
    maintenanceType: "routine",
    maintenancePrice: "",
    urgency: "medium",
    notes: "",
    targetFarmId: "",
    odometerReading: "",
    odometerUnit: "hours",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isEditMode, setIsEditMode] = useState(false);
  const [originalRequest, setOriginalRequest] = useState<any>(null);
  const [loadingRequest, setLoadingRequest] = useState(false);

  // Auto-calculate total price when fuel amount or litter price changes
  useEffect(() => {
    if (formData.requestSubType === 'use' && formData.fuelAmount && formData.litterPrice) {
      const fuelAmount = Number(formData.fuelAmount) || 0;
      const litterPrice = Number(formData.litterPrice) || 0;
      const totalPrice = fuelAmount * litterPrice;
      setFormData(prev => ({ ...prev, totalPrice: totalPrice.toString() }));
    } else {
      setFormData(prev => ({ ...prev, totalPrice: "" }));
    }
  }, [formData.fuelAmount, formData.litterPrice, formData.requestSubType]);
  const [initialized, setInitialized] = useState(false);
  const [availableMachinery, setAvailableMachinery] = useState<any[]>([]);
  const [customType, setCustomType] = useState('');
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [machineryTypesLookup, setMachineryTypesLookup] = useState<any[]>([]);
  const { getLookupsByCategory } = useLookupStore();

  // Date picker states
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());

  // Prepare dropdown items from lookup data and add "Other (Custom)" option
  const machineryTypes = [
    ...machineryTypesLookup.map(type => ({
      label: t(`machinery.${type.title.toLowerCase().replace(/[^a-z0-9]/g, '_')}`) ||
             t(`machinery.types.${type.title.toLowerCase()}`) ||
             t(`machinery.${type.title.toLowerCase()}`) ||
             type.title,
      value: type.id,
    })),
    // Add "Other (Custom)" option
    {
      label: t('machinery.other_custom') || 'Other (Custom)',
      value: 'other_custom',
    }
  ];

  const requestTypes = REQUEST_TYPES.map(type => ({
    label: t(type.key),
    value: type.stored,
  }));

  const urgencyLevels = URGENCY_LEVELS.map(level => ({
    label: t(level.key),
    value: level.stored,
  }));

  const maintenanceTypes = MAINTENANCE_TYPES.map(type => ({
    label: t(type.key),
    value: type.stored,
  }));

  const odometerUnits = ODOMETER_UNITS.map(unit => ({
    label: t(unit.key),
    value: unit.stored,
  }));

  // Load lookup data
  useEffect(() => {
    const machineryTypesData = getLookupsByCategory('machineryType');
    setMachineryTypesLookup(machineryTypesData);

    // Set default machinery type if not already set and not in edit mode
    if (machineryTypesData.length > 0 && !formData.machineryType && !stableParams.requestId) {
      setFormData(prev => ({
        ...prev,
        machineryType: machineryTypesData[0].id
      }));
    }
  }, [getLookupsByCategory, formData.machineryType, stableParams.requestId]);

  // Load request data for edit mode
  useEffect(() => {
    const loadRequestData = async () => {
      if (stableParams.requestId && stableParams.farmId && !initialized) {
        setLoadingRequest(true);
        setIsEditMode(true);

        try {
          const requestData = await getRequestById(stableParams.requestId, stableParams.farmId);
          if (requestData) {
            setOriginalRequest(requestData);

            // Parse dates
            const startDate = requestData.startDate ? new Date(requestData.startDate) : new Date();
            const endDate = requestData.endDate ? new Date(requestData.endDate) : new Date();

            setStartDate(startDate);
            setEndDate(endDate);

            // Fill form with request data
            setFormData({
              machineryName: requestData.machineryName || "",
              machineryType: requestData.machineryType || (machineryTypesLookup.length > 0 ? machineryTypesLookup[0].id : ""),
              requestSubType: requestData.requestSubType || "use",
              reason: requestData.reason || "",
              startDate: requestData.startDate || "",
              endDate: requestData.endDate || "",
              fuelAmount: requestData.fuelAmount?.toString() || "",
              litterPrice: requestData.litterPrice?.toString() || "",
              totalPrice: requestData.totalPrice?.toString() || "",
              maintenanceType: requestData.maintenanceType || "routine",
              maintenancePrice: (requestData as any).maintenancePrice?.toString() || "",
              urgency: requestData.urgency || "medium",
              notes: requestData.notes || "",
              targetFarmId: (requestData as any).targetFarmId || "",
              odometerReading: (requestData as any).odometerReading?.toString() || "",
              odometerUnit: (requestData as any).odometerUnit || "hours",
            });

            // Set custom type if the machinery type was custom
            if ((requestData as any).customType) {
              setCustomType((requestData as any).customType);
              setFormData(prev => ({ ...prev, machineryType: 'other_custom' }));
            }
          }
        } catch (error) {
          console.error("Error loading request data:", error);
          Alert.alert(t("common.error"), t("requests.loadError"));
        } finally {
          setLoadingRequest(false);
          setInitialized(true);
        }
      }
    };

    loadRequestData();
  }, [stableParams.requestId, stableParams.farmId, initialized]);

  // Initialize form data for create mode
  useEffect(() => {
    if (!initialized && !stableParams.requestId) {
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];

      // Initialize date states
      setStartDate(today);
      setEndDate(today);

      setFormData(prev => ({
        ...prev,
        startDate: todayString,
        // Pre-fill data if coming from machinery card
        ...(stableParams.machineryName && {
          machineryName: stableParams.machineryName,
          machineryType: stableParams.machineryType || (machineryTypesLookup.length > 0 ? machineryTypesLookup[0].id : ""),
          requestSubType: stableParams.requestSubType || "use",
        })
      }));

      setInitialized(true);
    }
  }, [initialized, stableParams]);
  
  // Load available machinery when farm changes
  useEffect(() => {
    if (selectedFarm) {
      loadAvailableMachinery();
    }
  }, [selectedFarm]);
  
  const loadAvailableMachinery = async () => {
    if (!selectedFarm) return;
    
    try {
      const machinery = await getAvailableMachineryByFarm(selectedFarm.id);
      setAvailableMachinery(machinery);
    } catch (error) {
      console.error("Error loading available machinery:", error);
    }
  };
  
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Handle type selection
  const handleTypeSelect = (typeId: string) => {
    setFormData(prev => ({ ...prev, machineryType: typeId }));

    // Clear custom type when switching away from "Other (Custom)"
    if (typeId !== 'other_custom') {
      setCustomType('');
    }
  };

  // Date picker handlers
  const onStartDateChange = (_event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || startDate;
    setShowStartDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setStartDate(currentDate);
      setFormData(prev => ({ ...prev, startDate: currentDate.toISOString().split('T')[0] }));
    }
  };

  const onEndDateChange = (_event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || endDate;
    setShowEndDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setEndDate(currentDate);
      setFormData(prev => ({ ...prev, endDate: currentDate.toISOString().split('T')[0] }));
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };
  
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.machineryName.trim()) {
      newErrors.machineryName = t("machinery.enterMachineryName");
    }

    // Validate custom type if "Other (Custom)" is selected
    if (formData.machineryType === 'other_custom' && !customType.trim()) {
      newErrors.customType = t("machinery.customTypeRequired");
    }
    
    // For existing machinery requests, check if machinery is available
    const existingMachinery = availableMachinery.find(m => 
      m.name.toLowerCase() === formData.machineryName.toLowerCase()
    );
    
    if (existingMachinery && (existingMachinery.status === 'in_use' || existingMachinery.status === 'in_use_other_farm')) {
      newErrors.machineryName = t("machinery.machineryCurrentlyInUse");
    }
    
    if (!formData.reason.trim()) {
      newErrors.reason = t("requests.reasonRequired");
    }
    if (!formData.startDate.trim()) {
      newErrors.startDate = t("machinery.startDateRequired");
    }
    
    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(formData.startDate)) {
      newErrors.startDate = t("machinery.dateFormatInvalid");
    }
    
    if (formData.requestSubType === 'use') {
      if (!formData.endDate.trim()) {
        newErrors.endDate = t("machinery.endDateRequired");
      }
      if (!dateRegex.test(formData.endDate)) {
        newErrors.endDate = t("machinery.dateFormatInvalid");
      }
      if (new Date(formData.endDate) < new Date(formData.startDate)) {
        newErrors.endDate = t("machinery.endDateBeforeStartDate");
      }

      // Fuel amount is mandatory for caretakers
      if (!formData.fuelAmount.trim() || isNaN(Number(formData.fuelAmount)) || Number(formData.fuelAmount) <= 0) {
        newErrors.fuelAmount = t("machinery.fuelAmountRequired");
      }

      // Litter price is mandatory for caretakers
      if (!formData.litterPrice.trim() || isNaN(Number(formData.litterPrice)) || Number(formData.litterPrice) <= 0) {
        newErrors.litterPrice = t("machinery.litterPriceRequired");
      }
    }

    if (formData.requestSubType === 'maintenance') {
      // Maintenance price is mandatory for caretakers
      if (!formData.maintenancePrice.trim() || isNaN(Number(formData.maintenancePrice)) || Number(formData.maintenancePrice) <= 0) {
        newErrors.maintenancePrice = t("machinery.maintenancePriceRequired");
      }
    }

    // Validate numeric fields
    if (formData.odometerReading && (isNaN(Number(formData.odometerReading)) || Number(formData.odometerReading) < 0)) {
      newErrors.odometerReading = formData.odometerUnit === 'hours'
        ? t("machinery.validHoursRequired")
        : t("machinery.validKmsRequired");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async () => {
    if (!selectedFarm || !user) {
      Alert.alert(t("common.error"), t("common.missingRequiredInformation"));
      return;
    }
    
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      const requestorRole = user.role === "caretaker" ? "caretaker" : "admin";

      // Handle type: use custom if provided, otherwise use the machinery type ID
      let finalMachineryType: string;
      if (formData.machineryType === 'other_custom' && customType.trim()) {
        finalMachineryType = customType.trim();
      } else {
        finalMachineryType = formData.machineryType;
      }

      // Validate maintenance type for type safety
      let validatedMaintenanceType: "routine" | "emergency" | "repair" | undefined = undefined;
      if (formData.maintenanceType.trim()) {
        const maintenanceType = formData.maintenanceType.trim() as "routine" | "emergency" | "repair";
        if (['routine', 'emergency', 'repair'].includes(maintenanceType)) {
          validatedMaintenanceType = maintenanceType;
        }
      }
      
      const requestData = {
        farmId: selectedFarm.id,
        farmName: selectedFarm.name,
        requestedBy: user.uid,
        requestedByName: user.displayName || user.email || "Unknown User",
        requestorRole: requestorRole,
        requestType: "machinery" as const,
        itemName: formData.machineryName.trim(),
        quantity: 1,
        unit: "unit",
        reason: formData.reason.trim(),
        notes: formData.notes.trim(),
        status: "pending" as const,
        urgency: formData.urgency as any,
        
        // Machinery-specific fields
        machineryName: formData.machineryName.trim(),
        machineryType: finalMachineryType,
        requestSubType: formData.requestSubType as any,
        startDate: formData.startDate,
        endDate: formData.requestSubType === 'use' ? formData.endDate : formData.startDate,
        fuelAmount: formData.fuelAmount ? Number(formData.fuelAmount) : undefined,
        litterPrice: formData.litterPrice ? Number(formData.litterPrice) : undefined,
        totalPrice: formData.totalPrice ? Number(formData.totalPrice) : undefined,
        maintenanceType: validatedMaintenanceType,
        maintenancePrice: formData.maintenancePrice ? Number(formData.maintenancePrice) : undefined,
        odometerReading: formData.odometerReading ? Number(formData.odometerReading) : undefined,
        odometerUnit: formData.odometerUnit,
        targetFarmId: formData.targetFarmId || undefined,

        // Save custom type if "Other (Custom)" was selected
        ...(formData.machineryType === 'other_custom' && customType.trim() && {
          customType: customType.trim()
        }),
      };
      
      if (isEditMode && originalRequest) {
        // Update existing request
        await updateRequest(originalRequest.id, requestData, originalRequest.farmId);

        Alert.alert(t("common.success"), t("requests.requestUpdated"), [
          {
            text: t("common.ok"),
            onPress: () => router.back()
          }
        ]);
      } else {
        // Create new request
        await createRequest(requestData);

        Alert.alert(t("common.success"), t("requests.machineryRequestSubmitted"), [
          {
            text: t("common.ok"),
            onPress: () => {
              // Reset form
              setFormData({
                machineryName: "",
                machineryType: machineryTypesLookup.length > 0 ? machineryTypesLookup[0].id : "",
                requestSubType: "use",
                reason: "",
                startDate: new Date().toISOString().split('T')[0],
                endDate: "",
                fuelAmount: "",
                litterPrice: "",
                totalPrice: "",
                maintenanceType: "routine",
                maintenancePrice: "",
                urgency: "medium",
                notes: "",
                targetFarmId: "",
                odometerReading: "",
                odometerUnit: "hours",
              });
              setCustomType('');
              setErrors({});
              router.back();
            }
          }
        ]);
      }
    } catch (error) {
      console.error(isEditMode ? "Error updating machinery request:" : "Error creating machinery request:", error);
      Alert.alert(t("common.error"), isEditMode ? t("requests.updateError") : t("requests.createError"));
    } finally {
      setLoading(false);
    }
  };
  


  // Show loading screen while loading request data
  if (loadingRequest) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t("requests.editRequest"),
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: isEditMode ? t("requests.editRequest") : t("requests.requestMachinery"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Banner */}
          <LinearGradient
            colors={[colors.primary + '20', colors.primary + '10']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerBanner}
          >
            <View style={styles.headerContent}>
              <Truck size={32} color={colors.primary} />
              <View style={styles.headerText}>
                <Text style={[styles.headerTitle, { color: colors.text }]}>
                  {t("requests.requestMachinery")}
                </Text>
                <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
                  {t("requests.submitMachineryRequestDescription")}
                </Text>
              </View>
            </View>
          </LinearGradient>

          {/* Availability Warning */}
          {availableMachinery.length === 0 && (
            <View style={[styles.warningBanner, { backgroundColor: colors.warning + '15', borderColor: colors.warning }]}>
              <AlertCircle size={20} color={colors.warning} />
              <Text style={[styles.warningText, { color: colors.text }]}>
                {t("machinery.noAvailableMachinery")} {t("machinery.allMachineryInUseOrMaintenance")}
              </Text>
            </View>
          )}

          {/* Machinery Details Card */}
          <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.sectionHeader}>
              <Truck size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t("machinery.machineryDetails")}
              </Text>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("machinery.machineryName")} *
              </Text>
              <TextInput
                style={[
                  styles.input, 
                  { 
                    backgroundColor: colors.background, 
                    color: colors.text, 
                    borderColor: errors.machineryName ? colors.error : colors.border 
                  }
                ]}
                value={formData.machineryName}
                onChangeText={(value) => handleInputChange("machineryName", value)}
                placeholder={t("machinery.enterMachineryName")}
                placeholderTextColor={colors.textSecondary}
              />
              {errors.machineryName && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.machineryName}
                </Text>
              )}
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t('machinery.machineryType')} *
              </Text>
              <ModalDropdown
                items={machineryTypes}
                selectedValue={formData.machineryType}
                onValueChange={(value) => {
                  handleTypeSelect(value);
                  if (formErrors.machineryType) {
                    setFormErrors(prev => ({ ...prev, machineryType: '' }));
                  }
                }}
                placeholder={t('machinery.selectType')}
                error={!!formErrors.machineryType}
              />

              {formData.machineryType === 'other_custom' && (
                <View>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: colors.text,
                        borderColor: errors.customType ? colors.error : colors.border,
                        backgroundColor: colors.background,
                        marginTop: 8,
                      }
                    ]}
                    placeholder={t('machinery.enterCustomType')}
                    placeholderTextColor={colors.textSecondary}
                    value={customType}
                    onChangeText={(value) => {
                      setCustomType(value);
                      // Clear error when user starts typing
                      if (errors.customType) {
                        setErrors(prev => ({ ...prev, customType: "" }));
                      }
                    }}
                  />
                  {errors.customType && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.customType}
                    </Text>
                  )}
                </View>
              )}
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("common.requestType")} *
              </Text>
              <ModalDropdown
                items={requestTypes}
                selectedValue={formData.requestSubType}
                onValueChange={(value) => {
                  handleInputChange("requestSubType", value);
                  if (formErrors.requestSubType) {
                    setFormErrors(prev => ({ ...prev, requestSubType: '' }));
                  }
                }}
                placeholder={t('machinery.selectRequestType')}
                error={!!formErrors.requestSubType}
              />
            </View>
          </View>
          
          {/* Request Details Card */}
          <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <View style={styles.sectionHeader}>
              <FileText size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t("requests.requestDetails")}
              </Text>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("common.reason")} *
              </Text>
              <TextInput
                style={[
                  styles.textArea, 
                  { 
                    backgroundColor: colors.background, 
                    color: colors.text, 
                    borderColor: errors.reason ? colors.error : colors.border 
                  }
                ]}
                value={formData.reason}
                onChangeText={(value) => handleInputChange("reason", value)}
                placeholder={t("machinery.reasonPlaceholder")}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={4}
              />
              {errors.reason && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.reason}
                </Text>
              )}
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("common.urgencyLevel")} *
              </Text>
              <ModalDropdown
                items={urgencyLevels}
                selectedValue={formData.urgency}
                onValueChange={(value) => {
                  handleInputChange("urgency", value);
                  if (formErrors.urgency) {
                    setFormErrors(prev => ({ ...prev, urgency: '' }));
                  }
                }}
                placeholder={t('common.selectUrgency')}
                error={!!formErrors.urgency}
              />
            </View>
            
            {/* Conditional Fields Based on Request Type */}
            <View style={styles.row}>
              <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("common.startDate")} *
                </Text>
                <TouchableOpacity
                  style={[
                    styles.input,
                    {
                      backgroundColor: colors.background,
                      borderColor: errors.startDate ? colors.error : colors.border,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      paddingHorizontal: 12,
                    }
                  ]}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <Text style={{ color: formData.startDate ? colors.text : colors.textSecondary }}>
                    {formData.startDate ? formatDate(startDate) : t('common.selectDate')}
                  </Text>
                  <Calendar size={20} color={colors.textSecondary} />
                </TouchableOpacity>
                {errors.startDate && (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {errors.startDate}
                  </Text>
                )}
              </View>

              {formData.requestSubType === 'use' && (
                <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("common.endDate")} *
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.input,
                      {
                        backgroundColor: colors.background,
                        borderColor: errors.endDate ? colors.error : colors.border,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingHorizontal: 12,
                      }
                    ]}
                    onPress={() => setShowEndDatePicker(true)}
                  >
                    <Text style={{ color: formData.endDate ? colors.text : colors.textSecondary }}>
                      {formData.endDate ? formatDate(endDate) : t('common.selectDate')}
                    </Text>
                    <Calendar size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                  {errors.endDate && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.endDate}
                    </Text>
                  )}
                </View>
              )}
            </View>
            
            {formData.requestSubType === 'use' && (
              <>
                {/* Fuel Amount Field */}
                <View style={styles.inputGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("machinery.fuelAmount")} (L) *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        backgroundColor: colors.background,
                        color: colors.text,
                        borderColor: errors.fuelAmount ? colors.error : colors.border
                      }
                    ]}
                    value={formData.fuelAmount}
                    onChangeText={(value) => handleInputChange("fuelAmount", value)}
                    placeholder={t("machinery.fuelAmountPlaceholder")}
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="numeric"
                  />
                  {errors.fuelAmount && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.fuelAmount}
                    </Text>
                  )}
                </View>

                {/* Litter Price Field */}
                <View style={styles.inputGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("machinery.litterPrice")} (Rs per L) *
                  </Text>
                  <View style={styles.inputWithIcon}>
                    <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>Rs</Text>
                    <TextInput
                      style={[
                        styles.inputWithPadding,
                        {
                          backgroundColor: colors.background,
                          color: colors.text,
                          borderColor: errors.litterPrice ? colors.error : colors.border
                        }
                      ]}
                      value={formData.litterPrice}
                      onChangeText={(value) => handleInputChange("litterPrice", value)}
                      placeholder={t("machinery.enterLitterPrice")}
                      placeholderTextColor={colors.textSecondary}
                      keyboardType="numeric"
                    />
                  </View>
                  {errors.litterPrice && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.litterPrice}
                    </Text>
                  )}
                </View>

                {/* Total Price Field (Disabled) */}
                <View style={styles.inputGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("machinery.totalPrice")} (Rs)
                  </Text>
                  <View style={styles.inputWithIcon}>
                    <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>Rs</Text>
                    <TextInput
                      style={[
                        styles.inputWithPadding,
                        styles.disabledInput,
                        {
                          backgroundColor: colors.background,
                          color: colors.textSecondary,
                          borderColor: colors.border,
                          opacity: 0.6
                        }
                      ]}
                      value={formData.totalPrice ? Number(formData.totalPrice).toLocaleString() : '0'}
                      editable={false}
                      placeholder="0"
                      placeholderTextColor={colors.textSecondary}
                    />
                  </View>
                  <Text style={[styles.helperText, { color: colors.textSecondary }]}>
                    {t("machinery.totalPriceCalculated")}
                  </Text>
                </View>
              </>
            )}
            
            {formData.requestSubType === 'maintenance' && (
              <>
                <View style={styles.inputGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("machinery.maintenanceType")} *
                  </Text>
                  <ModalDropdown
                    items={maintenanceTypes}
                    selectedValue={formData.maintenanceType}
                    onValueChange={(value) => {
                      handleInputChange("maintenanceType", value);
                      if (formErrors.maintenanceType) {
                        setFormErrors(prev => ({ ...prev, maintenanceType: '' }));
                      }
                    }}
                    placeholder={t('machinery.selectMaintenanceType')}
                    error={!!formErrors.maintenanceType}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("machinery.maintenancePrice")} (Rs) *
                  </Text>
                  <View style={styles.inputWithIcon}>
                    <Text style={[styles.currencySymbol, { color: colors.textSecondary }]}>Rs</Text>
                    <TextInput
                      style={[
                        styles.inputWithPadding,
                        {
                          backgroundColor: colors.background,
                          color: colors.text,
                          borderColor: errors.maintenancePrice ? colors.error : colors.border
                        }
                      ]}
                      value={formData.maintenancePrice}
                      onChangeText={(value) => handleInputChange("maintenancePrice", value)}
                      placeholder={t("machinery.enterMaintenancePrice")}
                      placeholderTextColor={colors.textSecondary}
                      keyboardType="numeric"
                    />
                  </View>
                  {errors.maintenancePrice && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.maintenancePrice}
                    </Text>
                  )}
                </View>
              </>
            )}
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("machinery.odometerReading")}
              </Text>

              {/* Combined Unit and Reading Input */}
              <View style={styles.row}>
                {/* Unit Selection - Shorter */}
                <View style={[styles.inputGroup, { flex: 0.35, marginRight: 8 }]}>
                  <ModalDropdown
                    items={odometerUnits}
                    selectedValue={formData.odometerUnit}
                    onValueChange={(value) => {
                      handleInputChange("odometerUnit", value);
                    }}
                    placeholder={t('machinery.selectUnit')}
                    error={false}
                  />
                </View>

                {/* Reading Input - Longer */}
                <View style={[styles.inputGroup, { flex: 0.65, marginLeft: 8 }]}>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        backgroundColor: colors.background,
                        color: colors.text,
                        borderColor: errors.odometerReading ? colors.error : colors.border
                      }
                    ]}
                    value={formData.odometerReading}
                    onChangeText={(value) => handleInputChange("odometerReading", value)}
                    placeholder={formData.odometerUnit === 'hours' ? "e.g., 1250" : "e.g., 15000"}
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {errors.odometerReading && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.odometerReading}
                </Text>
              )}
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t("common.notes")}
              </Text>
              <TextInput
                style={[
                  styles.textArea, 
                  { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }
                ]}
                value={formData.notes}
                onChangeText={(value) => handleInputChange("notes", value)}
                placeholder={t("machinery.enterNotes")}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>

          {/* Info Note */}
          <View style={[styles.infoNote, { backgroundColor: colors.info + '15' }]}>
            <Info size={20} color={colors.info} />
            <Text style={[styles.infoText, { color: colors.text }]}>
              {t("requests.machineryRequestNote")}
            </Text>
          </View>
          
          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitButton, 
              { 
                backgroundColor: colors.primary, 
                opacity: loading ? 0.7 : 1 
              }
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.submitButtonText}>
                {isEditMode ? t("common.save") : t("requests.submitRequest")}
              </Text>
            )}
          </TouchableOpacity>
        </ScrollView>

        {/* Date Pickers */}
        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display={Platform.OS === "ios" ? "spinner" : "default"}
            onChange={onStartDateChange}
            minimumDate={new Date()}
          />
        )}

        {showEndDatePicker && (
          <DateTimePicker
            value={endDate}
            mode="date"
            display={Platform.OS === "ios" ? "spinner" : "default"}
            onChange={onEndDateChange}
            minimumDate={startDate}
          />
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  headerBanner: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  warningBanner: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
  },
  warningText: {
    marginLeft: 12,
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  formContainer: {
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 16 : 20,
    marginBottom: screenWidth < 380 ? 16 : 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: screenWidth < 380 ? 16 : 20,
  },
  sectionTitle: {
    fontSize: screenWidth < 380 ? 16 : 18,
    fontWeight: "600",
    marginLeft: screenWidth < 380 ? 8 : 12,
    lineHeight: screenWidth < 380 ? 20 : 24,
  },
  inputGroup: {
    marginBottom: screenWidth < 380 ? 16 : 20,
  },
  label: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "500",
    marginBottom: screenWidth < 380 ? 6 : 8,
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  input: {
    borderWidth: 1,
    borderRadius: screenWidth < 380 ? 10 : 12,
    padding: screenWidth < 380 ? 12 : 14,
    fontSize: screenWidth < 380 ? 14 : 16,
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: screenWidth < 380 ? 10 : 12,
    padding: screenWidth < 380 ? 12 : 14,
    fontSize: screenWidth < 380 ? 14 : 16,
    minHeight: screenWidth < 380 ? 80 : 100,
    textAlignVertical: "top",
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  row: {
    flexDirection: screenWidth < 380 ? "column" : "row",
    gap: screenWidth < 380 ? 8 : 12,
  },
  optionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: screenWidth < 380 ? 8 : 12,
  },
  optionCard: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    minWidth: 120,
    gap: 8,
  },
  optionIcon: {
    fontSize: 16,
  },
  optionText: {
    fontSize: 14,
    fontWeight: "500",
  },
  infoNote: {
    flexDirection: "row",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: "flex-start",
  },
  infoText: {
    marginLeft: 12,
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  submitButton: {
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  inputWithIcon: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  inputWithPadding: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingLeft: 48,
    fontSize: 16,
    flex: 1,
  },
  disabledInput: {
    opacity: 0.6,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});