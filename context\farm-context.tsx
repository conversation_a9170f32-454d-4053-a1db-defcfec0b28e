import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Farm, getFarms, getFarmsByUserId } from '@/services/farm-service';
import { useAuth } from './auth-context';

interface FarmContextType {
  farms: Farm[];
  selectedFarm: Farm | null;
  setSelectedFarm: (farm: Farm | null) => void;
  isLoading: boolean;
  refreshFarms: () => Promise<void>;
  userFarms: Farm[];
}

const FarmContext = createContext<FarmContextType | undefined>(undefined);

export const FarmProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [lastUserId, setLastUserId] = useState<string | null>(null);
  
  // Use refs to track loading state and prevent duplicate calls
  const lastLoadTimeRef = useRef(0);
  const isLoadingRef = useRef(false);
  const manualSelectionRef = useRef(false); // Track manual farm selection

  // All farms assigned to the current user
  const userFarms = farms;

  // Reset state when user changes
  useEffect(() => {
    if (user?.uid !== lastUserId) {
      console.log("User changed, resetting farm context", { 
        oldUser: lastUserId, 
        newUser: user?.uid 
      });
      
      // Reset all state
      setFarms([]);
      setSelectedFarm(null);
      lastLoadTimeRef.current = 0;
      setInitialLoadComplete(false);
      setLastUserId(user?.uid || null);
      isLoadingRef.current = false;
      manualSelectionRef.current = false;
      
      // Clear selected farm from storage
      AsyncStorage.removeItem('selectedFarmId').catch(console.error);
    }
  }, [user?.uid, lastUserId]);

  const loadFarms = useCallback(async (forceRefresh = false) => {
    // Prevent duplicate calls
    if (isLoadingRef.current && !forceRefresh) {
      console.log("Farm loading already in progress, skipping...");
      return;
    }

    // Skip loading if we've loaded farms recently (within the last 5 minutes) and not forcing refresh
    // Also skip if there's no user
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    if (
      (!forceRefresh && now - lastLoadTimeRef.current < fiveMinutes && farms.length > 0) ||
      !user
    ) {
      setIsLoading(false);
      return;
    }

    isLoadingRef.current = true;
    setIsLoading(true);
    
    try {
      let userAssignedFarms: Farm[] = [];
      
      // Get farms assigned to the user based on their role
      if (user) {
        console.log("Loading farms for user:", user.uid, "role:", user.role);
        userAssignedFarms = await getFarmsByUserId(user.uid, user.role);
        console.log("Loaded farms:", userAssignedFarms.length);
      }
      
      setFarms(userAssignedFarms);
      lastLoadTimeRef.current = now;
      
      // Only auto-select farm if no manual selection has been made
      if (!manualSelectionRef.current) {
        // Try to restore the previously selected farm only if it belongs to current user
        const savedFarmId = await AsyncStorage.getItem('selectedFarmId');
        
        if (savedFarmId && userAssignedFarms.length > 0) {
          const savedFarm = userAssignedFarms.find(farm => farm.id === savedFarmId);
          if (savedFarm) {
            console.log("Restored selected farm:", savedFarm.name);
            setSelectedFarm(savedFarm);
          } else {
            // If saved farm not found, select the first available farm
            console.log("Saved farm not found, selecting first farm");
            setSelectedFarm(userAssignedFarms[0]);
            await AsyncStorage.setItem('selectedFarmId', userAssignedFarms[0].id);
          }
        } else if (userAssignedFarms.length > 0) {
          // If no saved farm, select the first farm
          console.log("No saved farm, selecting first farm");
          setSelectedFarm(userAssignedFarms[0]);
          await AsyncStorage.setItem('selectedFarmId', userAssignedFarms[0].id);
        } else {
          // No farms available
          console.log("No farms available for user");
          setSelectedFarm(null);
          await AsyncStorage.removeItem('selectedFarmId');
        }
      } else {
        // Manual selection was made, verify it's still valid
        if (selectedFarm && !userAssignedFarms.find(f => f.id === selectedFarm.id)) {
          // Selected farm is no longer available, clear it
          console.log("Previously selected farm no longer available, clearing selection");
          setSelectedFarm(null);
          await AsyncStorage.removeItem('selectedFarmId');
          manualSelectionRef.current = false;
        }
      }
    } catch (error) {
      console.error('Error loading farms:', error);
      setFarms([]);
      setSelectedFarm(null);
    } finally {
      setIsLoading(false);
      setInitialLoadComplete(true);
      isLoadingRef.current = false;
    }
  }, [user]); // Removed selectedFarm dependency to prevent infinite loop

  // Load farms only on initial mount and when user changes
  useEffect(() => {
    if (user && !initialLoadComplete) {
      console.log("Initial farm load for user:", user.uid);
      loadFarms();
    } else if (!user) {
      console.log("No user, clearing farm state");
      setFarms([]);
      setSelectedFarm(null);
      setInitialLoadComplete(false);
      isLoadingRef.current = false;
      manualSelectionRef.current = false;
    }
  }, [user?.uid, initialLoadComplete]); // Use user.uid to prevent unnecessary re-renders

  // Validate selected farm when farms list changes
  useEffect(() => {
    if (manualSelectionRef.current && selectedFarm && farms.length > 0) {
      // Manual selection was made, verify it's still valid
      if (!farms.find(f => f.id === selectedFarm.id)) {
        // Selected farm is no longer available, clear it
        console.log("Previously selected farm no longer available, clearing selection");
        setSelectedFarm(null);
        AsyncStorage.removeItem('selectedFarmId').catch(console.error);
        manualSelectionRef.current = false;
      }
    }
  }, [farms, selectedFarm]);

  const handleSetSelectedFarm = async (farm: Farm | null) => {
    console.log("Setting selected farm:", farm?.name || "none");
    
    // Mark as manual selection
    manualSelectionRef.current = true;
    
    // Update state immediately
    setSelectedFarm(farm);
    
    // Save to storage
    if (farm) {
      await AsyncStorage.setItem('selectedFarmId', farm.id);
    } else {
      await AsyncStorage.removeItem('selectedFarmId');
      manualSelectionRef.current = false;
    }
    
    console.log("Farm selection completed, new farm:", farm?.name || "none");
  };

  const refreshFarms = async () => {
    console.log("Refreshing farms...");
    // Don't reset manual selection flag on refresh
    await loadFarms(true);
  };

  return (
    <FarmContext.Provider
      value={{
        farms,
        selectedFarm,
        setSelectedFarm: handleSetSelectedFarm,
        isLoading,
        refreshFarms,
        userFarms,
      }}
    >
      {children}
    </FarmContext.Provider>
  );
};

export const useFarm = () => {
  const context = useContext(FarmContext);
  if (context === undefined) {
    throw new Error('useFarm must be used within a FarmProvider');
  }
  return context;
};