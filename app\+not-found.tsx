import { Link, Stack, router } from "expo-router";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { useAuth } from "@/context/auth-context";

export default function NotFoundScreen() {
  const { user } = useAuth();

  const handleGoHome = () => {
    if (!user) {
      router.replace("/(auth)/login");
    } else if (user.role === "owner") {
      router.replace("/(app)/(owner)/(tabs)");
    } else if (user.role === "admin") {
      router.replace("/(app)/(admin)");
    } else {
      router.replace("/(app)/(caretaker)");
    }
  };

  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <View style={styles.container}>
        <Text style={styles.title}>This screen doesn't exist.</Text>

        <TouchableOpacity style={styles.button} onPress={handleGoHome}>
          <Text style={styles.buttonText}>Go to home screen!</Text>
        </TouchableOpacity>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
  },
  button: {
    backgroundColor: "#2E7D32",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 15,
  },
  buttonText: {
    fontSize: 16,
    color: "#fff",
    fontWeight: "600",
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
  linkText: {
    fontSize: 14,
    color: "#2e78b7",
  },
});