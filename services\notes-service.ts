import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, orderBy, limit, Timestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";

const db = getFirestore();

// Define Note type
export interface Note {
  id: string;
  title: string;
  content: string;
  farmId: string;
  farmName?: string; // Optional farm name for display
  userId: string; // User who created the note
  userName?: string; // User name who created the note
  createdBy?: string; // For backward compatibility
  createdByName?: string; // For backward compatibility
  hasAudio?: boolean;
  audioUrl?: string;
  images?: string[];
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  createdAt: string;
  updatedAt?: string;
}

// Get all notes for a farm
export const getNotesByFarm = async (farmId: string): Promise<Note[]> => {
  try {
    if (!farmId) {
      console.log("No farmId provided to getNotesByFarm");
      return [];
    }
    
    console.log(`Getting notes for farm: ${farmId}`);
    
    const notesRef = collection(db, "farms", farmId, "notes");
    const notesQuery = query(notesRef, orderBy("createdAt", "desc"));
    
    const notesSnapshot = await getDocs(notesQuery);
    
    const notes: Note[] = [];
    notesSnapshot.forEach(doc => {
      const data = doc.data();
      notes.push({
        id: doc.id,
        ...data,
        farmId,
        // Ensure required fields have defaults
        userId: data.userId || data.createdBy || 'unknown',
        userName: data.userName || data.createdByName || 'Unknown User',
        createdBy: data.createdBy || data.userId || 'unknown',
        createdByName: data.createdByName || data.userName || 'Unknown User'
      } as Note);
    });
    
    console.log(`Retrieved ${notes.length} notes`);
    
    // Cache the results
    const cacheKey = `notes_${farmId}`;
    await AsyncStorage.setItem(cacheKey, JSON.stringify(notes));
    
    return notes;
  } catch (error) {
    console.error("Error getting notes:", error);
    
    // Try to get from cache if available
    const cacheKey = `notes_${farmId}`;
    try {
      const cachedNotes = await AsyncStorage.getItem(cacheKey);
      if (cachedNotes) {
        console.log("Returning cached notes");
        return JSON.parse(cachedNotes);
      }
    } catch (cacheError) {
      console.error("Error reading cached notes:", cacheError);
    }
    
    // Return mock data if both Firestore and cache fail
    return [
      {
        id: "mock-1",
        title: "Sample Note 1",
        content: "This is a sample note for testing purposes.",
        farmId,
        userId: "mock-user-1",
        userName: "John Doe",
        createdBy: "mock-user-1",
        createdByName: "John Doe",
        priority: "medium",
        category: "general",
        createdAt: new Date().toISOString(),
      },
      {
        id: "mock-2",
        title: "Important Reminder",
        content: "Remember to check the irrigation system tomorrow morning.",
        farmId,
        userId: "mock-user-2",
        userName: "Jane Smith",
        createdBy: "mock-user-2",
        createdByName: "Jane Smith",
        priority: "high",
        category: "maintenance",
        createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      },
      {
        id: "mock-3",
        title: "Weekly Report",
        content: "All systems are running smoothly. No issues to report.",
        farmId,
        userId: "mock-user-1",
        userName: "John Doe",
        createdBy: "mock-user-1",
        createdByName: "John Doe",
        priority: "low",
        category: "report",
        createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      }
    ];
  }
};

// Export getNotes as an alias for getNotesByFarm for backward compatibility
export const getNotes = getNotesByFarm;

// Get notes by user
export const getNotesByUser = async (userId: string, farmId: string): Promise<Note[]> => {
  try {
    if (!userId) {
      console.log("No userId provided to getNotesByUser");
      return [];
    }
    
    if (!farmId) {
      console.log("No farmId provided to getNotesByUser");
      return [];
    }
    
    console.log(`Getting notes for user: ${userId} in farm: ${farmId}`);
    
    const notesRef = collection(db, "farms", farmId, "notes");
    const notesQuery = query(
      notesRef,
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );
    
    const notesSnapshot = await getDocs(notesQuery);
    
    const notes: Note[] = [];
    notesSnapshot.forEach(doc => {
      const data = doc.data();
      notes.push({
        id: doc.id,
        ...data,
        farmId,
        // Ensure required fields have defaults
        userId: data.userId || data.createdBy || 'unknown',
        userName: data.userName || data.createdByName || 'Unknown User',
        createdBy: data.createdBy || data.userId || 'unknown',
        createdByName: data.createdByName || data.userName || 'Unknown User'
      } as Note);
    });
    
    console.log(`Retrieved ${notes.length} notes for user ${userId}`);
    
    return notes;
  } catch (error) {
    console.error("Error getting notes by user:", error);
    return [];
  }
};

// Get note by ID
export const getNoteById = async (id: string, farmId: string): Promise<Note | null> => {
  try {
    if (!farmId) {
      console.log("No farmId provided to getNoteById");
      return null;
    }
    
    console.log(`Getting note by ID: ${id} for farm: ${farmId}`);
    
    const noteRef = doc(db, "farms", farmId, "notes", id);
    const noteDoc = await getDoc(noteRef);
    
    if (noteDoc.exists()) {
      const data = noteDoc.data();
      return {
        id: noteDoc.id,
        ...data,
        farmId,
        // Ensure required fields have defaults
        userId: data.userId || data.createdBy || 'unknown',
        userName: data.userName || data.createdByName || 'Unknown User',
        createdBy: data.createdBy || data.userId || 'unknown',
        createdByName: data.createdByName || data.userName || 'Unknown User'
      } as Note;
    }
    
    return null;
  } catch (error) {
    console.error("Error getting note by ID:", error);
    return null;
  }
};

// Create a new note
export const createNote = async (noteData: Partial<Note>): Promise<Note> => {
  try {
    if (!noteData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = noteData.farmId;
    console.log(`Creating note for farm: ${farmId}`);
    
    // Remove undefined values
    const cleanedNoteData = Object.entries(noteData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const newNote = {
      ...cleanedNoteData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to Firestore
    const notesRef = collection(db, "farms", farmId, "notes");
    const docRef = await addDoc(notesRef, newNote);
    
    const createdNote: Note = {
      ...newNote as Note,
      id: docRef.id
    };
    
    return createdNote;
  } catch (error) {
    console.error("Error creating note:", error);
    throw error;
  }
};

// Update a note
export const updateNote = async (id: string, noteData: Partial<Note>, farmId: string): Promise<Note> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    console.log(`Updating note: ${id} for farm: ${farmId}`);
    
    // Get the current note
    const noteRef = doc(db, "farms", farmId, "notes", id);
    const noteDoc = await getDoc(noteRef);
    
    if (!noteDoc.exists()) {
      throw new Error("Note not found");
    }
    
    // Remove undefined values
    const cleanedNoteData = Object.entries(noteData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
    
    const updatedNote = {
      ...cleanedNoteData,
      updatedAt: new Date().toISOString(),
    };
    
    // Update in Firestore
    await updateDoc(noteRef, updatedNote);
    
    const currentData = noteDoc.data();
    
    return {
      id,
      ...currentData,
      ...cleanedNoteData,
      farmId,
      updatedAt: updatedNote.updatedAt,
      // Ensure required fields have defaults
      userId: cleanedNoteData.userId || currentData.userId || currentData.createdBy || 'unknown',
      userName: cleanedNoteData.userName || currentData.userName || currentData.createdByName || 'Unknown User',
      createdBy: cleanedNoteData.createdBy || currentData.createdBy || currentData.userId || 'unknown',
      createdByName: cleanedNoteData.createdByName || currentData.createdByName || currentData.userName || 'Unknown User'
    } as Note;
  } catch (error) {
    console.error("Error updating note:", error);
    throw error;
  }
};

// Delete a note
export const deleteNote = async (id: string, farmId: string): Promise<boolean> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    console.log(`Deleting note: ${id} for farm: ${farmId}`);
    
    // Delete from Firestore
    const noteRef = doc(db, "farms", farmId, "notes", id);
    await deleteDoc(noteRef);
    
    return true;
  } catch (error) {
    console.error("Error deleting note:", error);
    throw error;
  }
};

// Export the service object for backward compatibility
export const notesService = {
  getNotesByFarm,
  getNotes,
  getNotesByUser,
  getNoteById,
  createNote,
  updateNote,
  deleteNote
};