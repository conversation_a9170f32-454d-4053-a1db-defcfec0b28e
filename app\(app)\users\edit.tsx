import { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, ScrollView, Alert, ActivityIndicator, Platform, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { updateUser, User, getUserById, UserRole } from "@/services/user-service";
import { getFarms, Farm, getLocationDisplay, getFarmsByIds } from "@/services/farm-service";
import { ArrowLeft, Check, Eye, EyeOff, Calendar, User as UserIcon, MapPin, CreditCard, Image as ImageIcon, Camera } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import * as ImagePicker from 'expo-image-picker';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { uploadUserProfileImage, deleteImageFromStorage } from "@/services/storage-service";

export default function EditUserScreen() {
  const { theme, colors } = useTheme();
  const { selectedFarm } = useFarm();
  const { user: currentUser } = useAuth();
  const isDarkMode = theme === "dark";
  const { id } = useLocalSearchParams(); // Get the user ID from the route params
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [userData, setUserData] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    role: "caretaker" as UserRole,
    farmIds: [] as string[],
    phone: "",
    phoneNumber: "",
    bio: "",
    gender: "",
    dateOfBirth: "",
    cnic: "",
    address: "",
    photoURL: "",
  });
  const [formErrors, setFormErrors] = useState({
    name: "",
    email: "",
    farmIds: "",
    gender: "",
    dateOfBirth: "",
    cnic: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showCamera, setShowCamera] = useState(false);
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  // Get role-based primary color
  const getRolePrimaryColor = () => {
    if (!currentUser) return colors.primary;
    switch (currentUser.role) {
      case 'owner':
        return isDarkMode ? '#2E7D32' : '#4CAF50';
      case 'admin':
        return isDarkMode ? '#1976D2' : '#2196F3';
      case 'caretaker':
        return isDarkMode ? '#F57C00' : '#FF9800';
      default:
        return colors.primary;
    }
  };

  const primaryColor = getRolePrimaryColor();

  // Gender options
  const genderOptions = [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  // Determine available roles based on current user's role
  const getAvailableRoles = () => {
    if (currentUser?.role === "owner") {
      return [
        { value: "admin" as UserRole, label: "Admin" },
        { value: "caretaker" as UserRole, label: "Caretaker" }
      ];
    } else if (currentUser?.role === "admin") {
      return [
        { value: "caretaker" as UserRole, label: "Caretaker" }
      ];
    }
    return [{ value: "caretaker" as UserRole, label: "Caretaker" }];
  };

  const availableRoles = getAvailableRoles();

  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Load user data first
      if (id) {
        const user = await getUserById(id as string);
        if (user) {
          setUserData(user);
          setFormData({
            name: user.name || "",
            email: user.email || "",
            role: user.role || "caretaker",
            farmIds: user.assignedFarmIds || user.farmIds || (user.farmId ? [user.farmId] : []),
            phone: user.phone || "",
            phoneNumber: user.phoneNumber || user.phone || "",
            bio: user.bio || "",
            gender: user.gender || "",
            dateOfBirth: user.dateOfBirth || "",
            cnic: user.cnic || "",
            address: user.address || "",
            photoURL: user.photoURL || "",
          });

          // Load only the user's assigned farms
          const userFarmIds = user.assignedFarmIds || user.farmIds || (user.farmId ? [user.farmId] : []);
          if (userFarmIds.length > 0) {
            const userFarms = await getFarmsByIds(userFarmIds);
            setFarms(userFarms || []);
          } else {
            setFarms([]);
          }
        } else {
          Alert.alert("Error", "User not found");
          router.back();
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      Alert.alert("Error", "Failed to load data");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Format CNIC input
  const formatCNIC = (text: string) => {
    // Remove all non-digits
    const digits = text.replace(/\D/g, '');
    
    // Format as XXXXX-XXXXXXX-X
    if (digits.length <= 5) {
      return digits;
    } else if (digits.length <= 12) {
      return `${digits.slice(0, 5)}-${digits.slice(5)}`;
    } else {
      return `${digits.slice(0, 5)}-${digits.slice(5, 12)}-${digits.slice(12, 13)}`;
    }
  };

  // Format date input (DD/MM/YYYY)
  const formatDate = (text: string) => {
    // Remove all non-digits
    const digits = text.replace(/\D/g, '');
    
    // Format as DD/MM/YYYY
    if (digits.length <= 2) {
      return digits;
    } else if (digits.length <= 4) {
      return `${digits.slice(0, 2)}/${digits.slice(2)}`;
    } else {
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/${digits.slice(4, 8)}`;
    }
  };

  const validateForm = () => {
    let valid = true;
    const errors = {
      name: "",
      email: "",
      farmIds: "",
      gender: "",
      dateOfBirth: "",
      cnic: "",
    };

    if (!formData.name.trim()) {
      errors.name = "Name is required";
      valid = false;
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
      valid = false;
    }

    if (formData.farmIds.length === 0) {
      errors.farmIds = "At least one farm must be selected";
      valid = false;
    }

    if (!formData.gender) {
      errors.gender = "Gender is required";
      valid = false;
    }

    if (!formData.dateOfBirth.trim()) {
      errors.dateOfBirth = "Date of birth is required";
      valid = false;
    } else if (!/^\d{2}\/\d{2}\/\d{4}$/.test(formData.dateOfBirth)) {
      errors.dateOfBirth = "Date must be in DD/MM/YYYY format";
      valid = false;
    }

    if (!formData.cnic.trim()) {
      errors.cnic = "CNIC is required";
      valid = false;
    } else if (!/^\d{5}-\d{7}-\d{1}$/.test(formData.cnic)) {
      errors.cnic = "CNIC must be in XXXXX-XXXXXXX-X format";
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      if (id) {
        let photoURL = formData.photoURL;

        // Upload image to Firebase Storage if a new local image is selected
        if (formData.photoURL && formData.photoURL.startsWith('file://')) {
          console.log("Uploading new user profile image to Storage...");

          // Delete old image if it exists and is from Firebase Storage
          if (userData?.photoURL) {
            await deleteImageFromStorage(userData.photoURL);
          }

          // Upload new image
          photoURL = await uploadUserProfileImage(formData.photoURL, id as string);
          console.log("User profile image uploaded:", photoURL);
        }

        await updateUser(id as string, {
          name: formData.name,
          email: formData.email,
          role: formData.role,
          assignedFarmIds: formData.farmIds,
          phone: formData.phone,
          phoneNumber: formData.phoneNumber || formData.phone,
          bio: formData.bio,
          gender: formData.gender,
          dateOfBirth: formData.dateOfBirth,
          cnic: formData.cnic,
          address: formData.address,
          photoURL: photoURL,
        });
        Alert.alert("Success", "User updated successfully", [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]);
      }
    } catch (error) {
      console.error("Error updating user:", error);
      Alert.alert("Error", "Failed to update user. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const toggleFarmSelection = (farmId: string) => {
    const updatedFarmIds = formData.farmIds.includes(farmId)
      ? formData.farmIds.filter(id => id !== farmId)
      : [...formData.farmIds, farmId];
    
    setFormData({ ...formData, farmIds: updatedFarmIds });
  };

  const handleGoBack = () => {
    router.back();
  };

  const pickImage = async () => {
    // Request permission to access media library
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert("Permission denied", "Sorry, we need camera roll permissions to make this work!");
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false, // Remove cropping
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      setFormData({ ...formData, photoURL: result.assets[0].uri });
    }
  };

  const takePhoto = async () => {
    if (!cameraPermission) {
      return;
    }

    if (!cameraPermission.granted) {
      const { status } = await requestCameraPermission();
      if (status !== 'granted') {
        Alert.alert("Permission denied", "Sorry, we need camera permissions to make this work!");
        return;
      }
    }

    setShowCamera(true);
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const capturePhoto = async () => {
    if (cameraRef) {
      const photo = await cameraRef.takePictureAsync();
      setFormData({ ...formData, photoURL: photo.uri });
      setShowCamera(false);
    }
  };

  let cameraRef: any;

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Edit User",
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      </SafeAreaView>
    );
  }

  if (showCamera && Platform.OS !== 'web') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Capture Photo",
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
            headerLeft: () => (
              <TouchableOpacity onPress={() => setShowCamera(false)} style={styles.headerButton}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <CameraView
          style={styles.camera}
          facing={facing}
          ref={ref => {
            cameraRef = ref;
          }}
        >
          <View style={styles.cameraButtonContainer}>
            <TouchableOpacity style={styles.cameraButton} onPress={toggleCameraFacing}>
              <Text style={styles.cameraButtonText}>Flip Camera</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.cameraButton, styles.captureButton, { backgroundColor: primaryColor }]} onPress={capturePhoto}>
              <Text style={styles.cameraButtonText}>Capture</Text>
            </TouchableOpacity>
          </View>
        </CameraView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Edit User",
          headerStyle: { backgroundColor: primaryColor },
          headerTintColor: "#fff",
          headerTitleStyle: { fontWeight: "bold" },
          headerLeft: () => (
            <TouchableOpacity onPress={handleGoBack} style={styles.headerButton}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity 
              onPress={handleSubmit} 
              style={styles.headerButton}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Check size={24} color="#fff" />
              )}
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          {/* Basic Information Section */}
          <View style={styles.sectionHeader}>
            <UserIcon size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>Basic Information</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Full Name *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor },
                formErrors.name ? styles.inputError : null
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder="Enter full name"
              placeholderTextColor={secondaryTextColor}
              autoCapitalize="words"
            />
            {formErrors.name ? (
              <Text style={styles.errorText}>{formErrors.name}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Email Address *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor },
                formErrors.email ? styles.inputError : null
              ]}
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              placeholder="Enter email address"
              placeholderTextColor={secondaryTextColor}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {formErrors.email ? (
              <Text style={styles.errorText}>{formErrors.email}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Gender *</Text>
            <View style={styles.genderContainer}>
              {genderOptions.map((gender) => (
                <TouchableOpacity
                  key={gender.value}
                  style={[
                    styles.genderOption,
                    { borderColor, backgroundColor: cardColor },
                    formData.gender === gender.value && [styles.selectedGenderOption, { backgroundColor: primaryColor, borderColor: primaryColor }]
                  ]}
                  onPress={() => setFormData({ ...formData, gender: gender.value })}
                >
                  <Text
                    style={[
                      styles.genderOptionText,
                      { color: textColor },
                      formData.gender === gender.value && styles.selectedGenderOptionText
                    ]}
                  >
                    {gender.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {formErrors.gender ? (
              <Text style={styles.errorText}>{formErrors.gender}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Date of Birth *</Text>
            <View style={styles.inputWithIcon}>
              <TextInput
                style={[
                  styles.inputWithIconText,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.dateOfBirth ? styles.inputError : null
                ]}
                value={formData.dateOfBirth}
                onChangeText={(text) => setFormData({ ...formData, dateOfBirth: formatDate(text) })}
                placeholder="DD/MM/YYYY"
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
                maxLength={10}
              />
              <Calendar size={20} color={secondaryTextColor} style={styles.inputIcon} />
            </View>
            {formErrors.dateOfBirth ? (
              <Text style={styles.errorText}>{formErrors.dateOfBirth}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>CNIC *</Text>
            <View style={styles.inputWithIcon}>
              <TextInput
                style={[
                  styles.inputWithIconText,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.cnic ? styles.inputError : null
                ]}
                value={formData.cnic}
                onChangeText={(text) => setFormData({ ...formData, cnic: formatCNIC(text) })}
                placeholder="XXXXX-XXXXXXX-X"
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
                maxLength={15}
              />
              <CreditCard size={20} color={secondaryTextColor} style={styles.inputIcon} />
            </View>
            {formErrors.cnic ? (
              <Text style={styles.errorText}>{formErrors.cnic}</Text>
            ) : null}
          </View>

          {/* Contact Information Section */}
          <View style={[styles.sectionHeader, { marginTop: 24 }]}>
            <MapPin size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>Contact Information</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Phone Number</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text, phoneNumber: text })}
              placeholder="Enter phone number"
              placeholderTextColor={secondaryTextColor}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Address</Text>
            <TextInput
              style={[
                styles.textArea,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              placeholder="Enter complete address"
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Profile Image</Text>
            <View style={styles.imagePickerContainer}>
              {formData.photoURL ? (
                <View style={styles.imagePreviewContainer}>
                  <Image source={{ uri: formData.photoURL }} style={styles.imagePreview} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => setFormData({ ...formData, photoURL: "" })}
                  >
                    <Text style={styles.removeImageText}>Remove</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.imagePickerButtons}>
                  <TouchableOpacity
                    style={[styles.imagePickerButton, { borderColor, backgroundColor: cardColor }]}
                    onPress={pickImage}
                  >
                    <ImageIcon size={20} color={primaryColor} />
                    <Text style={[styles.imagePickerButtonText, { color: textColor }]}>Upload Image</Text>
                  </TouchableOpacity>
                  {Platform.OS !== 'web' && (
                    <TouchableOpacity
                      style={[styles.imagePickerButton, { borderColor, backgroundColor: cardColor }]}
                      onPress={takePhoto}
                    >
                      <Camera size={20} color={primaryColor} />
                      <Text style={[styles.imagePickerButtonText, { color: textColor }]}>Take Photo</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Bio</Text>
            <TextInput
              style={[
                styles.textArea,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.bio}
              onChangeText={(text) => setFormData({ ...formData, bio: text })}
              placeholder="Enter bio (optional)"
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {/* Role & Farm Assignment Section */}
          <View style={[styles.sectionHeader, { marginTop: 24 }]}>
            <UserIcon size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>Role & Farm Assignment</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Role *</Text>
            <View style={styles.roleContainer}>
              {availableRoles.map((role) => (
                <TouchableOpacity
                  key={role.value}
                  style={[
                    styles.roleOption,
                    formData.role === role.value && [styles.selectedRoleOption, { backgroundColor: primaryColor, borderColor: primaryColor }],
                    { borderColor, backgroundColor: cardColor },
                    availableRoles.length === 1 && [styles.singleRoleOption, { backgroundColor: primaryColor, borderColor: primaryColor }]
                  ]}
                  onPress={() => setFormData({ ...formData, role: role.value })}
                  disabled={availableRoles.length === 1}
                >
                  <Text
                    style={[
                      styles.roleOptionText,
                      { color: textColor },
                      formData.role === role.value && styles.selectedRoleOptionText
                    ]}
                  >
                    {role.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {currentUser?.role === "admin" && (
              <Text style={[styles.roleNote, { color: secondaryTextColor }]}>
                As an admin, you can only edit caretaker accounts
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>Assign to Farms *</Text>
            {formErrors.farmIds ? (
              <Text style={styles.errorText}>{formErrors.farmIds}</Text>
            ) : null}
            <ScrollView style={[styles.farmsContainer, { borderColor, backgroundColor: cardColor }]} nestedScrollEnabled={true}>
              {farms.length === 0 ? (
                <View style={styles.noFarmsContainer}>
                  <Text style={[styles.noFarmsText, { color: secondaryTextColor }]}>
                    No farms available. Please create a farm first.
                  </Text>
                </View>
              ) : (
                farms.map((farm) => (
                  <TouchableOpacity
                    key={farm.id}
                    style={[
                      styles.farmOption,
                      formData.farmIds.includes(farm.id) && [styles.selectedFarmOption, { backgroundColor: primaryColor, borderColor: primaryColor }],
                      { borderColor }
                    ]}
                    onPress={() => toggleFarmSelection(farm.id)}
                  >
                    <Text
                      style={[
                        styles.farmOptionText,
                        { color: textColor },
                        formData.farmIds.includes(farm.id) && styles.selectedFarmOptionText
                      ]}
                    >
                      {farm.name || 'Unnamed Farm'}
                    </Text>
                    <Text
                      style={[
                        styles.farmLocationText,
                        { color: secondaryTextColor },
                        formData.farmIds.includes(farm.id) && styles.selectedFarmLocationText
                      ]}
                    >
                      {getLocationDisplay(farm)}
                    </Text>
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </ScrollView>

      <View style={[styles.footer, { backgroundColor: cardColor, borderTopColor: borderColor }]}>
        <TouchableOpacity
          style={[styles.button, styles.cancelButton, { backgroundColor: isDarkMode ? "#424242" : "#757575" }]}
          onPress={handleGoBack}
        >
          <Text style={styles.buttonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.saveButton, { backgroundColor: primaryColor }]}
          onPress={handleSubmit}
          disabled={submitting || farms.length === 0}
        >
          {submitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.buttonText}>Save Changes</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  headerButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 8,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "600",
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputWithIcon: {
    position: "relative",
  },
  inputWithIconText: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingRight: 50,
    fontSize: 16,
  },
  inputIcon: {
    position: "absolute",
    right: 16,
    top: 14,
  },
  textArea: {
    height: 80,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#D32F2F",
  },
  errorText: {
    color: "#D32F2F",
    fontSize: 12,
    marginTop: 4,
  },
  genderContainer: {
    flexDirection: "row",
    gap: 12,
  },
  genderOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
  },
  selectedGenderOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  genderOptionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  selectedGenderOptionText: {
    color: "#FFFFFF",
  },
  roleContainer: {
    flexDirection: "row",
    gap: 12,
  },
  roleOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
  },
  singleRoleOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  selectedRoleOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  roleOptionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  selectedRoleOptionText: {
    color: "#FFFFFF",
  },
  roleNote: {
    fontSize: 12,
    marginTop: 8,
    fontStyle: "italic",
  },
  farmsContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    maxHeight: 200,
  },
  noFarmsContainer: {
    padding: 20,
    alignItems: "center",
  },
  noFarmsText: {
    fontSize: 14,
    textAlign: "center",
  },
  farmOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  selectedFarmOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  farmOptionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  selectedFarmOptionText: {
    color: "#FFFFFF",
  },
  farmLocationText: {
    fontSize: 12,
    marginTop: 2,
  },
  selectedFarmLocationText: {
    color: "#FFFFFF",
    opacity: 0.8,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    borderTopWidth: 1,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 6,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#757575",
  },
  saveButton: {
    // backgroundColor is set dynamically based on role
  },
  imagePickerContainer: {
    marginBottom: 16,
  },
  imagePickerButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  imagePickerButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 8,
  },
  imagePickerButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  imagePreviewContainer: {
    position: "relative",
    alignItems: "center",
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 8,
  },
  removeImageButton: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#D32F2F",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  removeImageText: {
    color: "#fff",
    fontSize: 12,
  },
  camera: {
    flex: 1,
  },
  cameraButtonContainer: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "transparent",
    margin: 64,
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  cameraButton: {
    flex: 0.4,
    alignSelf: "flex-end",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 16,
    borderRadius: 12,
  },
  captureButton: {
    // backgroundColor is set dynamically based on role in the component
  },
  cameraButtonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "white",
  },
});