import React, { useState, useEffect, useCallback } from "react";
import { 
  StyleSheet, 
  Text, 
  View, 
  FlatList, 
  TouchableOpacity, 
  RefreshControl, 
  Modal, 
  Alert, 
  ActivityIndicator,
  Animated,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { CheckCircle, XCircle, AlertTriangle, Clock, Check, X, Package, Calendar, User, Building } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { formatDistanceToNow } from "date-fns";
import { useAuth } from "@/context/auth-context";
import { getInventoryRequests, approveRequest, rejectRequest, Request } from "@/services/request-service";
import { LinearGradient } from "expo-linear-gradient";
import { Stack } from "expo-router";

export default function OwnerApprovalsScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [requests, setRequests] = useState<Request[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<Request | null>(null);
  const [approving, setApproving] = useState(false);
  const [rejecting, setRejecting] = useState(false);

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const primaryColor = isDarkMode ? "#90caf9" : "#1976D2";

  const loadRequests = useCallback(async () => {
    try {
      setLoading(true);
      // Get inventory requests from managers (admins)
      // Filter by requestType and requestorRole
      const inventoryRequests = await getInventoryRequests();
      
      // Filter only pending requests from admins
      const pendingRequests = inventoryRequests.filter(req => 
        req.status === "pending" && 
        req.requestorRole === "admin" &&
        req.requestType === "inventory"
      );
      
      setRequests(pendingRequests);
    } catch (error) {
      console.error("Error loading approval requests:", error);
      Alert.alert("Error", "Failed to load approval requests");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadRequests();
  }, [loadRequests]);

  const onRefresh = () => {
    setRefreshing(true);
    loadRequests();
  };

  const handleViewRequest = (request: Request) => {
    setSelectedRequest(request);
    setModalVisible(true);
  };

  const handleApprove = async () => {
    if (!selectedRequest || !user) return;
    
    setApproving(true);
    try {
      await approveRequest(
        selectedRequest.id, 
        user.uid, 
        user.displayName || "",
        selectedRequest.farmId
      );
      setModalVisible(false);
      Alert.alert("Success", "Request approved successfully");
      loadRequests();
    } catch (error) {
      console.error("Error approving request:", error);
      Alert.alert("Error", "Failed to approve request");
    } finally {
      setApproving(false);
    }
  };

  const handleReject = async () => {
    if (!selectedRequest || !user) return;
    
    setRejecting(true);
    try {
      await rejectRequest(
        selectedRequest.id, 
        user.uid, 
        user.displayName || "",
        "Request rejected by owner",
        selectedRequest.farmId
      );
      setModalVisible(false);
      Alert.alert("Success", "Request rejected successfully");
      loadRequests();
    } catch (error) {
      console.error("Error rejecting request:", error);
      Alert.alert("Error", "Failed to reject request");
    } finally {
      setRejecting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle size={20} color="#2E7D32" />;
      case "rejected":
        return <XCircle size={20} color="#D32F2F" />;
      case "pending":
        return <Clock size={20} color="#FF9800" />;
      default:
        return <AlertTriangle size={20} color="#757575" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "#2E7D32";
      case "rejected":
        return "#D32F2F";
      case "pending":
        return "#FF9800";
      default:
        return "#757575";
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return "Invalid date";
    }
  };

  const renderRequestItem = ({ item, index }: { item: Request, index: number }) => {
    // Animation values
    const fadeAnim = useState(new Animated.Value(0))[0];
    const translateYAnim = useState(new Animated.Value(50))[0];
    
    useEffect(() => {
      // Start animation with delay based on index
      const delay = index * 100;
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          delay,
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 500,
          delay,
          useNativeDriver: true,
        }),
      ]).start();
    }, []);
    
    return (
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ translateY: translateYAnim }],
        }}
      >
        <TouchableOpacity 
          style={styles.requestItemContainer}
          onPress={() => handleViewRequest(item)}
          activeOpacity={0.7}
        >
          <LinearGradient
            colors={isDarkMode ? ["#4F3200", "#2C1C00"] : ["#FFF8E1", "#FFECB3"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={[styles.requestCard, { borderColor }]}
          >
            <View style={styles.requestHeader}>
              <View style={styles.requestInfo}>
                <Text style={[styles.requestTitle, { color: textColor }]}>
                  {item.itemName}
                </Text>
                <View style={[styles.statusBadge, { backgroundColor: "#FF9800" + "20" }]}>
                  <Clock size={16} color="#FF9800" />
                  <Text style={[styles.statusText, { color: "#FF9800" }]}>
                    Pending
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.requestDetails}>
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Package size={16} color={isDarkMode ? "#90caf9" : "#1976D2"} />
                </View>
                <Text style={[styles.detailText, { color: secondaryTextColor }]}>
                  Quantity: <Text style={[styles.detailValue, { color: textColor }]}>{item.quantity} {item.unit}</Text>
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Building size={16} color={isDarkMode ? "#90caf9" : "#1976D2"} />
                </View>
                <Text style={[styles.detailText, { color: secondaryTextColor }]}>
                  Farm: <Text style={[styles.detailValue, { color: textColor }]}>{item.farmName}</Text>
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <User size={16} color={isDarkMode ? "#90caf9" : "#1976D2"} />
                </View>
                <Text style={[styles.detailText, { color: secondaryTextColor }]}>
                  Requested by: <Text style={[styles.detailValue, { color: textColor }]}>{item.requestedByName}</Text>
                </Text>
              </View>
            </View>
            
            <View style={[styles.requestFooter, { borderTopColor: "rgba(0,0,0,0.05)" }]}>
              <Text style={[styles.requestTime, { color: secondaryTextColor }]}>
                {formatDate(item.createdAt)}
              </Text>
              
              <View style={styles.actionButtons}>
                <TouchableOpacity 
                  style={[styles.actionButton, styles.rejectButton]}
                  onPress={() => {
                    setSelectedRequest(item);
                    handleReject();
                  }}
                  activeOpacity={0.8}
                >
                  <XCircle size={16} color="#fff" />
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.actionButton, styles.approveButton]}
                  onPress={() => {
                    setSelectedRequest(item);
                    handleApprove();
                  }}
                  activeOpacity={0.8}
                >
                  <CheckCircle size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={['top']}>
      <Stack.Screen options={{ 
        title: "Inventory Approvals",
        headerStyle: {
          backgroundColor,
        },
        headerTintColor: textColor,
      }} />

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      ) : (
        <FlatList
          data={requests}
          renderItem={renderRequestItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
                No pending inventory requests found
              </Text>
            </View>
          }
        />
      )}

      {/* Request Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: cardColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                Inventory Request
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <X size={24} color={textColor} />
              </TouchableOpacity>
            </View>

            {selectedRequest && (
              <View style={styles.modalBody}>
                <View style={[styles.detailCard, { borderColor }]}>
                  <Text style={[styles.detailCardTitle, { color: textColor }]}>
                    {selectedRequest.itemName}
                  </Text>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Status:</Text>
                    <View style={styles.statusContainer}>
                      {getStatusIcon(selectedRequest.status)}
                      <Text style={[styles.statusText, { color: getStatusColor(selectedRequest.status) }]}>
                        {selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Quantity:</Text>
                    <Text style={[styles.detailValue, { color: textColor }]}>
                      {selectedRequest.quantity} {selectedRequest.unit}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Farm:</Text>
                    <Text style={[styles.detailValue, { color: textColor }]}>
                      {selectedRequest.farmName}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Requested By:</Text>
                    <Text style={[styles.detailValue, { color: textColor }]}>
                      {selectedRequest.requestedByName}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Request Date:</Text>
                    <Text style={[styles.detailValue, { color: textColor }]}>
                      {new Date(selectedRequest.createdAt).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  {selectedRequest.reason && (
                    <View style={styles.reasonContainer}>
                      <Text style={[styles.reasonLabel, { color: secondaryTextColor }]}>Reason:</Text>
                      <Text style={[styles.reasonText, { color: textColor }]}>
                        {selectedRequest.reason}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )}

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.button, styles.rejectButton]}
                onPress={handleReject}
                disabled={rejecting}
                activeOpacity={0.8}
              >
                {rejecting ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <XCircle size={20} color="#fff" />
                    <Text style={styles.buttonText}>Reject</Text>
                  </>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.approveButton]}
                onPress={handleApprove}
                disabled={approving}
                activeOpacity={0.8}
              >
                {approving ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <CheckCircle size={20} color="#fff" />
                    <Text style={styles.buttonText}>Approve</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  listContent: {
    padding: 16,
  },
  requestItemContainer: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  requestCard: {
    padding: 16,
    borderWidth: 1,
  },
  requestHeader: {
    marginBottom: 12,
  },
  requestInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  requestTitle: {
    fontSize: 18,
    fontWeight: "700",
    flex: 1,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 4,
  },
  requestDetails: {
    marginBottom: 12,
    gap: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailIconContainer: {
    width: 24,
    alignItems: "center",
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
  },
  detailValue: {
    fontWeight: "500",
  },
  detailLabel: {
    fontSize: 14,
    width: 100,
  },
  requestFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    paddingTop: 12,
    marginTop: 4,
  },
  requestedBy: {
    fontSize: 12,
  },
  requestTime: {
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
  },
  approveButton: {
    backgroundColor: "#4CAF50",
  },
  rejectButton: {
    backgroundColor: "#D32F2F",
  },
  emptyContainer: {
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "90%",
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  modalBody: {
    marginBottom: 20,
  },
  detailCard: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },
  detailCardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  reasonContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: "rgba(0,0,0,0.03)",
    borderRadius: 8,
  },
  reasonLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  reasonText: {
    fontSize: 16,
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});