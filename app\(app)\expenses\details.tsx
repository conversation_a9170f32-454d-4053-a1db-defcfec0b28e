import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput,
  FlatList,
} from "react-native";
import { useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import {
  getExpensesByFarm,
  getUserExpenses,
  Expense,
  ExpenseFilter
} from "@/services/expense-service";
import {
  DollarSign,
  Search,
  ArrowLeft,
  Users,
  Building,
  Package,
  Truck,
  MoreHorizontal,
  X,
} from "lucide-react-native";
import { FilterButton } from "@/components/FilterButton";

export default function ExpenseDetailsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors, theme } = useTheme();
  const { t } = useLanguage();

  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'farm' | 'user'>('all');
  const [activeTab, setActiveTab] = useState<'inventory' | 'machinery'>('inventory');

  const filterOptions = [
    { key: 'all', label: t('common.all'), icon: MoreHorizontal },
    { key: 'farm', label: t('expenses.farmExpenses'), icon: Building },
    { key: 'user', label: t('expenses.userExpenses'), icon: Users },
  ];

  const tabOptions = [
    { key: 'inventory', label: t('inventory.title'), icon: Package },
    { key: 'machinery', label: t('machinery.title'), icon: Truck },
  ];

  useEffect(() => {
    if (selectedFarm && user) {
      loadAllExpenses();
    }
  }, [selectedFarm, user]);

  useEffect(() => {
    filterExpenses();
  }, [expenses, searchQuery, activeFilter, activeTab]);

  const loadAllExpenses = async () => {
    if (!selectedFarm || !user) return;

    try {
      setLoading(true);
      let result;

      if (user.role === 'caretaker') {
        // Caretakers only see their own expenses
        result = await getUserExpenses(selectedFarm.id, user.uid, 500);
      } else {
        // Owners and admins see all expenses
        result = await getExpensesByFarm(selectedFarm.id, {}, 500);
      }

      setExpenses(result.expenses);
    } catch (error) {
      console.error('Error loading expenses:', error);
      Alert.alert(t('common.error'), t('expenses.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const filterExpenses = () => {
    let filtered = [...expenses];

    // Filter by tab (category)
    if (activeTab === 'inventory') {
      filtered = filtered.filter(expense => expense.category === 'inventory');
    } else if (activeTab === 'machinery') {
      filtered = filtered.filter(expense =>
        expense.category === 'machinery' ||
        expense.category === 'fuel' ||
        expense.category === 'maintenance'
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(expense =>
        expense.description.toLowerCase().includes(query) ||
        expense.category.toLowerCase().includes(query) ||
        (expense.userName && expense.userName.toLowerCase().includes(query)) ||
        (expense.relatedItemName && expense.relatedItemName.toLowerCase().includes(query)) ||
        expense.amount.toString().includes(query)
      );
    }

    // Filter by type
    if (activeFilter !== 'all') {
      filtered = filtered.filter(expense => expense.type === activeFilter);
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    setFilteredExpenses(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAllExpenses();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    const isNegative = amount < 0;
    const absAmount = Math.abs(amount);
    return `${isNegative ? '-' : ''}Rs ${absAmount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'inventory': return '📦';
      case 'machinery': return '🚜';
      case 'fuel': return '⛽';
      case 'maintenance': return '🔧';
      case 'labor': return '👷';
      case 'utilities': return '💡';
      case 'transport': return '🚛';
      default: return '💰';
    }
  };

  const getExpenseTypeColor = (type: string) => {
    return type === 'farm' ? colors.primary : colors.secondary;
  };

  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('expenses.allExpenses'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.centerContainer}>
          <Text style={[styles.message, { color: colors.text }]}>
            {t('farm.selectFarmFirst')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('expenses.allExpenses'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
          ),
        }}
      />

      <View style={styles.content}>
        {/* Top Tabs */}
        <View style={[styles.tabsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          {tabOptions.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <TouchableOpacity
                key={tab.key}
                style={[
                  styles.tabButton,
                  activeTab === tab.key && [styles.activeTabButton, { borderColor: colors.primary }]
                ]}
                onPress={() => setActiveTab(tab.key as 'inventory' | 'machinery')}
              >
                <IconComponent
                  size={16}
                  color={activeTab === tab.key ? colors.primary : colors.textSecondary}
                />
                <Text
                  style={[
                    styles.tabText,
                    {
                      color: activeTab === tab.key ? colors.primary : colors.textSecondary,
                    }
                  ]}
                >
                  {tab.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder={t('expenses.searchExpenses')}
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <X size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Filter Section */}
        <View style={styles.filterContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterScroll}
          >
            {filterOptions.map((option) => (
              <FilterButton
                key={option.key}
                label={option.label}
                active={activeFilter === option.key}
                onPress={() => setActiveFilter(option.key as 'all' | 'farm' | 'user')}
                activeColor={colors.primary}
                isDarkMode={theme === 'dark'}
              />
            ))}
          </ScrollView>
        </View>

        {/* Results Count */}
        <View style={styles.resultsHeader}>
          <Text style={[styles.resultsCount, { color: colors.textSecondary }]}>
            {filteredExpenses.length} {t('expenses.expensesFound')}
          </Text>
        </View>

        {/* Expenses List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : filteredExpenses.length === 0 ? (
          <View style={styles.emptyContainer}>
            <DollarSign size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              {searchQuery || activeFilter !== 'all'
                ? t('expenses.noMatchingExpenses')
                : t('expenses.noExpenses')
              }
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredExpenses}
            keyExtractor={(item) => item.id}
            style={styles.expensesList}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            showsVerticalScrollIndicator={false}
            renderItem={({ item: expense }) => (
              <TouchableOpacity
                style={[styles.expenseItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
                onPress={() => {
                  console.log('🔍 Expense card clicked in All Expenses:', {
                    expenseId: expense.id,
                    requestId: expense.requestId,
                    farmId: expense.farmId,
                    description: expense.description,
                    type: expense.type,
                    category: expense.category,
                    hasRequestId: !!expense.requestId
                  });

                  if (expense.requestId) {
                    // Navigate to the original request details
                    const navigationPath = `/request/${expense.requestId}?farmId=${expense.farmId}`;
                    console.log('✅ Navigating to request details:', navigationPath);
                    router.push(navigationPath);
                  } else {
                    // Fallback to expense details if no request ID
                    console.log('❌ No requestId found, this expense was not created from an approved request');
                    console.log('💡 This might be a farm expense or manually created expense');
                    // For now, just show an alert instead of navigating to expense details
                    alert(`This expense (${expense.description}) was not created from a request and doesn't have an associated request to view.`);
                  }
                }}
              >
                <View style={styles.expenseLeft}>
                  <View style={[styles.categoryIcon, { backgroundColor: getExpenseTypeColor(expense.type) + '20' }]}>
                    <Text style={styles.categoryEmoji}>{getCategoryIcon(expense.category)}</Text>
                  </View>
                  <View style={styles.expenseInfo}>
                    <Text style={[styles.expenseDescription, { color: colors.text }]}>
                      {expense.description}
                    </Text>
                    <Text style={[styles.expenseDetails, { color: colors.textSecondary }]}>
                      {t(`expenses.categories.${expense.category}`)} • {formatDate(expense.date)}
                      {expense.userName && ` • ${expense.userName}`}
                    </Text>
                    {expense.relatedItemName && (
                      <Text style={[styles.relatedItem, { color: colors.textSecondary }]}>
                        📋 {expense.relatedItemName}
                      </Text>
                    )}
                    {expense.requestId && (
                      <Text style={[styles.requestLink, { color: colors.primary }]}>
                        📄 {t('expenses.viewRequest')}
                      </Text>
                    )}
                  </View>
                </View>
                <View style={styles.expenseRight}>
                  <Text style={[
                    styles.expenseAmount,
                    { color: expense.amount < 0 ? colors.error : expense.amount === 0 ? colors.warning : colors.text }
                  ]}>
                    {expense.amount === 0 ? 'Rs 0 (No Price)' : formatCurrency(expense.amount)}
                  </Text>
                  <View style={[styles.typeTag, { backgroundColor: getExpenseTypeColor(expense.type) + '20' }]}>
                    <Text style={[styles.typeTagText, { color: getExpenseTypeColor(expense.type) }]}>
                      {expense.amount < 0 ? t('expenses.adjustment') :
                       expense.amount === 0 ? t('expenses.tracking') :
                       t(`expenses.${expense.type}Expense`)}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },

  // Top Tabs
  tabsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    gap: 6,
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },

  // Search
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  // Filters
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  filterScroll: {
    paddingRight: 16,
    gap: 8,
  },

  // Results
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  resultsCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Expenses List
  expensesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  expenseItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  expenseLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryEmoji: {
    fontSize: 18,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  expenseDetails: {
    fontSize: 14,
    marginBottom: 2,
  },
  relatedItem: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  requestLink: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  expenseRight: {
    alignItems: 'flex-end',
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  typeTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  typeTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
