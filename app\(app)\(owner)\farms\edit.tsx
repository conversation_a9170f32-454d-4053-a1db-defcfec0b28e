import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { getFarmById, updateFarm } from "@/services/farm-service";
import { getUsers, User } from "@/services/user-service";
import { MapPin, Camera, X, Save, Check, ArrowLeft } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import * as ImagePicker from 'expo-image-picker';
import { uploadFarmImage, deleteImageFromStorage } from "@/services/storage-service";

export default function EditFarmScreen() {
  const { farmId } = useLocalSearchParams<{ farmId: string }>();
  const { theme, colors } = useTheme();
  const { t } = useLanguage();
  const { refreshFarms } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [caretakers, setCaretakers] = useState<User[]>([]);
  const [selectedCaretakers, setSelectedCaretakers] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [loadingCaretakers, setLoadingCaretakers] = useState(true);
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: "",
    latitude: "",
    longitude: "",
    size: "",
    sizeUnit: "acres",
    type: "mixed",
    photoURL: "",
  });
  
  const [formErrors, setFormErrors] = useState({
    name: "",
    address: "",
    size: "",
  });

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  const FARM_TYPES = [
    { value: "crop", label: t('farm.cropFarm') },
    { value: "livestock", label: t('farm.livestockFarm') },
    { value: "mixed", label: t('farm.mixedFarm') },
    { value: "dairy", label: t('farm.dairyFarm') },
    { value: "poultry", label: t('farm.poultyFarm') },
    { value: "organic", label: t('farm.organicFarm') },
    { value: "greenhouse", label: t('common.greenhouse') },
    { value: "orchard", label: t('common.orchard') },
  ];

  const SIZE_UNITS = [
    { value: "acres", label: t('farm.acres') },
    { value: "hectares", label: t('farm.hectares') },
    { value: "square_meters", label: t('farm.squareMeters') },
    { value: "square_feet", label: t('farm.squareFeet') },
  ];

  useEffect(() => {
    if (farmId) {
      loadFarmData();
      loadCaretakers();
    }
  }, [farmId]);

  const loadFarmData = async () => {
    try {
      const farm = await getFarmById(farmId);
      if (farm) {
        // Extract location data
        let address = "";
        let latitude = "";
        let longitude = "";
        
        if (farm.location) {
          if (typeof farm.location === 'string') {
            address = farm.location;
          } else if (typeof farm.location === 'object') {
            address = farm.location.address || "";
            latitude = farm.location.latitude?.toString() || "";
            longitude = farm.location.longitude?.toString() || "";
          }
        }
        
        setFormData({
          name: farm.name || "",
          description: farm.description || "",
          address,
          latitude,
          longitude,
          size: farm.size?.toString() || "",
          sizeUnit: farm.sizeUnit || "acres",
          type: farm.type || "mixed",
          photoURL: farm.photoURL || "",
        });
        
        setSelectedCaretakers(farm.caretakerIds || []);
      }
    } catch (error) {
      console.error("Error loading farm data:", error);
      Alert.alert(t('common.error'), t('farm.loadError'));
    } finally {
      setLoadingData(false);
    }
  };

  const loadCaretakers = async () => {
    try {
      const allUsers = await getUsers();
      const caretakerUsers = allUsers.filter(u => u.role === "caretaker");
      setCaretakers(caretakerUsers);
    } catch (error) {
      console.error("Error loading caretakers:", error);
    } finally {
      setLoadingCaretakers(false);
    }
  };

  const validateForm = () => {
    let valid = true;
    const errors = {
      name: "",
      address: "",
      size: "",
    };

    if (!formData.name.trim()) {
      errors.name = t('farm.farmNameRequired');
      valid = false;
    }

    if (!formData.address.trim()) {
      errors.address = t('farm.farmAddressRequired');
      valid = false;
    }

    if (!formData.size.trim()) {
      errors.size = t('farm.farmSizeRequired');
      valid = false;
    } else if (isNaN(Number(formData.size)) || Number(formData.size) <= 0) {
      errors.size = t('farm.invalidFarmSize');
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  const handleImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setFormData({ ...formData, photoURL: result.assets[0].uri });
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(t('common.error'), t('inventory.imagePickError'));
    }
  };

  const toggleCaretaker = (caretakerId: string) => {
    setSelectedCaretakers(prev => 
      prev.includes(caretakerId)
        ? prev.filter(id => id !== caretakerId)
        : [...prev, caretakerId]
    );
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let photoURL = formData.photoURL;

      // Upload image to Firebase Storage if a new local image is selected
      if (formData.photoURL && formData.photoURL.startsWith('file://')) {
        console.log("Uploading new farm image to Storage...");

        // Delete old image if it exists and is from Firebase Storage
        if (originalFarm?.photoURL) {
          await deleteImageFromStorage(originalFarm.photoURL);
        }

        // Upload new image
        photoURL = await uploadFarmImage(formData.photoURL, farmId);
        console.log("Farm image uploaded:", photoURL);
      }

      const updateData = {
        name: formData.name,
        description: formData.description,
        location: {
          address: formData.address,
          latitude: formData.latitude ? parseFloat(formData.latitude) : undefined,
          longitude: formData.longitude ? parseFloat(formData.longitude) : undefined,
        },
        size: parseFloat(formData.size),
        sizeUnit: formData.sizeUnit,
        type: formData.type,
        caretakerIds: selectedCaretakers,
        photoURL: photoURL,
      };

      await updateFarm(farmId, updateData, user?.role);
      Alert.alert(t('common.success'), t('farm.farmUpdated'), [
        { text: t('common.ok'), onPress: () => {
          refreshFarms();
          router.back();
        }}
      ]);
    } catch (error) {
      console.error("Error updating farm:", error);
      Alert.alert(t('common.error'), t('farm.updateError'));
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('farm.editFarm'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
            headerLeft: () => (
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
            {t('farm.loadingFarmDetails')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('farm.editFarm'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerTitleStyle: { fontWeight: "bold" },
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Basic Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.basicInformation')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmName')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor },
                formErrors.name ? styles.inputError : null
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder={t('farm.enterFarmName')}
              placeholderTextColor={secondaryTextColor}
            />
            {formErrors.name ? (
              <Text style={styles.errorText}>{formErrors.name}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmDescription')}</Text>
            <TextInput
              style={[styles.textArea, { color: textColor, borderColor }]}
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              placeholder={t('farm.enterFarmDescription')}
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmType')}</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
              {FARM_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.typeOption,
                    formData.type === type.value && [styles.selectedType, { backgroundColor: colors.primary }]
                  ]}
                  onPress={() => setFormData({ ...formData, type: type.value })}
                >
                  <Text style={[
                    styles.typeText,
                    { color: formData.type === type.value ? "#fff" : textColor }
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>

        {/* Location Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.location')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmAddress')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor },
                formErrors.address ? styles.inputError : null
              ]}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              placeholder={t('farm.enterFarmAddress')}
              placeholderTextColor={secondaryTextColor}
            />
            {formErrors.address ? (
              <Text style={styles.errorText}>{formErrors.address}</Text>
            ) : null}
          </View>

          <View style={styles.row}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.latitude')}</Text>
              <TextInput
                style={[styles.input, { color: textColor, borderColor }]}
                value={formData.latitude}
                onChangeText={(text) => setFormData({ ...formData, latitude: text })}
                placeholder={t('farm.enterLatitude')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.longitude')}</Text>
              <TextInput
                style={[styles.input, { color: textColor, borderColor }]}
                value={formData.longitude}
                onChangeText={(text) => setFormData({ ...formData, longitude: text })}
                placeholder={t('farm.enterLongitude')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Size Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.size')}</Text>
          
          <View style={styles.row}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('common.size')} *</Text>
              <TextInput
                style={[
                  styles.input,
                  { color: textColor, borderColor },
                  formErrors.size ? styles.inputError : null
                ]}
                value={formData.size}
                onChangeText={(text) => setFormData({ ...formData, size: text })}
                placeholder={t('farm.enterFarmSize')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
              {formErrors.size ? (
                <Text style={styles.errorText}>{formErrors.size}</Text>
              ) : null}
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.sizeUnit')}</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.unitSelector}>
                {SIZE_UNITS.map((unit) => (
                  <TouchableOpacity
                    key={unit.value}
                    style={[
                      styles.unitOption,
                      formData.sizeUnit === unit.value && [styles.selectedUnit, { backgroundColor: colors.primary }]
                    ]}
                    onPress={() => setFormData({ ...formData, sizeUnit: unit.value })}
                  >
                    <Text style={[
                      styles.unitText,
                      { color: formData.sizeUnit === unit.value ? "#fff" : textColor }
                    ]}>
                      {unit.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </View>

        {/* Photo */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.farmImage')}</Text>
          
          <TouchableOpacity style={[styles.photoUpload, { borderColor }]} onPress={handleImagePicker}>
            {formData.photoURL ? (
              <View style={styles.photoContainer}>
                <Image source={{ uri: formData.photoURL }} style={styles.farmPhoto} />
                <TouchableOpacity 
                  style={styles.removePhoto}
                  onPress={() => setFormData({ ...formData, photoURL: "" })}
                >
                  <X size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.photoPlaceholder}>
                <Camera size={32} color={secondaryTextColor} />
                <Text style={[styles.photoText, { color: secondaryTextColor }]}>
                  {t('farm.addFarmImage')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Caretakers */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.assignCaretakers')}</Text>
          
          {loadingCaretakers ? (
            <ActivityIndicator size="small" color={colors.primary} style={styles.loadingCaretakers} />
          ) : caretakers.length > 0 ? (
            <View style={styles.caretakersList}>
              {caretakers.map((caretaker) => (
                <TouchableOpacity
                  key={caretaker.uid}
                  style={[
                    styles.caretakerItem,
                    { borderColor },
                    selectedCaretakers.includes(caretaker.uid) && [styles.selectedCaretaker, { borderColor: colors.primary }]
                  ]}
                  onPress={() => toggleCaretaker(caretaker.uid)}
                >
                  <View style={styles.caretakerInfo}>
                    <View style={[styles.caretakerAvatar, { backgroundColor: colors.primary + "20" }]}>
                      <Text style={[styles.caretakerInitials, { color: colors.primary }]}>
                        {caretaker.displayName?.substring(0, 2).toUpperCase() || caretaker.name?.substring(0, 2).toUpperCase() || "??"}
                      </Text>
                    </View>
                    <View style={styles.caretakerDetails}>
                      <Text style={[styles.caretakerName, { color: textColor }]}>
                        {caretaker.displayName || caretaker.name}
                      </Text>
                      <Text style={[styles.caretakerEmail, { color: secondaryTextColor }]}>
                        {caretaker.email}
                      </Text>
                    </View>
                  </View>
                  {selectedCaretakers.includes(caretaker.uid) && (
                    <Check size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <Text style={[styles.noCaretakers, { color: secondaryTextColor }]}>
              {t('farm.noCaretakersAvailable')}
            </Text>
          )}
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={[styles.footer, { backgroundColor: cardColor, borderColor }]}>
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: colors.primary }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Save size={20} color="#fff" />
              <Text style={styles.submitButtonText}>{t('farm.updateFarm')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#D32F2F",
  },
  errorText: {
    color: "#D32F2F",
    fontSize: 12,
    marginTop: 4,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  typeSelector: {
    marginTop: 8,
  },
  typeOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedType: {
    borderColor: "transparent",
  },
  typeText: {
    fontSize: 14,
    fontWeight: "500",
  },
  unitSelector: {
    marginTop: 8,
  },
  unitOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedUnit: {
    borderColor: "transparent",
  },
  unitText: {
    fontSize: 12,
    fontWeight: "500",
  },
  photoUpload: {
    height: 120,
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
  },
  photoContainer: {
    position: "relative",
    width: "100%",
    height: "100%",
  },
  farmPhoto: {
    width: "100%",
    height: "100%",
    borderRadius: 6,
  },
  removePhoto: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  photoPlaceholder: {
    alignItems: "center",
  },
  photoText: {
    fontSize: 14,
    marginTop: 8,
  },
  loadingCaretakers: {
    marginVertical: 20,
  },
  caretakersList: {
    gap: 8,
  },
  caretakerItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectedCaretaker: {
    backgroundColor: "rgba(46, 125, 50, 0.1)",
  },
  caretakerInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  caretakerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  caretakerInitials: {
    fontSize: 16,
    fontWeight: "bold",
  },
  caretakerDetails: {
    flex: 1,
  },
  caretakerName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  caretakerEmail: {
    fontSize: 14,
  },
  noCaretakers: {
    textAlign: "center",
    fontSize: 14,
    fontStyle: "italic",
    marginVertical: 20,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  submitButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});