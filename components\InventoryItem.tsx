import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Image } from "react-native";
import { ChevronRight, AlertTriangle, Clock, CheckCircle, MapPin, MoreVertical, Edit, Trash2, Calendar } from "lucide-react-native";
import { InventoryItem } from "@/services/inventory-service";
import { Menu, MenuOptions, MenuOption, MenuTrigger } from "react-native-popup-menu";
import { useLanguage } from "@/context/language-context";
import { LinearGradient } from "expo-linear-gradient";

interface InventoryItemProps {
  item: InventoryItem;
  onPress: () => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  showEditOptions?: boolean;
  isDarkMode?: boolean;
  role?: "owner" | "admin" | "caretaker";
  farmName?: string;
}

export const InventoryItemComponent = ({
  item,
  onPress,
  onEditPress,
  onDeletePress,
  showEditOptions = false,
  isDarkMode = false,
  role = "caretaker",
  farmName,
}: InventoryItemProps) => {
  const { isRTL, t } = useLanguage();

  const itemName = item?.name || t("inventory.unnamedItem");
  const itemCategory = item?.category || t("inventory.other");
  const itemQuantity = item?.quantity || 0;
  const itemUnit = item?.unit || "pcs";
  const itemMinQuantity = item?.minQuantity || 0;
  const itemLocation = item?.location || "";
  const itemExpiryDate = item?.expiryDate;
  const itemImageUrl = item?.imageUrl;

  const isLowStock = itemQuantity <= itemMinQuantity;
  const isExpired = itemExpiryDate ? new Date(itemExpiryDate) < new Date() : false;
  const isExpiringSoon = itemExpiryDate
    ? (() => {
        const today = new Date();
        const expiryDate = new Date(itemExpiryDate);
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);
        return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
      })()
    : false;

  const getStatusInfo = () => {
    if (isExpired) {
      return {
        color: "#FF4444",
        bgColor: "#FFE6E6",
        icon: <AlertTriangle size={18} color="#FF4444" />,
        text: t("inventory.status.expired"),
        textColor: "#FF4444",
      };
    }
    if (isLowStock) {
      return {
        color: "#FFA726",
        bgColor: "#FFF3E0",
        icon: <AlertTriangle size={18} color="#FFA726" />,
        text: t("inventory.status.lowStock"),
        textColor: "#FFA726",
      };
    }
    if (isExpiringSoon) {
      return {
        color: "#FFD600",
        bgColor: "#FFF9C4",
        icon: <Clock size={18} color="#FFD600" />,
        text: t("inventory.status.expiringSoon"),
        textColor: "#FFD600",
      };
    }
    return {
      color: "#4CAF50",
      bgColor: "#E8F5E9",
      icon: <CheckCircle size={18} color="#4CAF50" />,
      text: t("inventory.status.inStock"),
      textColor: "#4CAF50",
    };
  };

  const getCategoryIcon = (category: string) => {
    const lower = category.toLowerCase();
    if (lower.includes("seed")) return "🌱";
    if (lower.includes("fertilizer")) return "🧪";
    if (lower.includes("tool")) return "🛠️";
    if (lower.includes("pesticide")) return "☣️";
    if (lower.includes("equipment")) return "⚙️";
    if (lower.includes("feed")) return "🌾";
    if (lower.includes("medication")) return "💊";
    if (lower.includes("vaccination")) return "💉";
    if (lower.includes("fuel")) return "⛽";
    return "📦";
  };

  // Function to translate category names
  const getTranslatedCategory = (category: string) => {
    if (!category) return t("inventory.other");

    const lower = category.toLowerCase();
    if (lower === "seeds" || lower.includes("seed")) return t("inventory.category_seeds");
    if (lower === "fertilizers" || lower.includes("fertilizer")) return t("inventory.category_fertilizers");
    if (lower === "pesticides" || lower.includes("pesticide")) return t("inventory.category_pesticides");
    if (lower === "tools" || lower.includes("tool")) return t("inventory.category_tools");
    if (lower === "equipment" || lower.includes("equipment")) return t("inventory.category_equipment");
    if (lower === "feed" || lower.includes("feed")) return t("inventory.category_feed");
    if (lower === "medication" || lower.includes("medication")) return t("inventory.category_medication");
    if (lower === "vaccination" || lower.includes("vaccination")) return t("inventory.category_vaccination");
    if (lower === "fuel" || lower.includes("fuel")) return t("inventory.category_fuel");
    if (lower === "other (custom)" || lower.includes("other")) return t("inventory.category_other_custom");

    // If no match found, return the original category
    return category;
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return null;
    try {
      const date = new Date(dateStr);
      const now = new Date();
      const diff = date.getTime() - now.getTime();
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
      if (days < 0) return t("common.daysAgo", { count: Math.abs(days) });
      if (days === 0) return t("common.today");
      if (days === 1) return t("common.tomorrow");
      if (days <= 7) return t("common.daysRemaining", { count: days });
      return date.toLocaleDateString();
    } catch {
      return null;
    }
  };

  const textColor = isDarkMode ? "#f1f1f1" : "#1a1a1a";
  const subTextColor = isDarkMode ? "#aaaaaa" : "#6b7280";
  const cardColor = isDarkMode ? "#1c1c1c" : "#fff";
  const borderColor = isDarkMode ? "#333" : "#e5e7eb";

  const canEdit = (role === "owner" || role === "admin") && showEditOptions;

  const statusInfo = getStatusInfo();
  const formattedExpiryDate = formatDate(itemExpiryDate);

  return (
    <TouchableOpacity style={[styles.cardContainer]} activeOpacity={0.9} onPress={onPress}>
      <View
        style={[
          styles.card,
          {
            backgroundColor: cardColor,
            borderColor,
            shadowColor: isDarkMode ? "#000" : "#00000022",
          },
        ]}
      >
        <View style={styles.topSection}>
          <View style={styles.imageBox}>
            {itemImageUrl ? (
              <Image source={{ uri: itemImageUrl }} style={styles.image} resizeMode="cover" />
            ) : (
              <LinearGradient
                colors={["#d0eaff", "#f0f8ff"]}
                start={[0, 0]}
                end={[1, 1]}
                style={styles.image}
              >
                <Text style={styles.categoryIcon}>{getCategoryIcon(itemCategory)}</Text>
              </LinearGradient>
            )}
            <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
              {statusInfo.icon}
            </View>
          </View>

          <View style={styles.itemInfo}>
            <View style={styles.rowBetween}>
              <Text style={[styles.itemName, { color: textColor }]} numberOfLines={2}>
                {itemName}
              </Text>
              {canEdit && (
                <Menu>
                  <MenuTrigger>
                    <View style={styles.menuBtn}>
                      <MoreVertical size={16} color={subTextColor} />
                    </View>
                  </MenuTrigger>
                  <MenuOptions customStyles={{ optionsContainer: { borderRadius: 12 } }}>
                    {onEditPress && (
                      <MenuOption onSelect={onEditPress}>
                        <View style={styles.menuRow}>
                          <Edit size={16} color="#333" />
                          <Text style={styles.menuText}>{t("common.edit")}</Text>
                        </View>
                      </MenuOption>
                    )}
                    {onDeletePress && (
                      <MenuOption onSelect={onDeletePress}>
                        <View style={styles.menuRow}>
                          <Trash2 size={16} color="#F44336" />
                          <Text style={[styles.menuText, { color: "#F44336" }]}>
                            {t("common.delete")}
                          </Text>
                        </View>
                      </MenuOption>
                    )}
                  </MenuOptions>
                </Menu>
              )}
            </View>
            <Text style={[styles.category, { color: subTextColor }]}>{getTranslatedCategory(itemCategory)}</Text>

            <View style={styles.quantityRow}>
              <View style={[styles.quantityBadge, { backgroundColor: statusInfo.bgColor }]}>
                <Text style={[styles.quantityNumber, { color: statusInfo.textColor }]}>
                  {itemQuantity}
                </Text>
                <Text style={[styles.quantityUnit, { color: statusInfo.textColor }]}>
                  {itemUnit}
                </Text>
              </View>
              <View style={styles.stockText}>
                <Text style={[styles.statusText, { color: statusInfo.textColor }]}>
                  {statusInfo.text}
                </Text>
                {itemMinQuantity > 0 && (
                  <Text style={[styles.minText, { color: subTextColor }]}>
                    {t("inventory.minimumStock")}: {itemMinQuantity} {itemUnit}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>

        <View style={styles.metaInfo}>
          {farmName && (
            <View style={styles.metaRow}>
              <MapPin size={14} color={subTextColor} />
              <Text style={[styles.metaLabel, { color: subTextColor }]}>{t("common.farm")}: </Text>
              <Text style={[styles.metaValue, { color: textColor }]}>{farmName}</Text>
            </View>
          )}
          {itemExpiryDate && (
            <View style={styles.metaRow}>
              <Calendar size={14} color={isExpired || isExpiringSoon ? "#F44336" : subTextColor} />
              <Text style={[styles.metaLabel, { color: subTextColor }]}>{t("inventory.expiryDate")}: </Text>
              <Text style={[styles.metaValue, { color: isExpired || isExpiringSoon ? "#F44336" : textColor }]}>
                {formattedExpiryDate || t("common.notSet")}
              </Text>
            </View>
          )}
        </View>

        <View style={[styles.footer, { borderTopColor: borderColor }]}>
          <Text style={[styles.footerText, { color: subTextColor }]}>
            {t("common.tapToViewDetails")}
          </Text>
          <ChevronRight size={16} color={subTextColor} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 20,
    borderWidth: 1,
    padding: 16,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 6,
  },
  topSection: {
    flexDirection: "row",
    gap: 16,
  },
  imageBox: {
    width: 100,
    height: 100,
    borderRadius: 16,
    overflow: "hidden",
    position: "relative",
  },
  image: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  categoryIcon: {
    fontSize: 40,
  },
  statusBadge: {
    position: "absolute",
    top: 6,
    right: 6,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    elevation: 3,
  },
  itemInfo: {
    flex: 1,
    justifyContent: "space-between",
  },
  rowBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
  },
  itemName: {
    fontSize: 18,
    fontWeight: "700",
    flex: 1,
  },
  menuBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  menuRow: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    gap: 10,
  },
  menuText: {
    fontSize: 14,
    color: "#333",
  },
  category: {
    fontSize: 14,
    marginTop: 2,
  },
  quantityRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    gap: 12,
  },
  quantityBadge: {
    flexDirection: "row",
    alignItems: "baseline",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    gap: 4,
  },
  quantityNumber: {
    fontSize: 20,
    fontWeight: "800",
  },
  quantityUnit: {
    fontSize: 12,
    fontWeight: "600",
    textTransform: "uppercase",
  },
  stockText: {
    flex: 1,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  minText: {
    fontSize: 11,
  },
  metaInfo: {
    marginTop: 16,
    gap: 8,
  },
  metaRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  metaLabel: {
    fontSize: 12,
    fontWeight: "600",
  },
  metaValue: {
    fontSize: 14,
    fontWeight: "600",
  },
  footer: {
    marginTop: 16,
    borderTopWidth: 1,
    paddingTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  footerText: {
    fontSize: 12,
    fontStyle: "italic",
  },
});
