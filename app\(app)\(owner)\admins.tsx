import { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, Modal, ScrollView, Alert, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { getUsers, User, createUser, updateUser, deleteUser } from "@/services/user-service";
import { UserItem } from "@/components/UserItem";
import { FilterButton } from "@/components/FilterButton";
import { Search, Plus, X, Check, Trash2 } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { getFarms } from "@/services/farm-service";

interface Farm {
  id: string;
  name: string;
}

export default function OwnerAdminsScreen() {
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [formData, setFormData] = useState({
    displayName: "",
    email: "",
    password: "",
    assignedFarms: [] as string[],
  });
  const [formErrors, setFormErrors] = useState({
    displayName: "",
    email: "",
    password: "",
  });
  const [submitting, setSubmitting] = useState(false);

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  const loadUsers = async () => {
    try {
      const allUsers = await getUsers();
      // Filter only admin users
      const adminUsers = allUsers.filter(user => user.role === "admin");
      setUsers(adminUsers);
      setFilteredUsers(adminUsers);
      
      // Load farms
      const farmData = await getFarms();
      setFarms(farmData);
    } catch (error) {
      console.error("Error loading admins:", error);
      Alert.alert("Error", "Failed to load admin users");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadUsers();
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text) {
      const filtered = users.filter(
        (user) =>
          user.displayName.toLowerCase().includes(text.toLowerCase()) ||
          user.email.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users);
    }
  };

  const handleAddAdmin = () => {
    setSelectedUser(null);
    setFormData({
      displayName: "",
      email: "",
      password: "",
      assignedFarms: [],
    });
    setFormErrors({
      displayName: "",
      email: "",
      password: "",
    });
    setModalVisible(true);
  };

  const handleEditAdmin = (user: User) => {
    setSelectedUser(user);
    setFormData({
      displayName: user.displayName,
      email: user.email,
      password: "",
      assignedFarms: user.assignedFarms || [],
    });
    setFormErrors({
      displayName: "",
      email: "",
      password: "",
    });
    setModalVisible(true);
  };

  const handleDeleteAdmin = (user: User) => {
    setSelectedUser(user);
    setDeleteModalVisible(true);
  };

  const confirmDeleteAdmin = async () => {
    if (!selectedUser) return;
    
    setSubmitting(true);
    try {
      await deleteUser(selectedUser.id);
      setDeleteModalVisible(false);
      Alert.alert("Success", "Admin user deleted successfully");
      loadUsers();
    } catch (error) {
      console.error("Error deleting admin:", error);
      Alert.alert("Error", "Failed to delete admin user");
    } finally {
      setSubmitting(false);
    }
  };

  const validateForm = () => {
    let valid = true;
    const errors = {
      displayName: "",
      email: "",
      password: "",
    };

    if (!formData.displayName.trim()) {
      errors.displayName = "Name is required";
      valid = false;
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
      valid = false;
    }

    if (!selectedUser && !formData.password.trim()) {
      errors.password = "Password is required for new admins";
      valid = false;
    } else if (!selectedUser && formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters";
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setSubmitting(true);
    try {
      if (selectedUser) {
        // Update existing admin
        await updateUser(selectedUser.id, {
          role: "admin",
          displayName: formData.displayName,
          assignedFarms: formData.assignedFarms
        });
        Alert.alert("Success", "Admin updated successfully");
      } else {
        // Create new admin
        await createUser({
          displayName: formData.displayName,
          email: formData.email,
          role: "admin",
          assignedFarms: formData.assignedFarms,
        });
        Alert.alert("Success", "Admin created successfully");
      }
      setModalVisible(false);
      loadUsers();
    } catch (error) {
      console.error("Error saving admin:", error);
      Alert.alert("Error", "Failed to save admin user");
    } finally {
      setSubmitting(false);
    }
  };

  const toggleFarmSelection = (farmId: string) => {
    setFormData(prev => {
      if (prev.assignedFarms.includes(farmId)) {
        return {
          ...prev,
          assignedFarms: prev.assignedFarms.filter(id => id !== farmId)
        };
      } else {
        return {
          ...prev,
          assignedFarms: [...prev.assignedFarms, farmId]
        };
      }
    });
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>Admin Management</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddAdmin}>
          <Plus size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: cardColor, borderColor }]}>
          <Search size={20} color={secondaryTextColor} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search admins..."
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      <FlatList
        data={filteredUsers}
        renderItem={({ item }) => (
          <UserItem
            user={item}
            onPress={() => handleEditAdmin(item)}
            onDelete={() => handleDeleteAdmin(item)}
          />
        )}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
              {loading ? "Loading admins..." : "No admin users found"}
            </Text>
          </View>
        }
      />

      {/* Add/Edit Admin Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: cardColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>
                {selectedUser ? "Edit Admin" : "Add New Admin"}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <X size={24} color={textColor} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: textColor }]}>Name</Text>
                <TextInput
                  style={[
                    styles.input,
                    { color: textColor, borderColor },
                    formErrors.displayName ? styles.inputError : null
                  ]}
                  value={formData.displayName}
                  onChangeText={(text) => setFormData({ ...formData, displayName: text })}
                  placeholder="Enter admin name"
                  placeholderTextColor={secondaryTextColor}
                />
                {formErrors.displayName ? (
                  <Text style={styles.errorText}>{formErrors.displayName}</Text>
                ) : null}
              </View>

              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: textColor }]}>Email</Text>
                <TextInput
                  style={[
                    styles.input,
                    { color: textColor, borderColor },
                    formErrors.email ? styles.inputError : null
                  ]}
                  value={formData.email}
                  onChangeText={(text) => setFormData({ ...formData, email: text })}
                  placeholder="Enter email address"
                  placeholderTextColor={secondaryTextColor}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  editable={!selectedUser} // Email can't be edited for existing users
                />
                {formErrors.email ? (
                  <Text style={styles.errorText}>{formErrors.email}</Text>
                ) : null}
              </View>

              {!selectedUser && (
                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: textColor }]}>Password</Text>
                  <TextInput
                    style={[
                      styles.input,
                      { color: textColor, borderColor },
                      formErrors.password ? styles.inputError : null
                    ]}
                    value={formData.password}
                    onChangeText={(text) => setFormData({ ...formData, password: text })}
                    placeholder="Enter password"
                    placeholderTextColor={secondaryTextColor}
                    secureTextEntry
                  />
                  {formErrors.password ? (
                    <Text style={styles.errorText}>{formErrors.password}</Text>
                  ) : null}
                </View>
              )}

              <View style={styles.formGroup}>
                <Text style={[styles.label, { color: textColor }]}>Assigned Farms</Text>
                {farms.length > 0 ? (
                  farms.map((farm) => (
                    <TouchableOpacity
                      key={farm.id}
                      style={[
                        styles.farmItem,
                        { borderColor },
                        formData.assignedFarms.includes(farm.id) && styles.selectedFarmItem
                      ]}
                      onPress={() => toggleFarmSelection(farm.id)}
                    >
                      <Text
                        style={[
                          styles.farmItemText,
                          { color: textColor },
                          formData.assignedFarms.includes(farm.id) && styles.selectedFarmItemText
                        ]}
                      >
                        {farm.name}
                      </Text>
                      {formData.assignedFarms.includes(farm.id) && (
                        <Check size={20} color="#2E7D32" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
                    No farms available
                  </Text>
                )}
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <Text style={styles.buttonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={deleteModalVisible}
        onRequestClose={() => setDeleteModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.confirmModal, { backgroundColor: cardColor }]}>
            <Text style={[styles.confirmTitle, { color: textColor }]}>Delete Admin</Text>
            <Text style={[styles.confirmText, { color: secondaryTextColor }]}>
              Are you sure you want to delete {selectedUser?.displayName}? This action cannot be undone.
            </Text>
            <View style={styles.confirmButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => setDeleteModalVisible(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.deleteButton]}
                onPress={confirmDeleteAdmin}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <>
                    <Trash2 size={16} color="#fff" />
                    <Text style={styles.buttonText}>Delete</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  addButton: {
    backgroundColor: "#2E7D32",
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "90%",
    maxHeight: "80%",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  modalScrollView: {
    maxHeight: 400,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#D32F2F",
  },
  errorText: {
    color: "#D32F2F",
    fontSize: 12,
    marginTop: 4,
  },
  farmItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedFarmItem: {
    borderColor: "#2E7D32",
    backgroundColor: "rgba(46, 125, 50, 0.1)",
  },
  farmItemText: {
    fontSize: 16,
  },
  selectedFarmItemText: {
    fontWeight: "600",
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 20,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    minWidth: 100,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#757575",
    marginRight: 10,
  },
  saveButton: {
    backgroundColor: "#2E7D32",
  },
  deleteButton: {
    backgroundColor: "#D32F2F",
    flexDirection: "row",
    gap: 8,
  },
  confirmModal: {
    width: "90%",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  confirmTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 12,
  },
  confirmText: {
    fontSize: 16,
    marginBottom: 20,
    lineHeight: 22,
  },
  confirmButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
});