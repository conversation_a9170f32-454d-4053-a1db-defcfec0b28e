import { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, ScrollView, Alert, ActivityIndicator, Platform, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router } from "expo-router";
import { createUser } from "@/services/user-service";
import { uploadUserProfileImage } from "@/services/storage-service";
import { Farm } from "@/services/farm-service";
import { ArrowLeft, Check, Eye, EyeOff, Calendar, User as UserIcon, MapPin, CreditCard, Image as ImageIcon, Camera } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import * as ImagePicker from 'expo-image-picker';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';

export default function AddUserScreen() {
  const { theme, colors } = useTheme();
  const { t } = useLanguage();
  const { userFarms } = useFarm();
  const { user: currentUser } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "caretaker",
    farmIds: [] as string[],
    phone: "",
    phoneNumber: "",
    bio: "",
    gender: "",
    dateOfBirth: "",
    cnic: "",
    address: "",
    photoURL: "",
  });
  const [formErrors, setFormErrors] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    farmIds: "",
    gender: "",
    dateOfBirth: "",
    cnic: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  // Get role-based primary color
  const getRolePrimaryColor = () => {
    if (!currentUser) return colors.primary;
    switch (currentUser.role) {
      case 'owner':
        return isDarkMode ? '#2E7D32' : '#4CAF50';
      case 'admin':
        return isDarkMode ? '#1976D2' : '#2196F3';
      case 'caretaker':
        return isDarkMode ? '#F57C00' : '#FF9800';
      default:
        return colors.primary;
    }
  };

  const primaryColor = getRolePrimaryColor();

  // Gender options
  const genderOptions = [
    { value: "male", label: t('users.male') },
    { value: "female", label: t('users.female') },
    { value: "other", label: t('users.other') },
  ];

  // Helper function to safely get location display text
  const getLocationDisplay = (farm: Farm): string => {
    try {
      if (!farm.location && !farm.locationData) {
        return t('common.location');
      }
      
      if (farm.location) {
        if (typeof farm.location === 'string') {
          return farm.location;
        }
        
        if (typeof farm.location === 'object' && farm.location !== null) {
          const locationObj = farm.location as any;
          if (locationObj.address && typeof locationObj.address === 'string') {
            return locationObj.address;
          }
          
          if (locationObj.latitude && locationObj.longitude) {
            return `${String(locationObj.latitude)}, ${String(locationObj.longitude)}`;
          }
        }
      }
      
      if (farm.locationData) {
        if (typeof farm.locationData === 'string') {
          return farm.locationData;
        }
        
        if (typeof farm.locationData === 'object' && farm.locationData !== null) {
          const locationDataObj = farm.locationData as any;
          if (locationDataObj.address && typeof locationDataObj.address === 'string') {
            return locationDataObj.address;
          }
          
          if (locationDataObj.latitude && locationDataObj.longitude) {
            return `${String(locationDataObj.latitude)}, ${String(locationDataObj.longitude)}`;
          }
        }
      }
      
      return t('common.location');
    } catch (error) {
      console.error('Error getting location display:', error);
      return t('common.location');
    }
  };

  // Determine role based on current user's role
  const getUserRole = () => {
    if (currentUser?.role === "owner") {
      return "admin";
    } else if (currentUser?.role === "admin") {
      return "caretaker";
    }
    return "caretaker";
  };

  // Set default role and farm selection
  useEffect(() => {
    const defaultRole = getUserRole();
    setFormData(prev => ({ 
      ...prev, 
      role: defaultRole,
      farmIds: userFarms.length > 0 ? [userFarms[0].id] : []
    }));
  }, [currentUser, userFarms]);

  // Format CNIC input
  const formatCNIC = (text: string) => {
    // Remove all non-digits
    const digits = text.replace(/\D/g, '');
    
    // Format as XXXXX-XXXXXXX-X
    if (digits.length <= 5) {
      return digits;
    } else if (digits.length <= 12) {
      return `${digits.slice(0, 5)}-${digits.slice(5)}`;
    } else {
      return `${digits.slice(0, 5)}-${digits.slice(5, 12)}-${digits.slice(12, 13)}`;
    }
  };

  // Format date input (DD/MM/YYYY)
  const formatDate = (text: string) => {
    // Remove all non-digits
    const digits = text.replace(/\D/g, '');
    
    // Format as DD/MM/YYYY
    if (digits.length <= 2) {
      return digits;
    } else if (digits.length <= 4) {
      return `${digits.slice(0, 2)}/${digits.slice(2)}`;
    } else {
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/${digits.slice(4, 8)}`;
    }
  };

  const validateForm = () => {
    let valid = true;
    const errors = {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      farmIds: "",
      gender: "",
      dateOfBirth: "",
      cnic: "",
    };

    if (!formData.name.trim()) {
      errors.name = t('users.nameRequired');
      valid = false;
    }

    if (!formData.email.trim()) {
      errors.email = t('users.emailRequired');
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = t('users.emailInvalid');
      valid = false;
    }

    if (!formData.password.trim()) {
      errors.password = t('users.passwordRequired');
      valid = false;
    } else if (formData.password.length < 6) {
      errors.password = t('users.passwordTooShort');
      valid = false;
    }

    if (!formData.confirmPassword.trim()) {
      errors.confirmPassword = t('users.confirmPasswordRequired');
      valid = false;
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = t('users.passwordsDoNotMatch');
      valid = false;
    }

    if (formData.farmIds.length === 0) {
      errors.farmIds = t('users.farmSelectionRequired');
      valid = false;
    }

    if (!formData.gender) {
      errors.gender = t('users.genderRequired');
      valid = false;
    }

    if (!formData.dateOfBirth.trim()) {
      errors.dateOfBirth = t('users.dateOfBirthRequired');
      valid = false;
    } else if (!/^\d{2}\/\d{2}\/\d{4}$/.test(formData.dateOfBirth)) {
      errors.dateOfBirth = t('users.dateFormatInvalid');
      valid = false;
    }

    if (!formData.cnic.trim()) {
      errors.cnic = t('users.cnicRequired');
      valid = false;
    } else if (!/^\d{5}-\d{7}-\d{1}$/.test(formData.cnic)) {
      errors.cnic = t('users.cnicFormatInvalid');
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      let photoURL = formData.photoURL;

      // Upload image to Firebase Storage if a local image is selected
      if (formData.photoURL && formData.photoURL.startsWith('file://')) {
        console.log("Uploading user profile image to Storage...");
        const tempUserId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        photoURL = await uploadUserProfileImage(formData.photoURL, tempUserId);
        console.log("User profile image uploaded:", photoURL);
      }

      await createUser({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        assignedFarmIds: formData.farmIds,
        phone: formData.phone,
        phoneNumber: formData.phoneNumber || formData.phone,
        bio: formData.bio,
        gender: formData.gender,
        dateOfBirth: formData.dateOfBirth,
        cnic: formData.cnic,
        address: formData.address,
        photoURL: photoURL,
      });
      Alert.alert(t('common.success'), t('users.userCreatedSuccessfully'), [
        {
          text: t('common.ok'),
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Error creating user:", error);
      Alert.alert(t('common.error'), t('users.failedToCreateUser'));
    } finally {
      setSubmitting(false);
    }
  };

  const toggleFarmSelection = (farmId: string) => {
    const updatedFarmIds = formData.farmIds.includes(farmId)
      ? formData.farmIds.filter(id => id !== farmId)
      : [...formData.farmIds, farmId];
    
    setFormData({ ...formData, farmIds: updatedFarmIds });
  };

  const handleGoBack = () => {
    router.back();
  };

  const pickImage = async () => {
    // Request permission to access media library
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(t('common.permissionDenied'), t('users.permissionDeniedCamera'));
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false, // Remove cropping
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      setFormData({ ...formData, photoURL: result.assets[0].uri });
    }
  };

  const takePhoto = async () => {
    if (!cameraPermission) {
      return;
    }

    if (!cameraPermission.granted) {
      const { status } = await requestCameraPermission();
      if (status !== 'granted') {
        Alert.alert(t('common.permissionDenied'), t('users.permissionDeniedCameraCapture'));
        return;
      }
    }

    setShowCamera(true);
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const capturePhoto = async () => {
    if (cameraRef) {
      const photo = await cameraRef.takePictureAsync();
      setFormData({ ...formData, photoURL: photo.uri });
      setShowCamera(false);
    }
  };

  let cameraRef: any;

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('users.addUser'),
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      </SafeAreaView>
    );
  }

  if (showCamera && Platform.OS !== 'web') {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t('users.capturePhoto'),
            headerStyle: { backgroundColor: primaryColor },
            headerTintColor: "#fff",
            headerTitleStyle: { fontWeight: "bold" },
            headerLeft: () => (
              <TouchableOpacity onPress={() => setShowCamera(false)} style={styles.headerButton}>
                <ArrowLeft size={24} color="#fff" />
              </TouchableOpacity>
            ),
          }}
        />
        <CameraView
          style={styles.camera}
          facing={facing}
          ref={ref => {
            cameraRef = ref;
          }}
        >
          <View style={styles.cameraButtonContainer}>
            <TouchableOpacity style={styles.cameraButton} onPress={toggleCameraFacing}>
              <Text style={styles.cameraButtonText}>{t('users.flipCamera')}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.cameraButton, styles.captureButton, { backgroundColor: primaryColor }]} onPress={capturePhoto}>
              <Text style={styles.cameraButtonText}>{t('users.capture')}</Text>
            </TouchableOpacity>
          </View>
        </CameraView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('users.addUser'),
          headerStyle: { backgroundColor: primaryColor },
          headerTintColor: "#fff",
          headerTitleStyle: { fontWeight: "bold" },
          headerLeft: () => (
            <TouchableOpacity onPress={handleGoBack} style={styles.headerButton}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity 
              onPress={handleSubmit} 
              style={styles.headerButton}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Check size={24} color="#fff" />
              )}
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          {/* Basic Information Section */}
          <View style={styles.sectionHeader}>
            <UserIcon size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('common.basicInformation')}</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.fullName')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor },
                formErrors.name ? styles.inputError : null
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder={t('users.enterFullName')}
              placeholderTextColor={secondaryTextColor}
              autoCapitalize="words"
            />
            {formErrors.name ? (
              <Text style={styles.errorText}>{formErrors.name}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.emailAddress')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor },
                formErrors.email ? styles.inputError : null
              ]}
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              placeholder={t('users.enterEmailAddress')}
              placeholderTextColor={secondaryTextColor}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {formErrors.email ? (
              <Text style={styles.errorText}>{formErrors.email}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.gender')} *</Text>
            <View style={styles.genderContainer}>
              {genderOptions.map((gender) => (
                <TouchableOpacity
                  key={gender.value}
                  style={[
                    styles.genderOption,
                    { borderColor, backgroundColor: cardColor },
                    formData.gender === gender.value && [styles.selectedGenderOption, { backgroundColor: primaryColor, borderColor: primaryColor }]
                  ]}
                  onPress={() => setFormData({ ...formData, gender: gender.value })}
                >
                  <Text
                    style={[
                      styles.genderOptionText,
                      { color: textColor },
                      formData.gender === gender.value && styles.selectedGenderOptionText
                    ]}
                  >
                    {gender.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {formErrors.gender ? (
              <Text style={styles.errorText}>{formErrors.gender}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.dateOfBirth')} *</Text>
            <View style={styles.inputWithIcon}>
              <TextInput
                style={[
                  styles.inputWithIconText,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.dateOfBirth ? styles.inputError : null
                ]}
                value={formData.dateOfBirth}
                onChangeText={(text) => setFormData({ ...formData, dateOfBirth: formatDate(text) })}
                placeholder={t('users.dateFormat')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
                maxLength={10}
              />
              <Calendar size={20} color={secondaryTextColor} style={styles.inputIcon} />
            </View>
            {formErrors.dateOfBirth ? (
              <Text style={styles.errorText}>{formErrors.dateOfBirth}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.cnic')} *</Text>
            <View style={styles.inputWithIcon}>
              <TextInput
                style={[
                  styles.inputWithIconText,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.cnic ? styles.inputError : null
                ]}
                value={formData.cnic}
                onChangeText={(text) => setFormData({ ...formData, cnic: formatCNIC(text) })}
                placeholder={t('users.cnicFormat')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
                maxLength={15}
              />
              <CreditCard size={20} color={secondaryTextColor} style={styles.inputIcon} />
            </View>
            {formErrors.cnic ? (
              <Text style={styles.errorText}>{formErrors.cnic}</Text>
            ) : null}
          </View>

          {/* Contact Information Section */}
          <View style={[styles.sectionHeader, { marginTop: 24 }]}>
            <MapPin size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('users.contactInformation')}</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.phoneNumber')}</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text, phoneNumber: text })}
              placeholder={t('users.enterPhoneNumber')}
              placeholderTextColor={secondaryTextColor}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.address')}</Text>
            <TextInput
              style={[
                styles.textArea,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              placeholder={t('users.enterCompleteAddress')}
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.profileImage')}</Text>
            <View style={styles.imagePickerContainer}>
              {formData.photoURL ? (
                <View style={styles.imagePreviewContainer}>
                  <Image source={{ uri: formData.photoURL }} style={styles.imagePreview} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => setFormData({ ...formData, photoURL: "" })}
                  >
                    <Text style={styles.removeImageText}>{t('users.removeImage')}</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.imagePickerButtons}>
                  <TouchableOpacity
                    style={[styles.imagePickerButton, { borderColor, backgroundColor: cardColor }]}
                    onPress={pickImage}
                  >
                    <ImageIcon size={20} color={primaryColor} />
                    <Text style={[styles.imagePickerButtonText, { color: textColor }]}>{t('users.uploadImage')}</Text>
                  </TouchableOpacity>
                  {Platform.OS !== 'web' && (
                    <TouchableOpacity
                      style={[styles.imagePickerButton, { borderColor, backgroundColor: cardColor }]}
                      onPress={takePhoto}
                    >
                      <Camera size={20} color={primaryColor} />
                      <Text style={[styles.imagePickerButtonText, { color: textColor }]}>{t('users.takePhoto')}</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.bio')}</Text>
            <TextInput
              style={[
                styles.textArea,
                { color: textColor, borderColor, backgroundColor: cardColor }
              ]}
              value={formData.bio}
              onChangeText={(text) => setFormData({ ...formData, bio: text })}
              placeholder={t('users.enterBio')}
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {/* Security Section */}
          <View style={[styles.sectionHeader, { marginTop: 24 }]}>
            <Eye size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('users.security')}</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.password')} *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[
                  styles.passwordInput,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.password ? styles.inputError : null
                ]}
                value={formData.password}
                onChangeText={(text) => setFormData({ ...formData, password: text })}
                placeholder={t('users.enterPassword')}
                placeholderTextColor={secondaryTextColor}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff size={20} color={secondaryTextColor} />
                ) : (
                  <Eye size={20} color={secondaryTextColor} />
                )}
              </TouchableOpacity>
            </View>
            {formErrors.password ? (
              <Text style={styles.errorText}>{formErrors.password}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.confirmPassword')} *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[
                  styles.passwordInput,
                  { color: textColor, borderColor, backgroundColor: cardColor },
                  formErrors.confirmPassword ? styles.inputError : null
                ]}
                value={formData.confirmPassword}
                onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
                placeholder={t('users.confirmPasswordPlaceholder')}
                placeholderTextColor={secondaryTextColor}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff size={20} color={secondaryTextColor} />
                ) : (
                  <Eye size={20} color={secondaryTextColor} />
                )}
              </TouchableOpacity>
            </View>
            {formErrors.confirmPassword ? (
              <Text style={styles.errorText}>{formErrors.confirmPassword}</Text>
            ) : null}
          </View>

          {/* Farm Assignment Section */}
          <View style={[styles.sectionHeader, { marginTop: 24 }]}>
            <MapPin size={20} color={primaryColor} />
            <Text style={[styles.sectionTitle, { color: textColor }]}>{t('users.farmAssignment')}</Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('users.assignToFarms')} *</Text>
            {formErrors.farmIds ? (
              <Text style={styles.errorText}>{formErrors.farmIds}</Text>
            ) : null}
            <ScrollView style={[styles.farmsContainer, { borderColor, backgroundColor: cardColor }]} nestedScrollEnabled={true}>
              {userFarms.length === 0 ? (
                <View style={styles.noFarmsContainer}>
                  <Text style={[styles.noFarmsText, { color: secondaryTextColor }]}>
                    {t('users.noFarmsAvailable')}
                  </Text>
                </View>
              ) : (
                userFarms.map((farm) => (
                  <TouchableOpacity
                    key={farm.id}
                    style={[
                      styles.farmOption,
                      formData.farmIds.includes(farm.id) && [styles.selectedFarmOption, { backgroundColor: primaryColor, borderColor: primaryColor }],
                      { borderColor }
                    ]}
                    onPress={() => toggleFarmSelection(farm.id)}
                  >
                    <Text
                      style={[
                        styles.farmOptionText,
                        { color: textColor },
                        formData.farmIds.includes(farm.id) && styles.selectedFarmOptionText
                      ]}
                    >
                      {farm.name || t('common.farm')}
                    </Text>
                    <Text
                      style={[
                        styles.farmLocationText,
                        { color: secondaryTextColor },
                        formData.farmIds.includes(farm.id) && styles.selectedFarmLocationText
                      ]}
                    >
                      {getLocationDisplay(farm)}
                    </Text>
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </ScrollView>

      <View style={[styles.footer, { backgroundColor: cardColor, borderTopColor: borderColor }]}>
        <TouchableOpacity
          style={[styles.button, styles.cancelButton, { backgroundColor: isDarkMode ? "#424242" : "#757575" }]}
          onPress={handleGoBack}
        >
          <Text style={styles.buttonText}>{t('common.cancel')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.saveButton, { backgroundColor: primaryColor }]}
          onPress={handleSubmit}
          disabled={submitting || userFarms.length === 0}
        >
          {submitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.buttonText}>{t('users.createUser')}</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  headerButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 8,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "600",
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  inputWithIcon: {
    position: "relative",
  },
  inputWithIconText: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingRight: 50,
    fontSize: 16,
  },
  inputIcon: {
    position: "absolute",
    right: 16,
    top: 14,
  },
  textArea: {
    height: 80,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  passwordContainer: {
    position: "relative",
  },
  passwordInput: {
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingRight: 50,
    fontSize: 16,
  },
  eyeButton: {
    position: "absolute",
    right: 16,
    top: 14,
    padding: 4,
  },
  inputError: {
    borderColor: "#D32F2F",
  },
  errorText: {
    color: "#D32F2F",
    fontSize: 12,
    marginTop: 4,
  },
  genderContainer: {
    flexDirection: "row",
    gap: 12,
  },
  genderOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: "center",
  },
  selectedGenderOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  genderOptionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  selectedGenderOptionText: {
    color: "#FFFFFF",
  },
  farmsContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    maxHeight: 200,
  },
  noFarmsContainer: {
    padding: 20,
    alignItems: "center",
  },
  noFarmsText: {
    fontSize: 14,
    textAlign: "center",
  },
  farmOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  selectedFarmOption: {
    // backgroundColor and borderColor are set dynamically based on role
  },
  farmOptionText: {
    fontSize: 16,
    fontWeight: "500",
  },
  selectedFarmOptionText: {
    color: "#FFFFFF",
  },
  farmLocationText: {
    fontSize: 12,
    marginTop: 2,
  },
  selectedFarmLocationText: {
    color: "#FFFFFF",
    opacity: 0.8,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    borderTopWidth: 1,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 6,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#757575",
  },
  saveButton: {
    // backgroundColor is set dynamically based on role
  },
  imagePickerContainer: {
    marginBottom: 16,
  },
  imagePickerButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  imagePickerButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 8,
  },
  imagePickerButtonText: {
    fontSize: 16,
    fontWeight: "500",
  },
  imagePreviewContainer: {
    position: "relative",
    alignItems: "center",
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 8,
  },
  removeImageButton: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#D32F2F",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  removeImageText: {
    color: "#fff",
    fontSize: 12,
  },
  camera: {
    flex: 1,
  },
  cameraButtonContainer: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "transparent",
    margin: 64,
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  cameraButton: {
    flex: 0.4,
    alignSelf: "flex-end",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 16,
    borderRadius: 12,
  },
  captureButton: {
    // backgroundColor is set dynamically based on role in the component
  },
  cameraButtonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "white",
  },
});