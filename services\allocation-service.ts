import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  orderBy, 
  limit as firestoreLimit, 
  onSnapshot, 
  Timestamp,
  startAfter
} from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import { createExpense } from "./expense-service";

const db = getFirestore();

// Define Allocation type
export interface Allocation {
  id: string;
  farmId: string;
  farmName: string;
  type: 'inventory' | 'machinery';
  itemId: string;
  itemName: string;
  quantity?: number; // For inventory items - original allocated quantity
  unit?: string; // For inventory items
  returnedQuantity?: number; // Quantity that has been returned
  remainingQuantity?: number; // Quantity still with user (calculated)
  allocatedTo: string; // User ID
  allocatedToName: string; // User name
  allocatedToRole: string; // User role
  allocatedBy: string; // User ID who allocated
  allocatedByName: string; // User name who allocated
  allocatedByRole: string; // User role who allocated
  allocationDate: string;
  expectedReturnDate?: string;
  actualReturnDate?: string;
  status: 'allocated' | 'partially_returned' | 'returned' | 'overdue' | 'damaged' | 'lost';
  condition?: 'excellent' | 'good' | 'fair' | 'poor'; // Condition when returned
  returnNotes?: string;
  allocationNotes?: string;
  requestId?: string; // Related request that led to this allocation
  allocationRequestId?: string; // Related allocation request
  dailyRate?: number; // Cost per day for allocation
  weeklyRate?: number; // Cost per week for allocation
  monthlyRate?: number; // Cost per month for allocation
  rateType?: 'daily' | 'weekly' | 'monthly' | 'custom';
  customRate?: number; // Custom rate amount
  totalCost?: number; // Calculated total cost
  expenseId?: string; // Related expense record
  createdAt: string;
  updatedAt: string;
}

// Define AllocationRequest type
export interface AllocationRequest {
  id: string;
  farmId: string;
  farmName: string;
  type: 'inventory' | 'machinery';
  itemId: string;
  itemName: string;
  requestedQuantity: number;
  unit?: string;
  requestedBy: string; // User ID
  requestedByName: string; // User name
  requestedByRole: string; // User role (caretaker or admin)
  requestedTo: string; // User ID (admin for caretakers, owner for admins)
  requestedToName: string; // User name
  requestedToRole: string; // User role
  reason: string;
  urgency: 'low' | 'medium' | 'high' | 'emergency';
  expectedReturnDate?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  allocationId?: string; // Created allocation ID when approved
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Define ReturnRequest type
export interface ReturnRequest {
  id: string;
  farmId: string;
  farmName: string;
  allocationId: string;
  type: 'inventory' | 'machinery';
  itemId: string;
  itemName: string;
  returnQuantity: number; // Quantity being returned
  unit?: string;
  returnReason: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  returnNotes?: string;
  requestedBy: string; // User ID
  requestedByName: string; // User name
  requestedByRole: string; // User role
  requestedTo: string; // User ID (admin for caretakers, owner for admins)
  requestedToName: string; // User name
  requestedToRole: string; // User role
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

// Define AllocationFilter type
export interface AllocationFilter {
  type?: 'inventory' | 'machinery' | 'all';
  status?: 'allocated' | 'returned' | 'overdue' | 'damaged' | 'lost' | 'all';
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  farmId?: string;
}

// Define AllocationSummary type
export interface AllocationSummary {
  totalAllocations: number;
  activeAllocations: number;
  returnedAllocations: number;
  overdueAllocations: number;
  inventoryAllocations: number;
  machineryAllocations: number;
  userBreakdown: { [userId: string]: { count: number; userName: string; userRole: string } };
  itemBreakdown: { [itemId: string]: { count: number; itemName: string; type: string } };
  statusBreakdown: { [status: string]: number };
  monthlyTrend: { month: string; count: number }[];
  returnRate: number;
  averageAllocationDuration: number;
  totalCost: number;
  topUsers: { userId: string; userName: string; count: number; percentage: number }[];
  topItems: { itemId: string; itemName: string; count: number; percentage: number }[];
}

// Create allocation (when request is approved)
export const createAllocation = async (allocationData: Omit<Allocation, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const now = new Date().toISOString();

    // Validate required fields
    if (!allocationData.farmId) {
      throw new Error('Farm ID is required for allocation');
    }
    if (!allocationData.itemId) {
      throw new Error('Item ID is required for allocation');
    }
    if (!allocationData.allocatedTo) {
      throw new Error('Allocated to user ID is required');
    }
    if (!allocationData.allocatedBy) {
      throw new Error('Allocated by user ID is required');
    }

    // Remove undefined values to prevent Firestore errors
    const cleanedData: Record<string, any> = {};
    Object.entries(allocationData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedData[key] = value;
      }
    });

    const allocation: Omit<Allocation, 'id'> = {
      ...cleanedData,
      status: 'allocated',
      returnedQuantity: cleanedData.returnedQuantity || 0,
      remainingQuantity: cleanedData.remainingQuantity || cleanedData.quantity || 0,
      createdAt: now,
      updatedAt: now,
    } as Omit<Allocation, 'id'>;

    // Calculate total cost if rate is provided
    if (allocation.rateType && allocation.expectedReturnDate) {
      const allocationDays = Math.ceil(
        (new Date(allocation.expectedReturnDate).getTime() - new Date(allocation.allocationDate).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      let rate = 0;
      switch (allocation.rateType) {
        case 'daily':
          rate = allocation.dailyRate || 0;
          allocation.totalCost = rate * allocationDays;
          break;
        case 'weekly':
          rate = allocation.weeklyRate || 0;
          allocation.totalCost = rate * Math.ceil(allocationDays / 7);
          break;
        case 'monthly':
          rate = allocation.monthlyRate || 0;
          allocation.totalCost = rate * Math.ceil(allocationDays / 30);
          break;
        case 'custom':
          allocation.totalCost = allocation.customRate || 0;
          break;
      }
    }

    const docRef = await addDoc(collection(db, 'allocations'), allocation);
    
    // Create expense record for the allocation
    if (allocation.totalCost && allocation.totalCost > 0) {
      const expenseData = {
        farmId: allocation.farmId,
        farmName: allocation.farmName,
        type: 'user' as const,
        category: allocation.type as 'inventory' | 'machinery',
        amount: allocation.totalCost,
        description: `Allocation of ${allocation.itemName} to ${allocation.allocatedToName}`,
        date: allocation.allocationDate,
        userId: allocation.allocatedTo,
        userName: allocation.allocatedToName,
        userRole: allocation.allocatedToRole,
        relatedItemId: allocation.itemId,
        relatedItemName: allocation.itemName,
        relatedItemType: allocation.type,
        allocationId: docRef.id,
        notes: allocation.allocationNotes,
        createdBy: allocation.allocatedBy,
        createdByName: allocation.allocatedByName,
        createdByRole: allocation.allocatedByRole,
      };

      const expenseId = await createExpense(expenseData);
      
      // Update allocation with expense ID
      await updateDoc(doc(db, 'allocations', docRef.id), { expenseId });
    }

    console.log('Allocation created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating allocation:', error);
    throw error;
  }
};

// Get allocation by ID
export const getAllocationById = async (allocationId: string): Promise<Allocation | null> => {
  try {
    const docRef = doc(db, 'allocations', allocationId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Allocation;
    } else {
      console.log('No allocation found with ID:', allocationId);
      return null;
    }
  } catch (error) {
    console.error('Error getting allocation:', error);
    throw error;
  }
};

// Get allocations by farm with filtering
export const getAllocationsByFarm = async (
  farmId: string, 
  filter?: AllocationFilter,
  limit?: number,
  lastDoc?: any
): Promise<{ allocations: Allocation[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId),
      orderBy('allocationDate', 'desc')
    );

    // Apply filters
    if (filter?.type && filter.type !== 'all') {
      q = query(q, where('type', '==', filter.type));
    }

    if (filter?.status && filter.status !== 'all') {
      q = query(q, where('status', '==', filter.status));
    }

    if (filter?.userId) {
      q = query(q, where('allocatedTo', '==', filter.userId));
    }

    if (filter?.dateFrom) {
      q = query(q, where('allocationDate', '>=', filter.dateFrom));
    }

    if (filter?.dateTo) {
      q = query(q, where('allocationDate', '<=', filter.dateTo));
    }

    // Add pagination
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const allocations: Allocation[] = [];
    let newLastDoc = null;

    querySnapshot.forEach((doc) => {
      allocations.push({ id: doc.id, ...doc.data() } as Allocation);
      newLastDoc = doc;
    });

    return { allocations, lastDoc: newLastDoc };
  } catch (error) {
    console.error('Error getting allocations:', error);
    throw error;
  }
};

// Get user allocations (for caretakers to see only their allocations)
export const getUserAllocations = async (
  farmId: string,
  userId: string,
  limit?: number,
  lastDoc?: any
): Promise<{ allocations: Allocation[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId),
      where('allocatedTo', '==', userId),
      orderBy('allocationDate', 'desc')
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const allocations: Allocation[] = [];
    let newLastDoc = null;

    querySnapshot.forEach((doc) => {
      allocations.push({ id: doc.id, ...doc.data() } as Allocation);
      newLastDoc = doc;
    });

    return { allocations, lastDoc: newLastDoc };
  } catch (error) {
    console.error('Error getting user allocations:', error);
    throw error;
  }
};

// Return allocation
export const returnAllocation = async (
  allocationId: string, 
  returnData: {
    condition: 'excellent' | 'good' | 'fair' | 'poor';
    returnNotes: string;
    actualReturnDate?: string;
    returnedBy: string;
    returnedByName: string;
    returnedByRole: string;
  }
): Promise<void> => {
  try {
    const allocation = await getAllocationById(allocationId);
    if (!allocation) {
      throw new Error('Allocation not found');
    }

    const actualReturnDate = returnData.actualReturnDate || new Date().toISOString();
    
    // Determine status based on condition
    let status: Allocation['status'] = 'returned';
    if (returnData.condition === 'poor') {
      status = 'damaged';
    }

    // Calculate actual cost if different from expected
    let actualCost = allocation.totalCost || 0;
    if (allocation.rateType && allocation.expectedReturnDate) {
      const actualDays = Math.ceil(
        (new Date(actualReturnDate).getTime() - new Date(allocation.allocationDate).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      let rate = 0;
      switch (allocation.rateType) {
        case 'daily':
          rate = allocation.dailyRate || 0;
          actualCost = rate * actualDays;
          break;
        case 'weekly':
          rate = allocation.weeklyRate || 0;
          actualCost = rate * Math.ceil(actualDays / 7);
          break;
        case 'monthly':
          rate = allocation.monthlyRate || 0;
          actualCost = rate * Math.ceil(actualDays / 30);
          break;
        case 'custom':
          actualCost = allocation.customRate || 0;
          break;
      }
    }

    // Update allocation
    const updateData = {
      status,
      condition: returnData.condition,
      returnNotes: returnData.returnNotes,
      actualReturnDate,
      totalCost: actualCost,
      updatedAt: new Date().toISOString(),
    };

    await updateDoc(doc(db, 'allocations', allocationId), updateData);

    // Update expense if cost changed
    if (allocation.expenseId && actualCost !== allocation.totalCost) {
      const { updateExpense } = await import('./expense-service');
      await updateExpense(allocation.expenseId, {
        amount: actualCost,
        description: `${allocation.itemName} allocation to ${allocation.allocatedToName} (Returned: ${returnData.condition})`,
        notes: returnData.returnNotes,
      });
    }

    console.log('Allocation returned successfully');
  } catch (error) {
    console.error('Error returning allocation:', error);
    throw error;
  }
};

// Update allocation
export const updateAllocation = async (allocationId: string, updates: Partial<Allocation>): Promise<void> => {
  try {
    const docRef = doc(db, 'allocations', allocationId);

    // Filter out undefined values to prevent Firestore errors
    const cleanedUpdates: Record<string, any> = {};
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedUpdates[key] = value;
      }
    });

    const updateData = {
      ...cleanedUpdates,
      updatedAt: new Date().toISOString(),
    };

    await updateDoc(docRef, updateData);
    console.log('Allocation updated successfully');
  } catch (error) {
    console.error('Error updating allocation:', error);
    throw error;
  }
};

// Delete allocation
export const deleteAllocation = async (allocationId: string): Promise<void> => {
  try {
    const allocation = await getAllocationById(allocationId);
    if (allocation?.expenseId) {
      // Delete related expense
      const { deleteExpense } = await import('./expense-service');
      await deleteExpense(allocation.expenseId);
    }

    const docRef = doc(db, 'allocations', allocationId);
    await deleteDoc(docRef);
    console.log('Allocation deleted successfully');
  } catch (error) {
    console.error('Error deleting allocation:', error);
    throw error;
  }
};

// Get allocation summary for a farm
export const getAllocationSummary = async (
  farmId: string,
  dateFrom?: string,
  dateTo?: string
): Promise<AllocationSummary> => {
  try {
    let q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId)
    );

    if (dateFrom) {
      q = query(q, where('allocationDate', '>=', dateFrom));
    }

    if (dateTo) {
      q = query(q, where('allocationDate', '<=', dateTo));
    }

    const querySnapshot = await getDocs(q);
    const allocations: Allocation[] = [];

    querySnapshot.forEach((doc) => {
      allocations.push({ id: doc.id, ...doc.data() } as Allocation);
    });

    // Calculate summary
    const summary: AllocationSummary = {
      totalAllocations: allocations.length,
      activeAllocations: 0,
      returnedAllocations: 0,
      overdueAllocations: 0,
      inventoryAllocations: 0,
      machineryAllocations: 0,
      userBreakdown: {},
      itemBreakdown: {},
      statusBreakdown: {},
      monthlyTrend: [],
      returnRate: 0,
      averageAllocationDuration: 0,
      totalCost: 0,
      topUsers: [],
      topItems: [],
    };

    const monthlyData: { [key: string]: number } = {};
    let totalDuration = 0;
    let returnedCount = 0;

    allocations.forEach((allocation) => {
      const allocationDate = new Date(allocation.allocationDate);
      const monthKey = `${allocationDate.getFullYear()}-${(allocationDate.getMonth() + 1).toString().padStart(2, '0')}`;

      // Status counts
      if (allocation.status === 'allocated') {
        summary.activeAllocations++;

        // Check if overdue
        if (allocation.expectedReturnDate && new Date(allocation.expectedReturnDate) < new Date()) {
          summary.overdueAllocations++;
        }
      } else if (allocation.status === 'returned') {
        summary.returnedAllocations++;
        returnedCount++;
      }

      // Type counts
      if (allocation.type === 'inventory') {
        summary.inventoryAllocations++;
      } else {
        summary.machineryAllocations++;
      }

      // Status breakdown
      if (!summary.statusBreakdown[allocation.status]) {
        summary.statusBreakdown[allocation.status] = 0;
      }
      summary.statusBreakdown[allocation.status]++;

      // User breakdown
      if (!summary.userBreakdown[allocation.allocatedTo]) {
        summary.userBreakdown[allocation.allocatedTo] = {
          count: 0,
          userName: allocation.allocatedToName,
          userRole: allocation.allocatedToRole,
        };
      }
      summary.userBreakdown[allocation.allocatedTo].count++;

      // Item breakdown
      if (!summary.itemBreakdown[allocation.itemId]) {
        summary.itemBreakdown[allocation.itemId] = {
          count: 0,
          itemName: allocation.itemName,
          type: allocation.type,
        };
      }
      summary.itemBreakdown[allocation.itemId].count++;

      // Monthly trend
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = 0;
      }
      monthlyData[monthKey]++;

      // Total cost
      if (allocation.totalCost) {
        summary.totalCost += allocation.totalCost;
      }

      // Duration calculation
      if (allocation.actualReturnDate) {
        const duration = Math.ceil(
          (new Date(allocation.actualReturnDate).getTime() - new Date(allocation.allocationDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        totalDuration += duration;
      } else if (allocation.expectedReturnDate) {
        const duration = Math.ceil(
          (new Date(allocation.expectedReturnDate).getTime() - new Date(allocation.allocationDate).getTime()) / (1000 * 60 * 60 * 24)
        );
        totalDuration += duration;
      }
    });

    // Calculate rates and averages
    summary.returnRate = summary.totalAllocations > 0 ? (returnedCount / summary.totalAllocations) * 100 : 0;
    summary.averageAllocationDuration = summary.totalAllocations > 0 ? totalDuration / summary.totalAllocations : 0;

    // Convert monthly data to trend array
    summary.monthlyTrend = Object.entries(monthlyData)
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => a.month.localeCompare(b.month));

    // Top users
    summary.topUsers = Object.entries(summary.userBreakdown)
      .map(([userId, data]) => ({
        userId,
        userName: data.userName,
        count: data.count,
        percentage: (data.count / summary.totalAllocations) * 100,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Top items
    summary.topItems = Object.entries(summary.itemBreakdown)
      .map(([itemId, data]) => ({
        itemId,
        itemName: data.itemName,
        count: data.count,
        percentage: (data.count / summary.totalAllocations) * 100,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return summary;
  } catch (error) {
    console.error('Error getting allocation summary:', error);
    throw error;
  }
};

// Check for overdue allocations and update status
export const updateOverdueAllocations = async (farmId: string): Promise<void> => {
  try {
    const q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId),
      where('status', '==', 'allocated')
    );

    const querySnapshot = await getDocs(q);
    const now = new Date();

    const updatePromises: Promise<void>[] = [];

    querySnapshot.forEach((doc) => {
      const allocation = { id: doc.id, ...doc.data() } as Allocation;

      if (allocation.expectedReturnDate && new Date(allocation.expectedReturnDate) < now) {
        updatePromises.push(
          updateDoc(doc.ref, {
            status: 'overdue',
            updatedAt: new Date().toISOString()
          })
        );
      }
    });

    await Promise.all(updatePromises);
    console.log('Overdue allocations updated');
  } catch (error) {
    console.error('Error updating overdue allocations:', error);
    throw error;
  }
};

// ===== ALLOCATION REQUEST FUNCTIONS =====

// Create allocation request
export const createAllocationRequest = async (requestData: Omit<AllocationRequest, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const now = new Date().toISOString();

    // Remove undefined values to prevent Firestore errors
    const cleanedData: Record<string, any> = {};
    Object.entries(requestData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedData[key] = value;
      }
    });

    const request: Omit<AllocationRequest, 'id'> = {
      ...cleanedData,
      status: 'pending',
      createdAt: now,
      updatedAt: now,
    } as Omit<AllocationRequest, 'id'>;

    const docRef = await addDoc(collection(db, 'allocationRequests'), request);
    console.log('Allocation request created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating allocation request:', error);
    throw error;
  }
};

// Get allocation requests by farm
export const getAllocationRequestsByFarm = async (
  farmId: string,
  filter?: { status?: string; requestedBy?: string; requestedTo?: string },
  limit?: number
): Promise<AllocationRequest[]> => {
  try {
    let q = query(
      collection(db, 'allocationRequests'),
      where('farmId', '==', farmId),
      orderBy('createdAt', 'desc')
    );

    if (filter?.status && filter.status !== 'all') {
      q = query(q, where('status', '==', filter.status));
    }

    if (filter?.requestedBy) {
      q = query(q, where('requestedBy', '==', filter.requestedBy));
    }

    if (filter?.requestedTo) {
      q = query(q, where('requestedTo', '==', filter.requestedTo));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const requests: AllocationRequest[] = [];

    querySnapshot.forEach((doc) => {
      requests.push({ id: doc.id, ...doc.data() } as AllocationRequest);
    });

    return requests;
  } catch (error) {
    console.error('Error getting allocation requests:', error);
    throw error;
  }
};

// Approve allocation request
export const approveAllocationRequest = async (
  requestId: string,
  approvedBy: string,
  approvedByName: string,
  approvedByRole: string
): Promise<void> => {
  try {
    const requestRef = doc(db, 'allocationRequests', requestId);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      throw new Error('Allocation request not found');
    }

    const requestData = { id: requestDoc.id, ...requestDoc.data() } as AllocationRequest;

    // Update request status
    await updateDoc(requestRef, {
      status: 'approved',
      approvedBy,
      approvedByName,
      approvedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // Create allocation with validated data
    const allocationData = {
      farmId: requestData.farmId,
      farmName: requestData.farmName,
      type: requestData.type,
      itemId: requestData.itemId || `temp_${Date.now()}`, // Provide fallback for itemId
      itemName: requestData.itemName || 'Unknown Item',
      quantity: requestData.requestedQuantity || 1,
      unit: requestData.unit,
      returnedQuantity: 0,
      remainingQuantity: requestData.requestedQuantity || 1,
      allocatedTo: requestData.requestedBy,
      allocatedToName: requestData.requestedByName || 'Unknown User',
      allocatedToRole: requestData.requestedByRole || 'unknown',
      allocatedBy: approvedBy,
      allocatedByName: approvedByName,
      allocatedByRole: approvedByRole,
      allocationDate: new Date().toISOString(),
      expectedReturnDate: requestData.expectedReturnDate,
      allocationRequestId: requestId,
      allocationNotes: `Allocated from request: ${requestData.reason || 'No reason provided'}`,
    };

    const allocationId = await createAllocation(allocationData);

    // Update request with allocation ID
    await updateDoc(requestRef, { allocationId });

    console.log('Allocation request approved and allocation created');
  } catch (error) {
    console.error('Error approving allocation request:', error);
    throw error;
  }
};

// Reject allocation request
export const rejectAllocationRequest = async (
  requestId: string,
  rejectedBy: string,
  rejectedByName: string,
  rejectionReason: string
): Promise<void> => {
  try {
    const requestRef = doc(db, 'allocationRequests', requestId);

    await updateDoc(requestRef, {
      status: 'rejected',
      rejectedBy,
      rejectedByName,
      rejectedAt: new Date().toISOString(),
      rejectionReason,
      updatedAt: new Date().toISOString(),
    });

    console.log('Allocation request rejected');
  } catch (error) {
    console.error('Error rejecting allocation request:', error);
    throw error;
  }
};

// ===== RETURN REQUEST FUNCTIONS =====

// Create return request
export const createReturnRequest = async (returnData: Omit<ReturnRequest, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const now = new Date().toISOString();

    // Remove undefined values to prevent Firestore errors
    const cleanedData: Record<string, any> = {};
    Object.entries(returnData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedData[key] = value;
      }
    });

    const request: Omit<ReturnRequest, 'id'> = {
      ...cleanedData,
      status: 'pending',
      createdAt: now,
      updatedAt: now,
    } as Omit<ReturnRequest, 'id'>;

    const docRef = await addDoc(collection(db, 'returnRequests'), request);
    console.log('Return request created with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating return request:', error);
    throw error;
  }
};

// Get return requests by farm
export const getReturnRequestsByFarm = async (
  farmId: string,
  filter?: { status?: string; requestedBy?: string; requestedTo?: string },
  limit?: number
): Promise<ReturnRequest[]> => {
  try {
    let q = query(
      collection(db, 'returnRequests'),
      where('farmId', '==', farmId),
      orderBy('createdAt', 'desc')
    );

    if (filter?.status && filter.status !== 'all') {
      q = query(q, where('status', '==', filter.status));
    }

    if (filter?.requestedBy) {
      q = query(q, where('requestedBy', '==', filter.requestedBy));
    }

    if (filter?.requestedTo) {
      q = query(q, where('requestedTo', '==', filter.requestedTo));
    }

    if (limit) {
      q = query(q, firestoreLimit(limit));
    }

    const querySnapshot = await getDocs(q);
    const requests: ReturnRequest[] = [];

    querySnapshot.forEach((doc) => {
      requests.push({ id: doc.id, ...doc.data() } as ReturnRequest);
    });

    return requests;
  } catch (error) {
    console.error('Error getting return requests:', error);
    throw error;
  }
};

// Approve return request
export const approveReturnRequest = async (
  requestId: string,
  approvedBy: string,
  approvedByName: string,
  approvedByRole: string
): Promise<void> => {
  try {
    const requestRef = doc(db, 'returnRequests', requestId);
    const requestDoc = await getDoc(requestRef);

    if (!requestDoc.exists()) {
      throw new Error('Return request not found');
    }

    const returnData = { id: requestDoc.id, ...requestDoc.data() } as ReturnRequest;

    // Get the allocation
    const allocation = await getAllocationById(returnData.allocationId);
    if (!allocation) {
      throw new Error('Related allocation not found');
    }

    // Calculate new quantities
    const newReturnedQuantity = (allocation.returnedQuantity || 0) + returnData.returnQuantity;
    const newRemainingQuantity = (allocation.quantity || 0) - newReturnedQuantity;

    // Determine new status
    let newStatus: Allocation['status'] = 'allocated';
    if (newRemainingQuantity <= 0) {
      newStatus = 'returned';
    } else if (newReturnedQuantity > 0) {
      newStatus = 'partially_returned';
    }

    // Update allocation
    await updateAllocation(returnData.allocationId, {
      returnedQuantity: newReturnedQuantity,
      remainingQuantity: newRemainingQuantity,
      status: newStatus,
      actualReturnDate: newStatus === 'returned' ? new Date().toISOString() : allocation.actualReturnDate,
      condition: returnData.condition,
      returnNotes: returnData.returnNotes,
    });

    // Update return request
    await updateDoc(requestRef, {
      status: 'approved',
      approvedBy,
      approvedByName,
      approvedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // Recalculate expenses if there's an expense associated
    if (allocation.expenseId && allocation.totalCost) {
      try {
        const { updateExpense } = await import('./expense-service');

        // Calculate the proportion of cost to deduct
        const returnProportion = returnData.returnQuantity / (allocation.quantity || 1);
        const costToDeduct = allocation.totalCost * returnProportion;
        const newTotalCost = allocation.totalCost - costToDeduct;

        await updateExpense(allocation.expenseId, {
          amount: newTotalCost,
          description: `${allocation.itemName} allocation to ${allocation.allocatedToName} (Partial return: ${returnData.returnQuantity} ${returnData.unit || 'units'})`,
          notes: `Cost adjusted for returned items. Original: Rs ${allocation.totalCost}, Returned: Rs ${costToDeduct}, New: Rs ${newTotalCost}`,
        });

        // Update allocation with new cost
        await updateAllocation(returnData.allocationId, {
          totalCost: newTotalCost,
        });
      } catch (expenseError) {
        console.error('Error updating expense for return:', expenseError);
        // Don't fail the return approval if expense update fails
      }
    }

    console.log('Return request approved and allocation updated');
  } catch (error) {
    console.error('Error approving return request:', error);
    throw error;
  }
};

// Reject return request
export const rejectReturnRequest = async (
  requestId: string,
  rejectedBy: string,
  rejectedByName: string,
  rejectionReason: string
): Promise<void> => {
  try {
    const requestRef = doc(db, 'returnRequests', requestId);

    await updateDoc(requestRef, {
      status: 'rejected',
      rejectedBy,
      rejectedByName,
      rejectedAt: new Date().toISOString(),
      rejectionReason,
      updatedAt: new Date().toISOString(),
    });

    console.log('Return request rejected');
  } catch (error) {
    console.error('Error rejecting return request:', error);
    throw error;
  }
};

// Get allocations by user (for user allocation screen)
export const getAllocationsByUser = async (
  farmId: string,
  userId: string,
  includeReturned: boolean = true
): Promise<Allocation[]> => {
  try {
    let q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId),
      where('allocatedTo', '==', userId),
      orderBy('allocationDate', 'desc')
    );

    if (!includeReturned) {
      q = query(q, where('status', '!=', 'returned'));
    }

    const querySnapshot = await getDocs(q);
    const allocations: Allocation[] = [];

    querySnapshot.forEach((doc) => {
      allocations.push({ id: doc.id, ...doc.data() } as Allocation);
    });

    return allocations;
  } catch (error) {
    console.error('Error getting user allocations:', error);
    throw error;
  }
};

// Get allocations by request ID
export const getAllocationsByRequestId = async (
  farmId: string,
  requestId: string
): Promise<Allocation[]> => {
  try {
    const q = query(
      collection(db, 'allocations'),
      where('farmId', '==', farmId),
      where('requestId', '==', requestId)
    );

    const querySnapshot = await getDocs(q);
    const allocations: Allocation[] = [];

    querySnapshot.forEach((doc) => {
      allocations.push({ id: doc.id, ...doc.data() } as Allocation);
    });

    return allocations;
  } catch (error) {
    console.error('Error getting allocations by request ID:', error);
    throw error;
  }
};

// Get return data for allocations
export const getReturnDataForAllocations = async (
  farmId: string,
  allocationIds: string[]
): Promise<Map<string, ReturnRequest[]>> => {
  try {
    const returnDataMap = new Map<string, ReturnRequest[]>();

    if (allocationIds.length === 0) {
      return returnDataMap;
    }

    // Get all approved return requests for these allocations from returnRequests collection
    const q = query(
      collection(db, 'returnRequests'),
      where('farmId', '==', farmId),
      where('allocationId', 'in', allocationIds.slice(0, 10)), // Firestore limit
      where('status', '==', 'approved')
    );

    const querySnapshot = await getDocs(q);

    querySnapshot.forEach((doc) => {
      const returnRequest = { id: doc.id, ...doc.data() } as ReturnRequest;
      const allocationId = returnRequest.allocationId;

      if (!returnDataMap.has(allocationId)) {
        returnDataMap.set(allocationId, []);
      }
      returnDataMap.get(allocationId)!.push(returnRequest);
    });

    // If we have more than 10 allocations, we need to make additional queries
    if (allocationIds.length > 10) {
      for (let i = 10; i < allocationIds.length; i += 10) {
        const batch = allocationIds.slice(i, i + 10);
        const batchQuery = query(
          collection(db, 'returnRequests'),
          where('farmId', '==', farmId),
          where('allocationId', 'in', batch),
          where('status', '==', 'approved')
        );

        const batchSnapshot = await getDocs(batchQuery);
        batchSnapshot.forEach((doc) => {
          const returnRequest = { id: doc.id, ...doc.data() } as ReturnRequest;
          const allocationId = returnRequest.allocationId;

          if (!returnDataMap.has(allocationId)) {
            returnDataMap.set(allocationId, []);
          }
          returnDataMap.get(allocationId)!.push(returnRequest);
        });
      }
    }

    // ALSO CHECK MACHINERY RETURNS - they are stored separately
    try {
      const machineryReturnsQuery = query(
        collection(db, 'farms', farmId, 'machineryReturns'),
        where('returnStatus', '==', 'approved')
      );

      const machineryReturnsSnapshot = await getDocs(machineryReturnsQuery);

      // Get all allocations to match with machinery returns
      const allocationsQuery = query(
        collection(db, 'allocations'),
        where('farmId', '==', farmId),
        where('type', '==', 'machinery')
      );

      const allocationsSnapshot = await getDocs(allocationsQuery);
      const allocationsMap = new Map<string, string>(); // requestId -> allocationId

      allocationsSnapshot.forEach((doc) => {
        const allocation = doc.data();
        if (allocation.requestId) {
          allocationsMap.set(allocation.requestId, doc.id);
        }
      });

      machineryReturnsSnapshot.forEach((doc) => {
        const machineryReturn = { id: doc.id, ...doc.data() } as any; // MachineryReturn type

        console.log('Found machinery return:', machineryReturn.machineryName, machineryReturn.returnStatus);

        // Find the corresponding allocation ID
        const allocationId = allocationsMap.get(machineryReturn.requestId);

        if (allocationId) {
          console.log('Matched machinery return to allocation:', allocationId);

          // Convert machinery return to ReturnRequest format for consistency
          const returnRequest: ReturnRequest = {
            id: doc.id,
            farmId: farmId,
            farmName: machineryReturn.farmName || '',
            allocationId: allocationId, // Use the actual allocation ID
            type: 'machinery',
            itemId: machineryReturn.machineryId,
            itemName: machineryReturn.machineryName,
            returnQuantity: 1, // Machinery is always quantity 1
            returnReason: 'Machinery return',
            condition: machineryReturn.returnCondition === 'working' ? 'excellent' :
                      machineryReturn.returnCondition === 'minor_issue' ? 'good' : 'poor',
            returnNotes: machineryReturn.remarks,
            requestedBy: machineryReturn.caretakerId,
            requestedByName: machineryReturn.caretakerName,
            requestedByRole: 'caretaker',
            requestedTo: machineryReturn.reviewedBy || '',
            requestedToName: machineryReturn.reviewedByName || '',
            requestedToRole: 'admin',
            status: 'approved',
            approvedBy: machineryReturn.reviewedBy,
            approvedByName: machineryReturn.reviewedByName,
            approvedAt: machineryReturn.reviewedAt,
            createdAt: machineryReturn.createdAt,
            updatedAt: machineryReturn.updatedAt
          };

          if (!returnDataMap.has(allocationId)) {
            returnDataMap.set(allocationId, []);
          }
          returnDataMap.get(allocationId)!.push(returnRequest);
        } else {
          console.warn('No allocation found for machinery return requestId:', machineryReturn.requestId);
        }
      });
    } catch (machineryError) {
      console.error('Error fetching machinery returns:', machineryError);
      // Don't throw, just log the error and continue
    }

    // ALSO CHECK INVENTORY RETURNS - they are stored separately like machinery returns
    try {
      const inventoryReturnsQuery = query(
        collection(db, 'farms', farmId, 'inventoryReturns'),
        where('returnStatus', '==', 'approved')
      );

      const inventoryReturnsSnapshot = await getDocs(inventoryReturnsQuery);

      // Get all allocations to match with inventory returns
      const inventoryAllocationsQuery = query(
        collection(db, 'allocations'),
        where('farmId', '==', farmId),
        where('type', '==', 'inventory')
      );

      const inventoryAllocationsSnapshot = await getDocs(inventoryAllocationsQuery);
      const inventoryAllocationsMap = new Map<string, string>(); // requestId -> allocationId

      inventoryAllocationsSnapshot.forEach((doc) => {
        const allocation = doc.data();
        if (allocation.requestId) {
          inventoryAllocationsMap.set(allocation.requestId, doc.id);
        }
      });

      inventoryReturnsSnapshot.forEach((doc) => {
        const inventoryReturn = { id: doc.id, ...doc.data() } as any; // InventoryReturn type

        console.log('Found inventory return:', inventoryReturn.itemName, inventoryReturn.returnStatus);

        // Find the corresponding allocation ID
        const allocationId = inventoryAllocationsMap.get(inventoryReturn.requestId);

        if (allocationId) {
          console.log('Matched inventory return to allocation:', allocationId);

          // Convert inventory return to ReturnRequest format for consistency
          const returnRequest: ReturnRequest = {
            id: doc.id,
            farmId: farmId,
            farmName: inventoryReturn.farmName || '',
            allocationId: allocationId, // Use the actual allocation ID
            type: 'inventory',
            itemId: inventoryReturn.itemId || '',
            itemName: inventoryReturn.itemName,
            returnQuantity: inventoryReturn.quantityReturned || 0,
            unit: '', // Unit not stored in inventory returns
            returnReason: 'Inventory return',
            condition: inventoryReturn.condition || 'good',
            returnNotes: inventoryReturn.remarks,
            requestedBy: inventoryReturn.caretakerId,
            requestedByName: inventoryReturn.caretakerName,
            requestedByRole: 'caretaker',
            requestedTo: inventoryReturn.reviewedBy || '',
            requestedToName: inventoryReturn.reviewedByName || '',
            requestedToRole: 'admin',
            status: 'approved',
            approvedBy: inventoryReturn.reviewedBy,
            approvedByName: inventoryReturn.reviewedByName,
            approvedAt: inventoryReturn.reviewedAt,
            createdAt: inventoryReturn.createdAt,
            updatedAt: inventoryReturn.updatedAt
          };

          if (!returnDataMap.has(allocationId)) {
            returnDataMap.set(allocationId, []);
          }
          returnDataMap.get(allocationId)!.push(returnRequest);
        } else {
          console.warn('No allocation found for inventory return requestId:', inventoryReturn.requestId);
        }
      });
    } catch (inventoryError) {
      console.error('Error fetching inventory returns:', inventoryError);
      // Don't throw, just log the error and continue
    }

    console.log('Return data map size:', returnDataMap.size);
    return returnDataMap;
  } catch (error) {
    console.error('Error getting return data for allocations:', error);
    throw error;
  }
};
