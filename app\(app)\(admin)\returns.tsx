import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, ActivityIndicator, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { InventoryReturn, getInventoryReturns } from "@/services/request-service";
import { MachineryReturn, getMachineryReturns } from "@/services/machinery-service";
import { Search, Package, Filter, CheckCircle, XCircle, Clock, Truck, ArrowLeft } from "lucide-react-native";
import { FilterButton } from "@/components/FilterButton";

interface ReturnCardProps {
  return: InventoryReturn | MachineryReturn;
  onPress: (returnItem: InventoryReturn | MachineryReturn) => void;
  isDarkMode: boolean;
  type: 'inventory' | 'machinery';
}

const ReturnCard: React.FC<ReturnCardProps> = ({ return: returnItem, onPress, isDarkMode, type }) => {
  const { colors } = useTheme();
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'approved':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} color={getStatusColor(status)} />;
      case 'approved':
        return <CheckCircle size={16} color={getStatusColor(status)} />;
      case 'rejected':
        return <XCircle size={16} color={getStatusColor(status)} />;
      default:
        return null;
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'good':
      case 'working':
        return '#4CAF50';
      case 'used':
      case 'minor_issue':
        return '#FF9800';
      case 'damaged':
      case 'needs_repair':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const cardColor = isDarkMode ? '#1e1e1e' : '#ffffff';
  const textColor = isDarkMode ? '#ffffff' : '#333333';
  const secondaryTextColor = isDarkMode ? '#aaaaaa' : '#666666';
  const borderColor = isDarkMode ? '#333333' : '#e0e0e0';

  const getItemName = () => {
    if (type === 'machinery') {
      return (returnItem as MachineryReturn).machineryName;
    }
    return (returnItem as InventoryReturn).itemName;
  };

  const getSubtitle = () => {
    if (type === 'machinery') {
      const machineryReturn = returnItem as MachineryReturn;
      return `${machineryReturn.hoursUsed ? `${machineryReturn.hoursUsed}h used` : 'Usage time not specified'}`;
    }
    const inventoryReturn = returnItem as InventoryReturn;
    return `${inventoryReturn.quantityReturned} returned of ${inventoryReturn.quantityApproved} approved`;
  };

  const getCondition = () => {
    if (type === 'machinery') {
      return (returnItem as MachineryReturn).returnCondition;
    }
    return (returnItem as InventoryReturn).condition;
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          backgroundColor: cardColor,
          borderColor: borderColor,
        },
      ]}
      onPress={() => onPress(returnItem)}
    >
      <View style={styles.cardHeader}>
        <View style={styles.typeIconContainer}>
          {type === 'machinery' ? (
            <Truck size={20} color={colors.primary} />
          ) : (
            <Package size={20} color={colors.primary} />
          )}
        </View>
        
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: textColor }]}>
            {getItemName()}
          </Text>
          
          <Text style={[styles.subtitle, { color: secondaryTextColor }]}>
            {getSubtitle()}
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(returnItem.returnStatus) + '20' }]}>
            {getStatusIcon(returnItem.returnStatus)}
            <Text style={[styles.statusText, { color: getStatusColor(returnItem.returnStatus) }]}>
              {returnItem.returnStatus.charAt(0).toUpperCase() + returnItem.returnStatus.slice(1)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={[styles.cardBody, { borderTopColor: borderColor }]}>
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
            Returned by:
          </Text>
          <Text style={[styles.infoValue, { color: textColor }]}>
            {returnItem.caretakerName}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
            Condition:
          </Text>
          <View style={styles.conditionContainer}>
            <View
              style={[
                styles.conditionIndicator,
                { backgroundColor: getConditionColor(getCondition() || 'good') },
              ]}
            />
            <Text style={[styles.infoValue, { color: getConditionColor(getCondition() || 'good') }]}>
              {(getCondition() || 'good').charAt(0).toUpperCase() + (getCondition() || 'good').slice(1).replace('_', ' ')}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
            Returned on:
          </Text>
          <Text style={[styles.infoValue, { color: textColor }]}>
            {formatDate(returnItem.returnedAt)}
          </Text>
        </View>
        
        {returnItem.remarks && (
          <View style={styles.remarksContainer}>
            <Text style={[styles.remarksLabel, { color: secondaryTextColor }]}>
              Remarks:
            </Text>
            <Text style={[styles.remarksText, { color: textColor }]} numberOfLines={2}>
              {returnItem.remarks}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default function AdminReturnsScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  
  const [inventoryReturns, setInventoryReturns] = useState<InventoryReturn[]>([]);
  const [machineryReturns, setMachineryReturns] = useState<MachineryReturn[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [activeTab, setActiveTab] = useState<'inventory' | 'machinery'>('inventory');
  
  const statusFilterButtons = [
    { key: "status-all", label: "All", value: "all", activeColor: colors.primary },
    { key: "status-pending", label: "Pending", value: "pending", activeColor: "#FF9800" },
    { key: "status-approved", label: "Approved", value: "approved", activeColor: "#4CAF50" },
    { key: "status-rejected", label: "Rejected", value: "rejected", activeColor: "#F44336" },
  ];
  
  useEffect(() => {
    if (selectedFarm) {
      loadReturns();
    }
  }, [selectedFarm]);
  
  const loadReturns = async () => {
    try {
      setLoading(true);
      if (!selectedFarm) {
        setInventoryReturns([]);
        setMachineryReturns([]);
        return;
      }
      
      const [fetchedInventoryReturns, fetchedMachineryReturns] = await Promise.all([
        getInventoryReturns(selectedFarm.id),
        getMachineryReturns(selectedFarm.id)
      ]);
      
      setInventoryReturns(fetchedInventoryReturns);
      setMachineryReturns(fetchedMachineryReturns);
    } catch (error) {
      console.error("Error loading returns:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const getFilteredReturns = () => {
    let returns: (InventoryReturn | MachineryReturn)[] = [];
    
    if (activeTab === 'inventory') {
      returns = inventoryReturns;
    } else {
      returns = machineryReturns;
    }
    
    // Apply status filter
    if (statusFilter !== "all") {
      returns = returns.filter((returnItem) => returnItem.returnStatus === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      returns = returns.filter(
        (returnItem) => {
          const itemName = 'itemName' in returnItem ? returnItem.itemName : (returnItem as MachineryReturn).machineryName;
          const condition = 'condition' in returnItem ? returnItem.condition : (returnItem as MachineryReturn).returnCondition;
          return (
            itemName.toLowerCase().includes(query) ||
            returnItem.caretakerName.toLowerCase().includes(query) ||
            (condition && condition.toLowerCase().includes(query)) ||
            (returnItem.remarks && returnItem.remarks.toLowerCase().includes(query))
          );
        }
      );
    }
    
    // Sort by creation date
    returns.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return returns;
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadReturns();
  };
  
  const handleReturnPress = (returnItem: InventoryReturn | MachineryReturn) => {
    if ('itemName' in returnItem) {
      // Inventory return
      router.push(`/return/${returnItem.id}`);
    } else {
      // Machinery return
      router.push(`/machinery/return/${returnItem.id}`);
    }
  };
  
  const renderEmptyList = () => {
    if (loading) return null;
    
    if (!selectedFarm) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No Farm Selected
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            Please select a farm to view returns
          </Text>
        </View>
      );
    }
    
    const emptyIcon = activeTab === 'inventory' ? Package : Truck;
    const emptyTitle = activeTab === 'inventory' ? 'No Inventory Returns' : 'No Machinery Returns';
    const emptySubtitle = activeTab === 'inventory' 
      ? 'Inventory returns from caretakers will appear here'
      : 'Machinery returns from caretakers will appear here';
    
    return (
      <View style={styles.emptyContainer}>
        {React.createElement(emptyIcon, { size: 64, color: colors.textSecondary })}
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all"
            ? "No Returns Found" 
            : emptyTitle}
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all"
            ? "Try adjusting your search or filters" 
            : emptySubtitle}
        </Text>
      </View>
    );
  };
  
  const filteredReturns = getFilteredReturns();
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Returns",
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.push("/(app)/(admin)")}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'inventory' && { borderBottomColor: colors.primary }
          ]}
          onPress={() => setActiveTab('inventory')}
        >
          <Package size={20} color={activeTab === 'inventory' ? colors.primary : colors.textSecondary} />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'inventory' ? colors.primary : colors.textSecondary }
          ]}>
            Inventory ({inventoryReturns.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'machinery' && { borderBottomColor: colors.primary }
          ]}
          onPress={() => setActiveTab('machinery')}
        >
          <Truck size={20} color={activeTab === 'machinery' ? colors.primary : colors.textSecondary} />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'machinery' ? colors.primary : colors.textSecondary }
          ]}>
            Machinery ({machineryReturns.length})
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Search size={20} color={colors.textSecondary} />
        <TextInput
          style={[
            styles.searchInput, 
            { color: colors.text, textAlign: isRTL ? 'right' : 'left' }
          ]}
          placeholder="Search returns..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <View style={[styles.filtersContainer, isRTL && styles.rtlFiltersContainer]}>
        <Text style={[styles.filtersLabel, { color: colors.textSecondary }]}>
          <Filter size={14} color={colors.textSecondary} /> Filter:
        </Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {statusFilterButtons.map((button) => (
            <FilterButton
              key={button.key}
              label={button.label}
              active={statusFilter === button.value}
              onPress={() => setStatusFilter(button.value)}
              activeColor={button.activeColor}
              isDarkMode={colors.background === "#121212"}
            />
          ))}
        </ScrollView>
      </View>
      
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredReturns}
          keyExtractor={(item) => `${activeTab}-${item.id}`}
          renderItem={({ item }) => (
            <ReturnCard
              return={item}
              onPress={handleReturnPress}
              isDarkMode={colors.background === "#121212"}
              type={activeTab}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    gap: 8,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    zIndex: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  filtersContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  rtlFiltersContainer: {
    flexDirection: "row-reverse",
  },
  filtersLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  filtersScrollContent: {
    paddingRight: 16,
    flexDirection: "row",
    gap: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  typeIconContainer: {
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  statusContainer: {
    marginLeft: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  cardBody: {
    padding: 16,
    borderTopWidth: 1,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  conditionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  conditionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  remarksContainer: {
    marginTop: 8,
  },
  remarksLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  remarksText: {
    fontSize: 14,
    lineHeight: 20,
  },
});