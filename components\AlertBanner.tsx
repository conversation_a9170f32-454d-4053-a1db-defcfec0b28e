import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useLanguage } from '@/context/language-context';

interface AlertBannerProps {
  message?: string;
  title?: string;
  type?: 'warning' | 'info' | 'success' | 'error';
  icon?: React.ReactNode;
  onPress?: () => void;
  isDarkMode?: boolean;
}

export const AlertBanner: React.FC<AlertBannerProps> = ({
  message,
  title,
  type = 'warning',
  icon,
  onPress,
  isDarkMode = false,
}) => {
  const { t } = useLanguage();
  
  // Define colors based on type
  const getColors = () => {
    if (isDarkMode) {
      switch (type) {
        case 'warning':
          return { bg: '#423520', text: '#FFC107', border: '#664D03' };
        case 'info':
          return { bg: '#1A2F42', text: '#0DCAF0', border: '#055160' };
        case 'success':
          return { bg: '#1F352A', text: '#4CAF50', border: '#0F5132' };
        case 'error':
          return { bg: '#422020', text: '#F44336', border: '#842029' };
        default:
          return { bg: '#423520', text: '#FFC107', border: '#664D03' };
      }
    } else {
      switch (type) {
        case 'warning':
          return { bg: '#FFF3CD', text: '#664D03', border: '#FFECB5' };
        case 'info':
          return { bg: '#CFF4FC', text: '#055160', border: '#B6EFFB' };
        case 'success':
          return { bg: '#D1E7DD', text: '#0F5132', border: '#BADBCC' };
        case 'error':
          return { bg: '#F8D7DA', text: '#842029', border: '#F5C2C7' };
        default:
          return { bg: '#FFF3CD', text: '#664D03', border: '#FFECB5' };
      }
    }
  };

  const { bg, text, border } = getColors();

  // Default icon based on type if none provided
  const defaultIcon = () => {
    switch (type) {
      case 'warning':
        return <AlertTriangle size={20} color={text} />;
      case 'info':
        return <AlertTriangle size={20} color={text} />;
      case 'success':
        return <AlertTriangle size={20} color={text} />;
      case 'error':
        return <AlertTriangle size={20} color={text} />;
      default:
        return <AlertTriangle size={20} color={text} />;
    }
  };

  // Get translated type name if no title provided
  const getDefaultTitle = () => {
    switch (type) {
      case 'warning':
        return t('common.warning');
      case 'info':
        return t('common.info');
      case 'success':
        return t('common.success');
      case 'error':
        return t('common.error');
      default:
        return t('common.warning');
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: bg,
          borderColor: border,
        },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          {icon || defaultIcon()}
        </View>
        <View style={styles.textContainer}>
          {(title || !message) && (
            <Text style={[styles.title, { color: text }]}>
              {title || getDefaultTitle()}
            </Text>
          )}
          {message && <Text style={[styles.message, { color: text }]}>{message}</Text>}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 12,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
  },
});