import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { Request, getRequestById } from "@/services/request-service";
import { createMachineryReturn, updateMachineryStatus } from "@/services/machinery-service";
import { ArrowLeft, Truck, AlertCircle, Wrench, Fuel, Clock } from "lucide-react-native";

export default function MachineryReturnScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  const { requestId, farmId } = useLocalSearchParams<{ requestId: string; farmId: string }>();
  
  const [request, setRequest] = useState<Request | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // Form state
  const [returnCondition, setReturnCondition] = useState<"working" | "minor_issue" | "needs_repair">("working");
  const [hoursUsed, setHoursUsed] = useState("");
  const [fuelUsed, setFuelUsed] = useState("");
  const [odometerReading, setOdometerReading] = useState("");
  const [remarks, setRemarks] = useState("");
  
  const getConditionOptions = () => {
    const isMaintenanceRequest = request?.requestSubType === 'maintenance';

    return [
      {
        value: "working",
        label: "Working",
        color: "#4CAF50",
        description: isMaintenanceRequest
          ? "Maintenance completed successfully, machinery is ready for use"
          : "Machinery is in good working condition"
      },
      {
        value: "minor_issue",
        label: "Minor Issue",
        color: "#FF9800",
        description: isMaintenanceRequest
          ? "Maintenance completed but minor issues remain"
          : "Small issues that don't affect operation"
      },
      {
        value: "needs_repair",
        label: "Needs Repair",
        color: "#F44336",
        description: isMaintenanceRequest
          ? "Maintenance incomplete, requires additional work"
          : "Requires maintenance before next use"
      },
    ];
  };
  
  useEffect(() => {
    if (requestId && farmId) {
      loadRequest();
    }
  }, [requestId, farmId]);
  
  const loadRequest = async () => {
    try {
      setLoading(true);
      const requestData = await getRequestById(requestId, farmId);
      if (requestData) {
        setRequest(requestData);
      } else {
        Alert.alert("Error", "Request not found");
        router.back();
      }
    } catch (error) {
      console.error("Error loading request:", error);
      Alert.alert("Error", "Failed to load request");
      router.back();
    } finally {
      setLoading(false);
    }
  };
  
  const handleSubmit = async () => {
    if (!request || !user || !selectedFarm) {
      Alert.alert("Error", "Missing required information");
      return;
    }
    
    // Validation
    if (hoursUsed && (isNaN(Number(hoursUsed)) || Number(hoursUsed) < 0)) {
      Alert.alert("Error", "Please enter a valid number of hours used");
      return;
    }
    
    if (fuelUsed && (isNaN(Number(fuelUsed)) || Number(fuelUsed) < 0)) {
      Alert.alert("Error", "Please enter a valid fuel amount");
      return;
    }
    
    if (odometerReading && (isNaN(Number(odometerReading)) || Number(odometerReading) < 0)) {
      Alert.alert("Error", "Please enter a valid odometer reading");
      return;
    }
    
    try {
      setSubmitting(true);
      
      await createMachineryReturn({
        requestId: request.id,
        machineryId: request.machineryId || "",
        machineryName: request.machineryName || request.itemName,
        caretakerId: user.uid,
        caretakerName: user.displayName || user.name || "Unknown",
        farmId: selectedFarm.id,
        farmName: selectedFarm.name,
        type: request.requestSubType as 'use' | 'maintenance', // Pass the request type
        returnCondition,
        hoursUsed: hoursUsed ? Number(hoursUsed) : undefined,
        fuelUsed: fuelUsed ? Number(fuelUsed) : undefined,
        odometerReading: odometerReading ? Number(odometerReading) : undefined,
        remarks: remarks.trim() || undefined,
      });
      
      Alert.alert(
        "Success",
        "Machinery return submitted successfully",
        [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error("Error submitting return:", error);
      Alert.alert("Error", "Failed to submit return request. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Return Machinery",
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading request...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (!request) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: "Return Machinery",
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.errorContainer}>
          <AlertCircle size={64} color={colors.textSecondary} />
          <Text style={[styles.errorTitle, { color: colors.text }]}>Request Not Found</Text>
          <Text style={[styles.errorSubtitle, { color: colors.textSecondary }]}>
            The machinery request you're trying to return could not be found.
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Return Machinery",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={styles.headerBackButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Request Info */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.sectionHeader}>
            <Truck size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Machinery Information</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Machinery:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{request.machineryName || request.itemName}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Type:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{request.machineryType || "N/A"}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Request Type:</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>
              {request.requestSubType === 'maintenance' ? 'For Maintenance' : 'For Use'}
            </Text>
          </View>
          
          {request.startDate && (
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Start Date:</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {new Date(request.startDate).toLocaleDateString()}
              </Text>
            </View>
          )}
          
          {request.endDate && (
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>End Date:</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {new Date(request.endDate).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>
        
        {/* Return Form */}
        <View style={[styles.section, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Return Details</Text>
          
          {/* Return Condition */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              Condition <Text style={styles.required}>*</Text>
            </Text>
            <View style={styles.conditionOptions}>
              {getConditionOptions().map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.conditionOption,
                    {
                      backgroundColor: returnCondition === option.value ? option.color + "20" : colors.background,
                      borderColor: returnCondition === option.value ? option.color : colors.border,
                    },
                  ]}
                  onPress={() => setReturnCondition(option.value as "working" | "minor_issue" | "needs_repair")}
                >
                  <View style={styles.conditionHeader}>
                    <View
                      style={[
                        styles.conditionIndicator,
                        { backgroundColor: option.color },
                      ]}
                    />
                    <Text
                      style={[
                        styles.conditionLabel,
                        {
                          color: returnCondition === option.value ? option.color : colors.text,
                          fontWeight: returnCondition === option.value ? "600" : "normal",
                        },
                      ]}
                    >
                      {option.label}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.conditionDescription,
                      { color: colors.textSecondary },
                    ]}
                  >
                    {option.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Usage/Maintenance Details */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              <Clock size={16} color={colors.text} />
              {request?.requestSubType === 'maintenance' ? ' Hours Worked (Optional)' : ' Hours Used (Optional)'}
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                },
              ]}
              placeholder={request?.requestSubType === 'maintenance' ? "Enter hours worked on maintenance" : "Enter hours used"}
              placeholderTextColor={colors.textSecondary}
              value={hoursUsed}
              onChangeText={setHoursUsed}
              keyboardType="numeric"
            />
          </View>

          {request?.requestSubType !== 'maintenance' && (
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                <Fuel size={16} color={colors.text} /> Fuel Used (Optional)
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    color: colors.text,
                  },
                ]}
                placeholder="Enter fuel used (liters)"
                placeholderTextColor={colors.textSecondary}
                value={fuelUsed}
                onChangeText={setFuelUsed}
                keyboardType="numeric"
              />
            </View>
          )}
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              <Wrench size={16} color={colors.text} /> Odometer Reading (Optional)
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                },
              ]}
              placeholder="Enter current odometer reading"
              placeholderTextColor={colors.textSecondary}
              value={odometerReading}
              onChangeText={setOdometerReading}
              keyboardType="numeric"
            />
          </View>
          
          {/* Remarks */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {request?.requestSubType === 'maintenance' ? 'Maintenance Notes (Optional)' : 'Remarks (Optional)'}
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                  color: colors.text,
                },
              ]}
              placeholder={
                request?.requestSubType === 'maintenance'
                  ? "Describe maintenance work completed, parts replaced, or issues found..."
                  : "Add any additional notes about the machinery return..."
              }
              placeholderTextColor={colors.textSecondary}
              value={remarks}
              onChangeText={setRemarks}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>
        
        {/* Info Note */}
        <View style={[styles.infoNote, { backgroundColor: colors.primary + "10", borderColor: colors.primary + "30" }]}>
          <AlertCircle size={16} color={colors.primary} />
          <Text style={[styles.infoNoteText, { color: colors.primary }]}>
            Your return request will be reviewed by the admin. The machinery status will be updated based on the condition you report.
          </Text>
        </View>
      </ScrollView>
      
      {/* Submit Button */}
      <View style={[styles.footer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: colors.primary,
              opacity: submitting ? 0.7 : 1,
            },
          ]}
          onPress={handleSubmit}
          disabled={submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>Submit Return Request</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  errorSubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  headerBackButton: {
    padding: 8,
    marginLeft: -8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  required: {
    color: "#F44336",
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 100,
  },
  conditionOptions: {
    gap: 12,
  },
  conditionOption: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
  },
  conditionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 12,
  },
  conditionIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  conditionLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  conditionDescription: {
    fontSize: 14,
    marginLeft: 24,
  },
  infoNote: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
    gap: 12,
  },
  infoNoteText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});