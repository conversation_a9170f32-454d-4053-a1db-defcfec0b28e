import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, Stack } from 'expo-router';
import { useTheme } from '@/context/theme-context';
import { useLanguage } from '@/context/language-context';
import { useFarm } from '@/context/farm-context';
import { useAuth } from '@/context/auth-context';
import ModalDropdown from '@/components/ModalDropdown';
import {
  ArrowLeft,
  Camera,
  Upload,
  Calendar,
  ChevronDown,
  Truck,
  AlertTriangle,
  X,
  Save,
  Package,
  Info
} from 'lucide-react-native';
import { createInventoryItem } from '@/services/inventory-service';
import { uploadInventoryImage } from '@/services/storage-service';
import * as ImagePicker from 'expo-image-picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { LinearGradient } from 'expo-linear-gradient';
import { getCategories, getUnits } from '@/services/settings-service';

// Helper function for category icons
const getCategoryIcon = (category: string) => {
  const icons: { [key: string]: string } = {
    'Seeds': '🌱',
    'Fertilizers': '🧪',
    'Tools': '🔧',
    'Equipment': '⚙️',
    'Pesticides': '🚫',
    'Feed': '🌾',
    'Medicine': '💊',
    'Vaccination': '💉',
    'Other': '📦'
  };
  return icons[category] || '📦';
};

export default function AddInventoryItem() {
  const router = useRouter();
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === 'dark';
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [units, setUnits] = useState<string[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    customCategory: '',
    quantity: '',
    unit: 'pcs',
    customUnit: '',
    minQuantity: '',
    supplier: '',
    unitPrice: '',
    totalAmount: '',
    purchaseDate: null as Date | null,
    expiryDate: null as Date | null,
    isConsumable: false,
    description: '',
    imageUrl: '',
  });
  
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showPurchaseDatePicker, setShowPurchaseDatePicker] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Calculate total price and update total amount field
  const calculateTotalPrice = () => {
    const quantity = Number(formData.quantity) || 0;
    const unitPrice = Number(formData.unitPrice) || 0;
    return quantity * unitPrice;
  };

  // Update total amount whenever quantity or unit price changes
  useEffect(() => {
    const totalAmount = calculateTotalPrice();
    setFormData(prev => ({ ...prev, totalAmount: totalAmount.toString() }));
  }, [formData.quantity, formData.unitPrice]);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const [categoriesData, unitsData] = await Promise.all([
        getCategories(),
        getUnits()
      ]);
      setCategories(categoriesData);
      setUnits(unitsData);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.name.trim()) {
      newErrors.name = t('inventory.validation.nameRequired');
    }
    
    if (!formData.category && !formData.customCategory.trim()) {
      newErrors.category = t('inventory.validation.categoryRequired');
    }
    
    if (!formData.quantity.trim()) {
      newErrors.quantity = t('inventory.validation.quantityRequired');
    } else if (isNaN(Number(formData.quantity)) || Number(formData.quantity) <= 0) {
      newErrors.quantity = t('inventory.validation.quantityInvalid');
    }
    
    if (formData.minQuantity && (isNaN(Number(formData.minQuantity)) || Number(formData.minQuantity) < 0)) {
      newErrors.minQuantity = t('inventory.validation.minQuantityInvalid');
    }

    // Make unit price mandatory
    if (!formData.unitPrice || isNaN(Number(formData.unitPrice)) || Number(formData.unitPrice) <= 0) {
      newErrors.unitPrice = t('inventory.validation.priceRequired');
    }

    // Make purchase date mandatory
    if (!formData.purchaseDate) {
      newErrors.purchaseDate = t('inventory.validation.purchaseDateRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (!selectedFarm) {
      Alert.alert(t('common.error'), t('farm.noFarmSelected'));
      return;
    }

    try {
      setSubmitting(true);

      let imageUrl = formData.imageUrl;

      // Upload image to Firebase Storage if a local image is selected
      if (formData.imageUrl && formData.imageUrl.startsWith('file://')) {
        console.log("Uploading inventory image to Storage...");
        const tempItemId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        imageUrl = await uploadInventoryImage(formData.imageUrl, tempItemId);
        console.log("Inventory image uploaded:", imageUrl);
      }

      const inventoryData = {
        name: formData.name.trim(),
        category: formData.customCategory.trim() || formData.category,
        quantity: Number(formData.quantity),
        unit: formData.customUnit.trim() || formData.unit,
        minQuantity: formData.minQuantity ? Number(formData.minQuantity) : 0,
        supplier: formData.supplier.trim(),
        price: formData.unitPrice ? Number(formData.unitPrice) : 0, // Store unit price in price field
        purchaseDate: formData.purchaseDate,
        expiryDate: formData.expiryDate,
        isConsumable: formData.isConsumable,
        description: formData.description.trim(),
        imageUrl: imageUrl,
        farmId: selectedFarm.id,
        createdBy: user?.uid || '',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await createInventoryItem(inventoryData, user?.uid || '');
      
      Alert.alert(
        t('common.success'),
        t('inventory.itemCreated'),
        [
          {
            text: t('common.ok'),
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      console.error('Error creating inventory item:', error);
      Alert.alert(t('common.error'), t('inventory.createError'));
    } finally {
      setSubmitting(false);
    }
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('common.error'), t('inventory.cameraPermissionRequired'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setFormData(prev => ({ ...prev, imageUrl: result.assets[0].uri }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(t('common.error'), t('inventory.imagePickError'));
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, expiryDate: selectedDate }));
    }
  };

  const handlePurchaseDateChange = (event: any, selectedDate?: Date) => {
    setShowPurchaseDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, purchaseDate: selectedDate }));
      if (errors.purchaseDate) setErrors(prev => ({ ...prev, purchaseDate: '' }));
    }
  };

  const textColor = isDarkMode ? '#FFFFFF' : '#000000';
  const secondaryTextColor = isDarkMode ? '#B0B0B0' : '#666666';
  const backgroundColor = isDarkMode ? '#121212' : '#F5F5F5';
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const borderColor = isDarkMode ? '#333333' : '#E0E0E0';

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <Stack.Screen
          options={{
            title: t('inventory.addItem'),
            headerStyle: { backgroundColor: colors.primary },
            headerTintColor: '#fff',
            headerTitleStyle: { fontWeight: 'bold' },
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <Stack.Screen
        options={{
          title: t('inventory.addItem'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: '#fff',
          headerTitleStyle: { fontWeight: 'bold' },
        }}
      />
      
      <KeyboardAvoidingView 
        style={styles.flex1} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Basic Information Section */}
          <LinearGradient
            colors={isDarkMode ? ['#1e1e1e', '#2a2a2a'] : ['#ffffff', '#f8f9fa']}
            style={[styles.section, { borderColor }]}
          >
            <View style={styles.sectionHeader}>
              <Package size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                {t('inventory.basicInformation')}
              </Text>
            </View>

            {/* Item Name */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.itemName')} *
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    color: textColor,
                    backgroundColor: cardColor,
                    borderColor: errors.name ? '#F44336' : borderColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}
                value={formData.name}
                onChangeText={(text) => {
                  setFormData(prev => ({ ...prev, name: text }));
                  if (errors.name) setErrors(prev => ({ ...prev, name: '' }));
                }}
                placeholder={t('inventory.enterItemName')}
                placeholderTextColor={secondaryTextColor}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
            </View>

            {/* Category */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.category')} *
              </Text>
              <ModalDropdown
                options={categories.map(cat => ({
                  label: t(`inventory.categories.${cat.toLowerCase()}`) || cat,
                  value: cat,
                  icon: getCategoryIcon(cat)
                }))}
                selectedValue={formData.category}
                onSelect={(value) => {
                  setFormData(prev => ({ ...prev, category: value, customCategory: '' }));
                  if (errors.category) setErrors(prev => ({ ...prev, category: '' }));
                }}
                placeholder={t('inventory.selectCategory')}
                error={!!errors.category}
              />

              {/* Custom Category Input */}
              {!formData.category && (
                <View style={styles.customInputContainer}>
                  <Text style={[styles.customLabel, { color: secondaryTextColor }]}>
                    {t('inventory.orEnterCustomCategory')}
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: textColor,
                        backgroundColor: cardColor,
                        borderColor: errors.category ? '#F44336' : borderColor,
                        textAlign: isRTL ? 'right' : 'left'
                      }
                    ]}
                    value={formData.customCategory}
                    onChangeText={(text) => {
                      setFormData(prev => ({ ...prev, customCategory: text }));
                      if (errors.category) setErrors(prev => ({ ...prev, category: '' }));
                    }}
                    placeholder={t('inventory.enterCustomCategory')}
                    placeholderTextColor={secondaryTextColor}
                  />
                </View>
              )}
              {errors.category && (
                <Text style={styles.errorText}>{errors.category}</Text>
              )}
            </View>

            {/* Quantity and Unit */}
            <View style={styles.rowContainer}>
              <View style={[styles.inputGroup, styles.flex1]}>
                <Text style={[styles.label, { color: textColor }]}>
                  {t('inventory.quantity')} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: textColor,
                      backgroundColor: cardColor,
                      borderColor: errors.quantity ? '#F44336' : borderColor,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}
                  value={formData.quantity}
                  onChangeText={(text) => {
                    setFormData(prev => ({ ...prev, quantity: text }));
                    if (errors.quantity) setErrors(prev => ({ ...prev, quantity: '' }));
                  }}
                  placeholder={t('inventory.enterQuantity')}
                  placeholderTextColor={secondaryTextColor}
                  keyboardType="numeric"
                />
                {errors.quantity && (
                  <Text style={styles.errorText}>{errors.quantity}</Text>
                )}
              </View>

              <View style={[styles.inputGroup, styles.flex1, { marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                <Text style={[styles.label, { color: textColor }]}>
                  {t('inventory.unit')}
                </Text>
                <ModalDropdown
                  options={units.map(unit => ({
                    label: t(`inventory.units.${unit.toLowerCase()}`) || unit,
                    value: unit
                  }))}
                  selectedValue={formData.unit}
                  onSelect={(value) => {
                    setFormData(prev => ({ ...prev, unit: value, customUnit: '' }));
                  }}
                  placeholder={t('inventory.selectUnit')}
                />

                {/* Custom Unit Input */}
                {!formData.unit && (
                  <TextInput
                    style={[
                      styles.input,
                      {
                        color: textColor,
                        backgroundColor: cardColor,
                        borderColor: borderColor,
                        textAlign: isRTL ? 'right' : 'left',
                        marginTop: 8
                      }
                    ]}
                    value={formData.customUnit}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, customUnit: text }))}
                    placeholder={t('inventory.enterCustomUnit')}
                    placeholderTextColor={secondaryTextColor}
                  />
                )}
              </View>
            </View>
          </LinearGradient>

          {/* Additional Details Section */}
          <LinearGradient
            colors={isDarkMode ? ['#1e1e1e', '#2a2a2a'] : ['#ffffff', '#f8f9fa']}
            style={[styles.section, { borderColor }]}
          >
            <View style={styles.sectionHeader}>
              <Info size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                {t('inventory.additionalDetails')}
              </Text>
            </View>

            {/* Minimum Quantity */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.minimumQuantity')}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    color: textColor,
                    backgroundColor: cardColor,
                    borderColor: errors.minQuantity ? '#F44336' : borderColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}
                value={formData.minQuantity}
                onChangeText={(text) => {
                  setFormData(prev => ({ ...prev, minQuantity: text }));
                  if (errors.minQuantity) setErrors(prev => ({ ...prev, minQuantity: '' }));
                }}
                placeholder={t('inventory.enterMinQuantity')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
              {errors.minQuantity && (
                <Text style={styles.errorText}>{errors.minQuantity}</Text>
              )}
            </View>

            {/* Supplier */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.supplier')}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    color: textColor,
                    backgroundColor: cardColor,
                    borderColor: borderColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}
                value={formData.supplier}
                onChangeText={(text) => setFormData(prev => ({ ...prev, supplier: text }))}
                placeholder={t('inventory.enterSupplier')}
                placeholderTextColor={secondaryTextColor}
              />
            </View>

            {/* Unit Price */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.unitPrice')} *
              </Text>
              <View style={styles.inputContainer}>
                <Package size={20} color={secondaryTextColor} />
                <TextInput
                  style={[
                    styles.input,
                    {
                      color: textColor,
                      backgroundColor: cardColor,
                      borderColor: errors.unitPrice ? '#F44336' : borderColor,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}
                  value={formData.unitPrice}
                  onChangeText={(text) => {
                    setFormData(prev => ({ ...prev, unitPrice: text }));
                    if (errors.unitPrice) setErrors(prev => ({ ...prev, unitPrice: '' }));
                  }}
                  placeholder="Rs 0"
                  placeholderTextColor={secondaryTextColor}
                  keyboardType="numeric"
                />
              </View>
              {errors.unitPrice && (
                <Text style={styles.errorText}>{errors.unitPrice}</Text>
              )}
            </View>

            {/* Total Amount (Auto-calculated, Disabled) */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.totalAmount')}
              </Text>
              <View style={styles.inputContainer}>
                <Package size={20} color={secondaryTextColor} />
                <TextInput
                  style={[
                    styles.input,
                    styles.disabledInput,
                    {
                      color: secondaryTextColor,
                      backgroundColor: isDarkMode ? '#2a2a2a' : '#f0f0f0',
                      borderColor: borderColor,
                      textAlign: isRTL ? 'right' : 'left'
                    }
                  ]}
                  value={formData.totalAmount ? `Rs ${Number(formData.totalAmount).toLocaleString()}` : 'Rs 0'}
                  editable={false}
                  placeholder="Rs 0"
                  placeholderTextColor={secondaryTextColor}
                />
              </View>
            </View>

            {/* Purchase Date */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.purchaseDate')} *
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  {
                    backgroundColor: cardColor,
                    borderColor: errors.purchaseDate ? '#F44336' : borderColor
                  }
                ]}
                onPress={() => setShowPurchaseDatePicker(true)}
              >
                <Calendar size={20} color={secondaryTextColor} />
                <Text style={[
                  styles.dateButtonText,
                  { color: formData.purchaseDate ? textColor : secondaryTextColor }
                ]}>
                  {formData.purchaseDate
                    ? formData.purchaseDate.toLocaleDateString()
                    : t('inventory.selectPurchaseDate')
                  }
                </Text>
                {formData.purchaseDate && (
                  <TouchableOpacity
                    onPress={() => setFormData(prev => ({ ...prev, purchaseDate: null }))}
                    style={styles.clearDateButton}
                  >
                    <X size={16} color={secondaryTextColor} />
                  </TouchableOpacity>
                )}
              </TouchableOpacity>
              {errors.purchaseDate && (
                <Text style={styles.errorText}>{errors.purchaseDate}</Text>
              )}
            </View>

            {/* Expiry Date */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.expiryDate')}
              </Text>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  { backgroundColor: cardColor, borderColor }
                ]}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={20} color={secondaryTextColor} />
                <Text style={[
                  styles.dateButtonText,
                  { color: formData.expiryDate ? textColor : secondaryTextColor }
                ]}>
                  {formData.expiryDate
                    ? formData.expiryDate.toLocaleDateString()
                    : t('inventory.selectExpiryDate')
                  }
                </Text>
                {formData.expiryDate && (
                  <TouchableOpacity
                    onPress={() => setFormData(prev => ({ ...prev, expiryDate: null }))}
                    style={styles.clearDateButton}
                  >
                    <X size={16} color={secondaryTextColor} />
                  </TouchableOpacity>
                )}
              </TouchableOpacity>
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.description')}
              </Text>
              <TextInput
                style={[
                  styles.textArea,
                  {
                    color: textColor,
                    backgroundColor: cardColor,
                    borderColor: borderColor,
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}
                value={formData.description}
                onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                placeholder={t('inventory.enterDescription')}
                placeholderTextColor={secondaryTextColor}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Consumable Checkbox */}
            <View style={styles.inputGroup}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setFormData(prev => ({ ...prev, isConsumable: !prev.isConsumable }))}
              >
                <View style={[
                  styles.checkbox,
                  {
                    backgroundColor: formData.isConsumable ? colors.primary : 'transparent',
                    borderColor: formData.isConsumable ? colors.primary : borderColor
                  }
                ]}>
                  {formData.isConsumable && (
                    <Text style={styles.checkmark}>✓</Text>
                  )}
                </View>
                <Text style={[styles.checkboxLabel, { color: textColor }]}>
                  {t('inventory.isConsumable')}
                </Text>
              </TouchableOpacity>
              <Text style={[styles.checkboxDescription, { color: secondaryTextColor }]}>
                {t('inventory.consumableDescription')}
              </Text>
            </View>

            {/* Image Upload */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t('inventory.itemImage')}
              </Text>
              {formData.imageUrl ? (
                <View style={styles.imageContainer}>
                  <Image source={{ uri: formData.imageUrl }} style={styles.selectedImage} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => setFormData(prev => ({ ...prev, imageUrl: '' }))}
                  >
                    <X size={16} color="#fff" />
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={[styles.imageUploadButton, { backgroundColor: cardColor, borderColor }]}
                  onPress={pickImage}
                >
                  <Upload size={24} color={colors.primary} />
                  <Text style={[styles.imageUploadText, { color: colors.primary }]}>
                    {t('inventory.uploadImage')}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </LinearGradient>

          {showDatePicker && (
            <DateTimePicker
              value={formData.expiryDate || new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}

          {showPurchaseDatePicker && (
            <DateTimePicker
              value={formData.purchaseDate || new Date()}
              mode="date"
              display="default"
              onChange={handlePurchaseDateChange}
              maximumDate={new Date()}
            />
          )}
        </ScrollView>
        
        {/* Submit Button */}
        <View style={[styles.submitContainer, { backgroundColor: cardColor, borderTopColor: borderColor }]}>
          <TouchableOpacity
            style={[styles.submitButton, { backgroundColor: colors.primary }]}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <>
                <Save size={20} color="#fff" />
                <Text style={styles.submitButtonText}>
                  {t('inventory.createItem')}
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flex1: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  submitContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    borderTopWidth: 1,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 100,
  },
  errorText: {
    color: '#F44336',
    fontSize: 12,
    marginTop: 4,
  },
  rowContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  flex1: {
    flex: 1,
  },
  customInputContainer: {
    marginTop: 8,
  },
  customLabel: {
    fontSize: 12,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 25,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
  },
  totalPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 4,
  },
  totalPriceLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalPriceValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  dateButtonText: {
    flex: 1,
    fontSize: 16,
  },
  clearDateButton: {
    padding: 4,
  },
  imageContainer: {
    position: 'relative',
    alignSelf: 'flex-start',
  },
  selectedImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#F44336',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageUploadButton: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    paddingVertical: 40,
    alignItems: 'center',
    gap: 8,
  },
  imageUploadText: {
    fontSize: 16,
    fontWeight: '600',
  },
  disabledInput: {
    opacity: 0.6,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  checkboxDescription: {
    fontSize: 14,
    marginTop: 4,
    fontStyle: 'italic',
  },
});
