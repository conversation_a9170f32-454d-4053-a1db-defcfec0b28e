import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, ActivityIndicator, Alert, Modal, ScrollView, Animated, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { InventoryItemComponent } from "@/components/InventoryItem";
import { InventoryItem, getInventoryItems, deleteInventoryItem, subscribeToInventory } from "@/services/inventory-service";
import { Search, Plus, Package, Grid, List, Filter } from "lucide-react-native";
import { FilterButton } from "@/components/FilterButton";
import { LinearGradient } from "expo-linear-gradient";

export default function AdminInventoryScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm, farms } = useFarm();
  const router = useRouter();
  
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  
  const isDarkMode = colors.background === "#121212";
  const screenWidth = Dimensions.get("window").width;
  
  const statusFilterButtons = [
    { key: "status-all", label: t('inventory.showAll'), value: "all", activeColor: colors.primary },
    { key: "status-low", label: t('inventory.showLowStock'), value: "low", activeColor: "#FF9800" },
    { key: "status-expiring", label: t('inventory.showExpiringSoon'), value: "expiring", activeColor: "#FFC107" },
    { key: "status-expired", label: t('inventory.showExpired'), value: "expired", activeColor: "#F44336" },
  ];
  
  useEffect(() => {
    // If no farm is selected, don't load inventory
    if (!selectedFarm) {
      setInventoryItems([]);
      setFilteredItems([]);
      setLoading(false);
      return;
    }

    // Set up real-time listener for inventory changes
    const unsubscribe = subscribeToInventory(selectedFarm.id, (items) => {
      setInventoryItems(items);
      setLoading(false);
      setRefreshing(false);
    });

    // Clean up listener on unmount
    return () => {
      unsubscribe();
    };
  }, [selectedFarm]);

  useEffect(() => {
    filterItems();
  }, [searchQuery, statusFilter, inventoryItems]);
  
  const filterItems = () => {
    let filtered = [...inventoryItems];
    
    // Apply status filter
    if (statusFilter === "low") {
      filtered = filtered.filter((item) => item.quantity <= (item.minQuantity || 0));
    } else if (statusFilter === "expiring") {
      filtered = filtered.filter((item) => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);
        return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
      });
    } else if (statusFilter === "expired") {
      filtered = filtered.filter((item) => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        const today = new Date();
        return expiryDate < today;
      });
    }
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          (item.name && item.name.toLowerCase().includes(query)) ||
          (item.category && item.category.toLowerCase().includes(query)) ||
          (item.location && item.location.toLowerCase().includes(query)) ||
          (item.supplier && item.supplier.toLowerCase().includes(query))
      );
    }
    
    setFilteredItems(filtered);
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    // The real-time subscription will automatically update the data
    // Just reset the refreshing state after a short delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };
  
  const handleItemPress = (itemId: string) => {
    router.push(`/inventory/${itemId}`);
  };
  
  const openAddItemModal = () => {
    setShowAddItemModal(true);
  };
  
  const closeAddItemModal = () => {
    setShowAddItemModal(false);
  };
  
  const handleAddItem = () => {
    closeAddItemModal();
    router.push("/inventory/edit");
  };
  
  const handleEditItem = (item: InventoryItem) => {
    router.push({
      pathname: "/inventory/edit",
      params: {
        id: item.id,
        name: item.name,
        category: item.category,
        quantity: item.quantity.toString(),
        unit: item.unit,
        minQuantity: item.minQuantity.toString(),
        location: item.location,
        expiryDate: item.expiryDate || "",
        purchaseDate: item.purchaseDate || "",
        description: item.description || "",
        supplier: item.supplier || "",
        price: item.price ? item.price.toString() : "",
        imageUrl: item.imageUrl || "",
      }
    });
  };
  
  const handleDeleteItem = async (itemId: string) => {
    Alert.alert(
      "Confirm Delete",
      "Are you sure you want to delete this item?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: async () => {
            try {
              if (!selectedFarm) {
                Alert.alert("Error", "Please select a farm first");
                return;
              }
              
              await deleteInventoryItem(itemId, selectedFarm.id);
              setInventoryItems((prevItems) => prevItems.filter((item) => item.id !== itemId));
              Alert.alert("Success", "Item deleted successfully");
            } catch (error) {
              console.error("Error deleting item:", error);
              Alert.alert("Error", "Failed to delete item");
            }
          },
          style: "destructive",
        },
      ]
    );
  };

  const getFarmName = (farmId: string) => {
    const farm = farms.find(f => f.id === farmId);
    return farm ? farm.name : "Unknown Farm";
  };
  
  const renderEmptyList = () => {
    if (loading) return null;
    
    return (
      <View style={styles.emptyContainer}>
        <LinearGradient
          colors={isDarkMode ? ["#2a2a2a", "#1e1e1e"] : ["#f8f9fa", "#ffffff"]}
          style={styles.emptyGradient}
        >
          <Package size={80} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {searchQuery.trim() !== "" || statusFilter !== "all"
              ? "No items found"
              : "No inventory items"}
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            {searchQuery.trim() !== "" || statusFilter !== "all"
              ? "Try adjusting your search or filters"
              : "Start by adding your first inventory item"}
          </Text>
          {searchQuery.trim() === "" && statusFilter === "all" && (
            <TouchableOpacity
              style={[styles.emptyButton, { backgroundColor: colors.primary }]}
              onPress={handleAddItem}
            >
              <Plus size={20} color="#fff" />
              <Text style={styles.emptyButtonText}>Add First Item</Text>
            </TouchableOpacity>
          )}
        </LinearGradient>
      </View>
    );
  };
  
  // If no farm is selected, show a message
  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen
          options={{
            title: t("inventory.title"),
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.noFarmContainer}>
          <Package size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No farm selected
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            Please select a farm to view inventory
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("inventory.title"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerRight: () => (
            <View style={styles.headerActions}>
              {/* <TouchableOpacity 
                onPress={() => setViewMode(viewMode === "list" ? "grid" : "list")}
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginRight: 8 }]}
              >
                {viewMode === "list" ? <Grid size={20} color="#fff" /> : <List size={20} color="#fff" />}
              </TouchableOpacity> */}
              <TouchableOpacity
                style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)" }]}
                onPress={openAddItemModal}
              >
                <Plus size={24} color="#fff" />
              </TouchableOpacity>
            </View>
          ),
        }}
      />
      
      {/* Simple Search Section */}
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Search size={20} color={colors.textSecondary} />
        <TextInput
          style={[
            styles.searchInput, 
            { 
              color: colors.text, 
              textAlign: isRTL ? 'right' : 'left'
            }
          ]}
          placeholder="Search by name, category, or supplier..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      {/* Filter Section */}
      <View style={[styles.filtersContainer, isRTL && styles.rtlFiltersContainer]}>
        <View style={styles.filtersHeader}>
          <Text style={[styles.filtersLabel, { color: colors.textSecondary }]}>
            {/* <Filter size={14} color={colors.textSecondary} />  */}
          </Text>
          {/* <Text style={[styles.itemCount, { color: colors.primary }]}>
            {filteredItems.length} items
          </Text> */}
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {statusFilterButtons.map((button) => (
            <FilterButton
              key={button.key}
              label={button.label}
              active={statusFilter === button.value}
              onPress={() => setStatusFilter(button.value)}
              activeColor={button.activeColor}
              isDarkMode={isDarkMode}
            />
          ))}
        </ScrollView>
      </View>
      
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading inventory...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredItems}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <InventoryItemComponent
              item={item}
              onPress={() => handleItemPress(item.id)}
              onEditPress={() => handleEditItem(item)}
              onDeletePress={() => handleDeleteItem(item.id)}
              showEditOptions={true}
              isDarkMode={isDarkMode}
              role="admin"
              farmName={getFarmName(item.location)}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          showsVerticalScrollIndicator={false}
        />
      )}
      
      {/* Add Item Modal */}
      <Modal
        visible={showAddItemModal}
        transparent={true}
        animationType="fade"
        onRequestClose={closeAddItemModal}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add New Item
            </Text>
            <Text style={[styles.modalSubtitle, { color: colors.textSecondary }]}>
              Add a new item to your inventory
            </Text>
            
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.primary }]}
              onPress={handleAddItem}
            >
              <Plus size={20} color="#fff" />
              <Text style={styles.modalButtonText}>Add Item</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.modalCancelButton, { borderColor: colors.border }]}
              onPress={closeAddItemModal}
            >
              <Text style={[styles.modalCancelButtonText, { color: colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      
      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={handleAddItem}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 5,
    marginBottom: 2,
    paddingHorizontal: 16,
    paddingVertical: 1,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 1,
    marginBottom: 8,
  },
  rtlFiltersContainer: {
    flexDirection: "row-reverse",
  },
  filtersHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 1,
  },
  filtersLabel: {
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  itemCount: {
    fontSize: 14,
    fontWeight: "600",
  },
  filtersScrollContent: {
    paddingRight: 16,
    flexDirection: "row",
    gap: 12,
  },
  listContent: {
    padding: 20,
    paddingTop: 0,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: "500",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 40,
  },
  emptyGradient: {
    padding: 32,
    borderRadius: 20,
    alignItems: "center",
    width: "100%",
  },
  noFarmContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  emptyButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  modalContent: {
    width: "100%",
    borderRadius: 12,
    padding: 24,
    borderWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  modalSubtitle: {
    fontSize: 14,
    marginBottom: 24,
    textAlign: "center",
  },
  modalButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  modalButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  modalCancelButton: {
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
  },
  modalCancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1000,
  },
});