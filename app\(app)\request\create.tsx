import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, Alert, ActivityIndicator, KeyboardAvoidingView, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { createRequest } from "@/services/request-service";
import { Package, Info, User, Calendar, FileText } from "lucide-react-native";
import ModalDropdown from "@/components/ModalDropdown";
import { LinearGradient } from "expo-linear-gradient";

export default function CreateRequestScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  
  const [loading, setLoading] = useState(false);
  const [itemName, setItemName] = useState("");
  const [category, setCategory] = useState("");
  const [quantity, setQuantity] = useState("");
  const [unit, setUnit] = useState("pieces");
  const [reason, setReason] = useState("");
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  
  const isCaretaker = user?.role === "caretaker";
  const isOwner = user?.role === "owner";
  
  // Redirect owners away from this page
  useEffect(() => {
    if (isOwner) {
      Alert.alert(
        t("common.notAvailable"),
        t("requests.ownersCannotCreateRequests"),
        [{ text: t("common.ok"), onPress: () => router.back() }]
      );
    }
  }, [isOwner, router]);
  
  // Categories matching add inventory screen
  const CATEGORY_OPTIONS = [
    { key: 'inventory.category_seeds', stored: 'Seeds' },
    { key: 'inventory.category_fertilizers', stored: 'Fertilizers' },
    { key: 'inventory.category_pesticides', stored: 'Pesticides' },
    { key: 'inventory.category_tools', stored: 'Tools' },
    { key: 'inventory.category_equipment', stored: 'Equipment' },
    { key: 'inventory.category_feed', stored: 'Feed' },
    { key: 'inventory.category_medication', stored: 'Medication' },
    { key: 'inventory.category_vaccination', stored: 'Vaccination' },
    { key: 'inventory.category_fuel', stored: 'Fuel' },
    { key: 'inventory.category_other_custom', stored: 'Other (Custom)' },
  ];

  // Units matching add inventory screen
  const UNIT_OPTIONS = [
    { key: 'inventory.unit_kg', stored: 'kg' },
    { key: 'inventory.unit_g', stored: 'g' },
    { key: 'inventory.unit_l', stored: 'L' },
    { key: 'inventory.unit_ml', stored: 'mL' },
    { key: 'inventory.unit_units', stored: 'units' },
    { key: 'inventory.unit_bags', stored: 'bags' },
    { key: 'inventory.unit_boxes', stored: 'boxes' },
    { key: 'inventory.unit_bottles', stored: 'bottles' },
    { key: 'inventory.unit_packs', stored: 'packs' },
    { key: 'inventory.unit_pieces', stored: 'pieces' },
    { key: 'inventory.unit_other_custom', stored: 'Other (Custom)' },
  ];

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!itemName.trim()) {
      newErrors.itemName = t("inventory.itemNameRequired");
    }

    if (!category) {
      newErrors.category = t("inventory.categoryRequired");
    }

    if (!quantity.trim()) {
      newErrors.quantity = t("inventory.validQuantityRequired");
    } else if (isNaN(Number(quantity)) || Number(quantity) <= 0) {
      newErrors.quantity = t("requests.invalidQuantity");
    }

    if (!reason.trim()) {
      newErrors.reason = t("requests.reasonRequired");
    }

    if (!selectedFarm) {
      newErrors.farm = t("common.noFarmSelected");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async () => {
    if (isOwner) {
      Alert.alert(t("common.notAvailable"), t("requests.ownersCannotCreateRequests"));
      return;
    }

    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      
      const requestData = {
        requestType: "inventory" as "inventory" | "existing",
        itemName: itemName.trim(),
        category,
        quantity: Number(quantity),
        unit,
        reason: reason.trim(),
        status: "pending" as "pending" | "approved" | "rejected",
        requestedBy: user?.uid || "",
        requestedByName: user?.displayName || "",
        requestorRole: user?.role || "caretaker",
        farmId: selectedFarm!.id,
        farmName: selectedFarm!.name,
        createdAt: new Date().toISOString(),
      };
      
      await createRequest(requestData);
      
      Alert.alert(
        t("common.success"),
        t("requests.requestCreated"),
        [
          {
            text: t("common.ok"),
            onPress: () => {
              if (isCaretaker) {
                router.push("/(app)/(caretaker)/requests");
              } else {
                router.back();
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error creating request:", error);
      Alert.alert(t("common.error"), t("requests.createError"));
    } finally {
      setLoading(false);
    }
  };
  
  // If user is owner, don't render the form
  if (isOwner) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
        <Stack.Screen
          options={{
            title: t("requests.createRequest"),
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }}
        />
        <View style={styles.emptyContainer}>
          <Package size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {t("requests.ownersCannotCreateRequests")}
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
            {t("requests.ownersCanOnlyApproveReject")}
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>{t("common.back")}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <Stack.Screen
        options={{
          title: t("requests.createRequest"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }}
      />
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Banner */}
          <LinearGradient
            colors={[colors.primary + '20', colors.primary + '10']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.headerBanner}
          >
            <View style={styles.headerContent}>
              <Package size={32} color={colors.primary} />
              <View style={styles.headerText}>
                <Text style={[styles.headerTitle, { color: colors.text }]}>
                  {t("requests.newItemRequest")}
                </Text>
                <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
                  {t("requests.submitRequestDescription")}
                </Text>
              </View>
            </View>
          </LinearGradient>

          {/* Form Card */}
          <View style={[styles.formCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            {/* Item Information Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Package size={20} color={colors.primary} />
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {t("inventory.itemInformation")}
                </Text>
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("inventory.itemName")} *
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { 
                      backgroundColor: colors.background,
                      borderColor: errors.itemName ? colors.error : colors.border,
                      color: colors.text,
                    }
                  ]}
                  placeholder={t("inventory.enterItemName")}
                  placeholderTextColor={colors.textSecondary}
                  value={itemName}
                  onChangeText={(text) => {
                    setItemName(text);
                    if (errors.itemName) {
                      setErrors(prev => ({ ...prev, itemName: "" }));
                    }
                  }}
                />
                {errors.itemName && (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {errors.itemName}
                  </Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("common.category")} *
                </Text>
                <ModalDropdown
                  items={CATEGORY_OPTIONS.map(option => {
                    const translatedLabel = t(option.key);
                    return {
                      label: translatedLabel,
                      value: option.stored,
                    };
                  })}
                  selectedValue={category}
                  onValueChange={(value) => {
                    setCategory(value);
                    if (errors.category) {
                      setErrors(prev => ({ ...prev, category: "" }));
                    }
                  }}
                  placeholder={t("inventory.selectCategory")}
                  error={!!errors.category}
                />
                {errors.category && (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {errors.category}
                  </Text>
                )}
              </View>
            </View>

            {/* Quantity Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <FileText size={20} color={colors.primary} />
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {t("inventory.quantityDetails")}
                </Text>
              </View>

              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 2, marginRight: 12 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("common.quantity")} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      { 
                        backgroundColor: colors.background,
                        borderColor: errors.quantity ? colors.error : colors.border,
                        color: colors.text,
                      }
                    ]}
                    placeholder={t("inventory.enterQuantity")}
                    placeholderTextColor={colors.textSecondary}
                    value={quantity}
                    onChangeText={(text) => {
                      setQuantity(text);
                      if (errors.quantity) {
                        setErrors(prev => ({ ...prev, quantity: "" }));
                      }
                    }}
                    keyboardType="numeric"
                  />
                  {errors.quantity && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.quantity}
                    </Text>
                  )}
                </View>

                <View style={[styles.inputGroup, { flex: 1 }]}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("common.unit")}
                  </Text>
                  <ModalDropdown
                    items={UNIT_OPTIONS.map(option => {
                      const translatedLabel = t(option.key);
                      return {
                        label: translatedLabel,
                        value: option.stored,
                      };
                    })}
                    selectedValue={unit}
                    onValueChange={setUnit}
                    placeholder={t("inventory.selectUnit")}
                  />
                </View>
              </View>
            </View>

            {/* Request Details Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <User size={20} color={colors.primary} />
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {t("requests.requestDetails")}
                </Text>
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {t("requests.reasonForRequest")} *
                </Text>
                <TextInput
                  style={[
                    styles.textArea,
                    { 
                      backgroundColor: colors.background,
                      borderColor: errors.reason ? colors.error : colors.border,
                      color: colors.text,
                    }
                  ]}
                  placeholder={t("requests.explainWhyNeeded")}
                  placeholderTextColor={colors.textSecondary}
                  value={reason}
                  onChangeText={(text) => {
                    setReason(text);
                    if (errors.reason) {
                      setErrors(prev => ({ ...prev, reason: "" }));
                    }
                  }}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
                {errors.reason && (
                  <Text style={[styles.errorText, { color: colors.error }]}>
                    {errors.reason}
                  </Text>
                )}
              </View>

              {/* Farm Info */}
              <View style={[styles.farmInfo, { backgroundColor: colors.primary + '10' }]}>
                <Calendar size={16} color={colors.primary} />
                <Text style={[styles.farmInfoText, { color: colors.text }]}>
                  {t("requests.requestFor")}: {selectedFarm?.name || t("common.unknownFarm")}
                </Text>
              </View>
            </View>

            {/* Info Note */}
            {/* <View style={[styles.infoNote, { backgroundColor: colors.info + '15' }]}>
              <Info size={20} color={colors.info} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                {t("requests.requestNote")}
              </Text>
            </View> */}

            {/* Submit Button */}
            <TouchableOpacity
              style={[
                styles.submitButton, 
                { 
                  backgroundColor: colors.primary,
                  opacity: loading ? 0.7 : 1 
                }
              ]}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.submitButtonText}>{t("requests.submitRequest")}</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  headerBanner: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  formCard: {
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 12,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: "top",
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: "hidden",
  },
  picker: {
    height: 50,
    width: "100%",
  },
  row: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  farmInfo: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  farmInfoText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: "500",
  },
  infoNote: {
    flexDirection: "row",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: "flex-start",
  },
  infoText: {
    marginLeft: 12,
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 22,
  },
  backButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});