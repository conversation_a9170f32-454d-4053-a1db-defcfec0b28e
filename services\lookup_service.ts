import { create } from 'zustand';
// import { collection, getDocs } from 'firebase/firestore';
// import { database } from '@/firebase/config';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  Timestamp,
  getDoc,
  setDoc,
  arrayUnion
} from 'firebase/firestore';
import { db } from '@/services/firebase-config';
import { toCamelCase } from '@/services/utils';
export interface Lookup {
  label: string;
  value: string;
  [key: string]: any; // for other properties
}
 
export interface LookupCategory {
  id: string;
  name: string;
  // other properties
}
 
interface LookupState {
  lookups: { [category: string]: Lookup[] };
  lookupCategories: LookupCategory[];
  isLoading: boolean;
  fetchLookups: () => Promise<void>;
  getLookupsByCategory: (category: string) => Lookup[];
}
 
export const useLookupStore = create<LookupState>((set, get) => ({
  lookups: {},
  lookupCategories: [],
  isLoading: false,
 
  fetchLookups: async () => {
    // Prevent re-fetching if already loading or data exists
    if (get().isLoading || Object.keys(get().lookups).length > 0) {
      return;
    }
 
    set({ isLoading: true });
    try {
      // 1. Fetch all lookup categories from 'lookupCategories' collection
      const categoriesSnapshot = await getDocs(collection(db, 'lookupCategories'));
      const categories = categoriesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as LookupCategory[];
      set({ lookupCategories: categories });
 
      // 2. Fetch all individual lookup items from 'lookups' collection
      const lookupsSnapshot = await getDocs(collection(db, 'lookups'));
      const allLookups = lookupsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
 
      // 3. Group the lookups by their 'category' field
      const groupedLookups: { [category: string]: Lookup[] } = {};
      allLookups.forEach(lookup => {
        const categoryKey = toCamelCase(lookup.categoryName); // e.g., 'taskPriorities', 'taskCategories'
        if (!categoryKey) return;
 
        if (!groupedLookups[categoryKey]) {
          groupedLookups[categoryKey] = [];
        }
        groupedLookups[categoryKey].push(lookup as Lookup);
      });
      const {taskCategory,taskPriority}=groupedLookups
      console.log({ groupedLookups},{taskPriority})
      // console.log({groupedLookups },{categories},"------------------------",{allLookups})
      set({ lookups: groupedLookups, isLoading: false });
    } catch (error) {
      console.error("Error fetching lookups:", error);
      set({ isLoading: false });
    }
  },
 
  getLookupsByCategory: (category: string) => {
    return get().lookups[category] || [];
  },
}));
 
 