import React, { useEffect, useState } from "react";
import { 
  StyleSheet, 
  Text, 
  View, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator, 
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useLocalSearch<PERSON>ara<PERSON>, useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { getInventoryItemById, InventoryItem } from "@/services/inventory-service";
import { getRequestById, updateRequest, Request } from "@/services/request-service";
import { getFarmById } from "@/services/farm-service";
import ModalDropdown from "@/components/ModalDropdown";
import {
  Package,
  Building,
  Tag,
  FileText,
  ArrowLeft,
  Save,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit3,
  Info
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

export default function EditRequestScreen() {
  const { id, farmId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { theme } = useTheme();
  const { t } = useLanguage();
  const isDarkMode = theme === "dark";
  
  const [request, setRequest] = useState<Request | null>(null);
  const [item, setItem] = useState<InventoryItem | null>(null);
  const [farmName, setFarmName] = useState("");
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  const [itemName, setItemName] = useState("");
  const [category, setCategory] = useState("");
  const [quantity, setQuantity] = useState("");
  const [unit, setUnit] = useState("pcs");
  const [reason, setReason] = useState("");
  const [error, setError] = useState("");
  
  // Role-based theme colors
  const getRoleColors = () => {
    switch (user?.role) {
      case 'owner':
        return {
          primary: '#2E7D32',
          secondary: '#4CAF50',
          light: '#E8F5E8',
        };
      case 'admin':
        return {
          primary: '#1976D2',
          secondary: '#2196F3',
          light: '#E3F2FD',
        };
      case 'caretaker':
        return {
          primary: '#FF9800',
          secondary: '#FFC107',
          light: '#FFF3E0',
        };
      default:
        return {
          primary: '#2E7D32',
          secondary: '#4CAF50',
          light: '#E8F5E8',
        };
    }
  };

  // Theme colors based on user role
  const getThemeColors = () => {
    const roleColors = getRoleColors();
    const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8f9fa";
    const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
    const surfaceColor = isDarkMode ? "#252525" : "#f5f5f5";
    const textColor = isDarkMode ? "#ffffff" : "#1a1a1a";
    const textSecondary = isDarkMode ? "#b0b0b0" : "#6b7280";
    const placeholderColor = isDarkMode ? "#888888" : "#9ca3af";
    const borderColor = isDarkMode ? "#2a2a2a" : "#e5e7eb";
    const inputBgColor = isDarkMode ? "#2a2a2a" : "#ffffff";
    const inputBorderColor = isDarkMode ? "#404040" : "#d1d5db";

    const primaryColor = roleColors.primary;
    const primaryLight = isDarkMode ? roleColors.primary + '20' : roleColors.light;
    const inputFocusColor = roleColors.primary;

    const errorColor = isDarkMode ? "#ef4444" : "#dc2626";
    const warningColor = isDarkMode ? "#f59e0b" : "#d97706";
    const successColor = isDarkMode ? "#10b981" : "#059669";
    
    return {
      backgroundColor,
      cardColor,
      surfaceColor,
      textColor,
      textSecondary,
      placeholderColor,
      borderColor,
      inputBgColor,
      inputBorderColor,
      inputFocusColor,
      primaryColor,
      primaryLight,
      errorColor,
      warningColor,
      successColor
    };
  };
  
  const colors = getThemeColors();
  
  // Common units for dropdown
  const units = ["pcs", "kg", "g", "l", "ml", "bags", "boxes", "packets"];
  
  // Common categories for dropdown
  const categories = ["Seeds", "Fertilizer", "Tools", "Equipment", "Pesticides", "Other"];

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        if (!id) {
          Alert.alert(t("common.error"), t("requests.idRequired"));
          router.back();
          return;
        }
        
        const requestId = Array.isArray(id) ? id[0] : id as string;
        
        // Get farmId from params or selectedFarm
        let farmIdToUse;
        if (farmId) {
          farmIdToUse = Array.isArray(farmId) ? farmId[0] : farmId.toString();
        } else if (selectedFarm) {
          farmIdToUse = selectedFarm.id;
        } else {
          Alert.alert(t("common.error"), "Farm ID is required");
          router.back();
          return;
        }
        
        console.log("Loading request for edit:", requestId, "farmId:", farmIdToUse);
        
        const requestData = await getRequestById(requestId, farmIdToUse);
        
        if (!requestData) {
          Alert.alert(t("common.error"), t("requests.notFound"));
          router.back();
          return;
        }
        
        // Check if user can edit this request
        if (requestData.requestedBy !== user?.uid) {
          Alert.alert(t("common.error"), t("requests.cannotEditOthers"));
          router.back();
          return;
        }
        
        // Check if request is pending
        if (requestData.status !== "pending") {
          Alert.alert(t("common.error"), t("requests.canOnlyEditPending"));
          router.back();
          return;
        }
        
        setRequest(requestData);
        setItemName(requestData.itemName);
        setQuantity(requestData.quantity.toString());
        setUnit(requestData.unit);
        setReason(requestData.reason || "");
        setCategory(requestData.category || "");
        
        // Load farm name
        if (requestData.farmId) {
          const farm = await getFarmById(requestData.farmId);
          if (farm) {
            setFarmName(farm.name);
          }
        }
        
        // If itemId is provided and not "new-item", load the item details
        if (requestData.itemId && requestData.itemId !== "new-item") {
          const itemData = await getInventoryItemById(requestData.itemId, requestData.farmId);
          if (itemData) {
            setItem(itemData);
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        Alert.alert(t("common.error"), t("requests.loadError"));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, farmId, selectedFarm, user]);

  const handleSubmit = async () => {
    // Validate inputs
    if (!itemName.trim()) {
      setError(t("requests.itemNameRequired"));
      return;
    }
    
    if (!quantity || parseInt(quantity) <= 0) {
      setError(t("requests.validQuantityRequired"));
      return;
    }
    
    if (item && parseInt(quantity) > item.quantity) {
      setError(t("requests.quantityExceedsAvailable", { available: `${item.quantity} ${item.unit}` }));
      return;
    }
    
    if (!unit) {
      setError(t("requests.unitRequired"));
      return;
    }

    if (!reason.trim()) {
      setError(t("requests.reasonRequired"));
      return;
    }

    if (!request) {
      setError(t("requests.missingData"));
      return;
    }
    
    setError("");
    setSubmitting(true);
    
    try {
      console.log("Updating request:", request.id, "farmId:", request.farmId);
      
      // Update the request
      await updateRequest(request.id, {
        itemName,
        quantity: parseInt(quantity),
        unit,
        reason,
        category: category || request.category,
        notes: reason, // Update notes for better visibility
      }, request.farmId);
      
      Alert.alert(
        t("common.success"), 
        t("requests.requestUpdated"),
        [
          { 
            text: t("common.ok"), 
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error("Error updating request:", error);
      setError(t("requests.updateError"));
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={20} color={colors.warningColor} />;
      case 'approved':
        return <CheckCircle size={20} color={colors.successColor} />;
      case 'rejected':
        return <XCircle size={20} color={colors.errorColor} />;
      default:
        return <Clock size={20} color={colors.textSecondary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return colors.warningColor;
      case 'approved':
        return colors.successColor;
      case 'rejected':
        return colors.errorColor;
      default:
        return colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]}>
        <Stack.Screen options={{
          title: t("requests.editRequest"),
          headerStyle: { backgroundColor: getRoleColors().primary },
          headerTintColor: "#fff",
        }} />
        <View style={styles.loadingContainer}>
          <View style={[styles.loadingCard, { backgroundColor: colors.cardColor }]}>
            <ActivityIndicator size="large" color={getRoleColors().primary} />
            <Text style={[styles.loadingText, { color: colors.textColor }]}>
              {t("common.loading")}
            </Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  const isNewItemRequest = request?.requestType === "inventory";

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]} edges={['top']}>
      <Stack.Screen options={{
        title: t("requests.editRequest"),
        headerStyle: { backgroundColor: getRoleColors().primary },
        headerTintColor: "#fff",
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
            <ArrowLeft size={24} color="#fff" />
          </TouchableOpacity>
        ),
      }} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Enhanced Header Section */}
          <View style={[styles.headerSection, { backgroundColor: colors.cardColor, borderColor: colors.borderColor }]}>
            <LinearGradient
              colors={[colors.primaryColor + '15', colors.primaryColor + '05']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                <View style={styles.headerTop}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.primaryColor + '20' }]}>
                    <Edit3 size={24} color={colors.primaryColor} />
                  </View>
                  
                  <View style={styles.headerInfo}>
                    <Text style={[styles.headerTitle, { color: colors.textColor }]}>
                      {t("requests.editRequest")}
                    </Text>
                    {/* <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
                      {isNewItemRequest 
                        ? t("requests.updateNewItemRequest") 
                        : t("requests.updateExistingItemRequest")}
                    </Text> */}
                  </View>
                  
                  {request && (
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(request.status) + '20' }]}>
                      {getStatusIcon(request.status)}
                      <Text style={[styles.statusText, { color: getStatusColor(request.status) }]}>
                        {t(`requests.${request.status}`)}
                      </Text>
                    </View>
                  )}
                </View>
                
                {farmName && (
                  <View style={styles.farmInfo}>
                    <Building size={16} color={colors.textSecondary} />
                    <Text style={[styles.farmName, { color: colors.textSecondary }]}>
                      {farmName}
                    </Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </View>
          
          {/* Item Information Card */}
          {!isNewItemRequest && item && (
            <View style={[styles.infoCard, { backgroundColor: colors.cardColor, borderColor: colors.borderColor }]}>
              <View style={styles.cardHeader}>
                <View style={[styles.cardIconContainer, { backgroundColor: colors.primaryLight }]}>
                  <Package size={20} color={colors.primaryColor} />
                </View>
                <Text style={[styles.cardTitle, { color: colors.textColor }]}>
                  {t("inventory.itemInformation")}
                </Text>
              </View>
              
              <View style={styles.itemDetails}>
                <Text style={[styles.itemName, { color: colors.textColor }]}>{item.name}</Text>
                
                <View style={styles.detailsGrid}>
                  <View style={styles.detailItem}>
                    <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                      {t("inventory.available")}
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.textColor }]}>
                      {item.quantity} {item.unit}
                    </Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                      {t("inventory.category")}
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.textColor }]}>
                      {item.category}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}
          
          {/* Form Section */}
          <View style={[styles.formCard, { backgroundColor: colors.cardColor, borderColor: colors.borderColor }]}>
            <View style={styles.cardHeader}>
              <View style={[styles.cardIconContainer, { backgroundColor: colors.primaryLight }]}>
                <FileText size={20} color={colors.primaryColor} />
              </View>
              <Text style={[styles.cardTitle, { color: colors.textColor }]}>
                {t("requests.requestDetails")}
              </Text>
            </View>
            
            <View style={styles.formContent}>
              {isNewItemRequest && (
                <>
                  <View style={styles.formGroup}>
                    <View style={styles.labelContainer}>
                      <Tag size={16} color={colors.primaryColor} />
                      <Text style={[styles.label, { color: colors.textColor }]}>
                        {t("requests.itemName")} *
                      </Text>
                    </View>
                    <View style={[
                      styles.inputContainer, 
                      { 
                        backgroundColor: colors.inputBgColor,
                        borderColor: colors.inputBorderColor,
                      }
                    ]}>
                      <TextInput
                        style={[styles.input, { color: colors.textColor }]}
                        value={itemName}
                        onChangeText={setItemName}
                        placeholder={t("requests.enterItemName")}
                        placeholderTextColor={colors.placeholderColor}
                      />
                    </View>
                  </View>
                  
                  <View style={styles.formGroup}>
                    <View style={styles.labelContainer}>
                      <Tag size={16} color={colors.primaryColor} />
                      <Text style={[styles.label, { color: colors.textColor }]}>
                        {t("common.category")}
                      </Text>
                    </View>
                    <ModalDropdown
                      items={categories.map(cat => ({
                        label: cat,
                        value: cat,
                      }))}
                      selectedValue={category}
                      onValueChange={(value) => setCategory(value)}
                      placeholder={t("requests.selectCategory")}
                    />
                  </View>
                </>
              )}
              
              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Package size={16} color={colors.primaryColor} />
                  <Text style={[styles.label, { color: colors.textColor }]}>
                    {t("requests.quantity")} *
                  </Text>
                </View>
                <View style={[
                  styles.inputContainer, 
                  { 
                    backgroundColor: colors.inputBgColor,
                    borderColor: colors.inputBorderColor,
                  }
                ]}>
                  <TextInput
                    style={[styles.input, { color: colors.textColor }]}
                    value={quantity}
                    onChangeText={setQuantity}
                    keyboardType="numeric"
                    placeholder={item 
                      ? t("requests.enterQuantityMax", { max: item.quantity }) 
                      : t("requests.enterQuantity")
                    }
                    placeholderTextColor={colors.placeholderColor}
                  />
                </View>
                {item && (
                  <View style={styles.helperTextContainer}>
                    <Info size={14} color={colors.textSecondary} />
                    <Text style={[styles.helperText, { color: colors.textSecondary }]}>
                      {t("inventory.available")}: {item.quantity} {item.unit}
                    </Text>
                  </View>
                )}
              </View>
              
              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <Package size={16} color={colors.primaryColor} />
                  <Text style={[styles.label, { color: colors.textColor }]}>
                    {t("requests.unit")} *
                  </Text>
                </View>
                <ModalDropdown
                  items={units.map(u => ({
                    label: u,
                    value: u,
                  }))}
                  selectedValue={unit}
                  onValueChange={(value) => setUnit(value)}
                  placeholder={t("common.unit")}
                  disabled={!isNewItemRequest}
                />
                {!isNewItemRequest && (
                  <View style={styles.helperTextContainer}>
                    <Info size={14} color={colors.textSecondary} />
                    <Text style={[styles.helperText, { color: colors.textSecondary }]}>
                      Unit cannot be changed for existing items
                    </Text>
                  </View>
                )}
              </View>
              
              <View style={styles.formGroup}>
                <View style={styles.labelContainer}>
                  <FileText size={16} color={getRoleColors().primary} />
                  <Text style={[styles.label, { color: colors.textColor }]}>
                    {t("requests.reasonForRequest")}
                    <Text style={[styles.required, { color: colors.errorColor }]}> *</Text>
                  </Text>
                </View>
                <View style={[
                  styles.textAreaContainer,
                  {
                    backgroundColor: colors.inputBgColor,
                    borderColor: reason.trim() ? getRoleColors().primary : colors.inputBorderColor,
                  }
                ]}>
                  <TextInput
                    style={[styles.textArea, { color: colors.textColor }]}
                    value={reason}
                    onChangeText={setReason}
                    placeholder={t("requests.explainWhyNeeded")}
                    placeholderTextColor={colors.placeholderColor}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                  />
                </View>
              </View>
            </View>
          </View>
          
          {/* Error Message */}
          {error ? (
            <View style={[styles.errorCard, { backgroundColor: colors.errorColor + '15', borderColor: colors.errorColor + '30' }]}>
              <AlertCircle size={20} color={colors.errorColor} />
              <Text style={[styles.errorText, { color: colors.errorColor }]}>{error}</Text>
            </View>
          ) : null}
          
          {/* Action Buttons */}
          <View style={styles.actionSection}>
            <TouchableOpacity 
              style={[
                styles.cancelButton, 
                { 
                  backgroundColor: colors.surfaceColor,
                  borderColor: colors.borderColor,
                }
              ]} 
              onPress={() => router.back()}
              disabled={submitting}
            >
              <Text style={[styles.cancelButtonText, { color: colors.textColor }]}>
                {t("common.cancel")}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.submitButton,
                {
                  backgroundColor: getRoleColors().primary,
                  opacity: submitting ? 0.7 : 1,
                }
              ]}
              onPress={handleSubmit}
              disabled={submitting}
            >
              {submitting ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <Save size={20} color="#fff" />
                  <Text style={styles.submitButtonText}>{t("common.save")}</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingCard: {
    padding: 32,
    borderRadius: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: "500",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  headerButton: {
    marginLeft: 16,
    padding: 4,
  },
  
  // Header Section
  headerSection: {
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerGradient: {
    padding: 20,
  },
  headerContent: {
    gap: 16,
  },
  headerTop: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  farmInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  farmName: {
    fontSize: 14,
    fontWeight: "500",
  },
  
  // Card Styles
  infoCard: {
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  formCard: {
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingBottom: 16,
    gap: 12,
  },
  cardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  
  // Item Details
  itemDetails: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  itemName: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 16,
  },
  detailsGrid: {
    flexDirection: "row",
    gap: 20,
  },
  detailItem: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 6,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  
  // Form Styles
  formContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  formGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
  },
  inputContainer: {
    borderWidth: 1.5,
    borderRadius: 12,
    overflow: "hidden",
  },
  input: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    minHeight: 50,
  },
  textAreaContainer: {
    borderWidth: 1.5,
    borderRadius: 12,
    overflow: "hidden",
  },
  textArea: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: "top",
  },
  pickerContainer: {
    borderWidth: 1.5,
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
  },
  picker: {
    height: 50,
    width: "100%",
  },
  pickerIcon: {
    position: "absolute",
    right: 16,
    top: 15,
    pointerEvents: "none",
  },
  helperTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    gap: 6,
  },
  helperText: {
    fontSize: 14,
  },
  
  // Error Styles
  errorCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    gap: 12,
  },
  errorText: {
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  
  // Action Section
  actionSection: {
    flexDirection: "row",
    gap: 12,
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1.5,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  submitButton: {
    flex: 2,
    flexDirection: "row",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "700",
  },
  required: {
    fontSize: 16,
    fontWeight: "600",
  },
});