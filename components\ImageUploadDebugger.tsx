import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import { debugImageUploadIssues, testStorageConnectivity, uploadInventoryImage } from '@/services/storage-service';
import * as ImagePicker from 'expo-image-picker';

/**
 * Debug component for troubleshooting image upload issues in production
 * Add this component temporarily to any screen to debug upload issues
 */
export const ImageUploadDebugger: React.FC = () => {
  const [isDebugging, setIsDebugging] = useState(false);
  const [debugResults, setDebugResults] = useState<any>(null);

  const runDebugTests = async () => {
    setIsDebugging(true);
    try {
      console.log('🔧 Starting image upload debug tests...');
      
      const results = await debugImageUploadIssues();
      setDebugResults(results);
      
      // Show summary alert
      const authStatus = results.authentication.isAuthenticated ? '✅' : '❌';
      const storageStatus = results.storage.connectivity ? '✅' : '❌';
      
      Alert.alert(
        'Debug Results',
        `Authentication: ${authStatus}\nStorage: ${storageStatus}\n\nCheck console for details`,
        [{ text: 'OK' }]
      );
      
    } catch (error) {
      console.error('Debug test failed:', error);
      Alert.alert('Debug Error', 'Failed to run debug tests. Check console for details.');
    } finally {
      setIsDebugging(false);
    }
  };

  const testStorageOnly = async () => {
    setIsDebugging(true);
    try {
      console.log('🔧 Testing storage connectivity only...');
      const result = await testStorageConnectivity();

      if (result) {
        Alert.alert(
          'Storage Test Success! ✅',
          'Storage connectivity is working. You can now upload images.',
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Storage Test Failed ❌',
          'Storage connectivity failed. This is likely a Firebase Storage rules issue.\n\n' +
          '🔧 Fix: Go to Firebase Console > Storage > Rules and update them.\n\n' +
          'Check console logs for detailed error information.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Storage test failed:', error);
      Alert.alert(
        'Storage Test Error ❌',
        `Error: ${error instanceof Error ? error.message : 'Unknown error'}\n\nCheck console for details.`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsDebugging(false);
    }
  };

  const testActualImageUpload = async () => {
    setIsDebugging(true);
    try {
      console.log('🔧 Testing actual image upload...');

      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera roll permission is required to test image upload.');
        return;
      }

      // Pick an image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.8,
      });

      if (result.canceled) {
        Alert.alert('Test Cancelled', 'No image selected for testing.');
        return;
      }

      const imageUri = result.assets[0].uri;
      console.log('Selected image for testing:', imageUri);

      // Try to upload the image
      const testId = `test_${Date.now()}`;
      const downloadUrl = await uploadInventoryImage(imageUri, testId);

      Alert.alert(
        'Upload Test Success! ✅',
        `Image uploaded successfully!\n\nDownload URL: ${downloadUrl.substring(0, 50)}...`,
        [{ text: 'Great!' }]
      );

    } catch (error) {
      console.error('Image upload test failed:', error);
      Alert.alert(
        'Upload Test Failed ❌',
        `Error: ${error instanceof Error ? error.message : 'Unknown error'}\n\nCheck console for details.`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsDebugging(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔧 Image Upload Debugger</Text>
      <Text style={styles.subtitle}>Use this to debug APK upload issues</Text>
      
      <TouchableOpacity 
        style={[styles.button, isDebugging && styles.buttonDisabled]} 
        onPress={runDebugTests}
        disabled={isDebugging}
      >
        <Text style={styles.buttonText}>
          {isDebugging ? 'Running Tests...' : 'Run Full Debug'}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.button, styles.buttonSecondary, isDebugging && styles.buttonDisabled]}
        onPress={testStorageOnly}
        disabled={isDebugging}
      >
        <Text style={styles.buttonText}>Test Storage Only</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.buttonTertiary, isDebugging && styles.buttonDisabled]}
        onPress={testActualImageUpload}
        disabled={isDebugging}
      >
        <Text style={styles.buttonText}>
          {isDebugging ? 'Testing Upload...' : 'Test Real Image Upload'}
        </Text>
      </TouchableOpacity>
      
      {debugResults && (
        <View style={styles.results}>
          <Text style={styles.resultsTitle}>Last Debug Results:</Text>
          <Text style={styles.resultsText}>
            Platform: {debugResults.platform}
          </Text>
          <Text style={styles.resultsText}>
            Auth: {debugResults.authentication.isAuthenticated ? '✅' : '❌'}
          </Text>
          <Text style={styles.resultsText}>
            Storage: {debugResults.storage.connectivity ? '✅' : '❌'}
          </Text>
          <Text style={styles.resultsText}>
            Time: {new Date(debugResults.timestamp).toLocaleTimeString()}
          </Text>
        </View>
      )}
      
      <Text style={styles.note}>
        💡 Check console logs for detailed information
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  buttonSecondary: {
    backgroundColor: '#34C759',
  },
  buttonTertiary: {
    backgroundColor: '#FF9500',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
  results: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 6,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  resultsTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultsText: {
    fontSize: 12,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  note: {
    fontSize: 12,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ImageUploadDebugger;
