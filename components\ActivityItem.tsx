import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Dimensions } from 'react-native';
import { Activity } from '@/services/inventory-service';
import { 
  Clock, 
  User, 
  Package, 
  ClipboardList, 
  FileText, 
  Users, 
  MapPin, 
  FileBarChart, 
  Calendar, 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  XCircle, 
  LogIn, 
  LogOut 
} from 'lucide-react-native';
import { Timestamp } from 'firebase/firestore';

const { width: screenWidth } = Dimensions.get('window');

interface ActivityItemProps {
  activity: Activity;
  onPress?: () => void;
  backgroundColor?: string;
  textColor?: string;
  secondaryTextColor?: string;
  borderColor?: string;
  isDarkMode?: boolean;
}

const ActivityItem: React.FC<ActivityItemProps> = ({
  activity,
  onPress,
  backgroundColor = '#ffffff',
  textColor = '#333333',
  secondaryTextColor = '#666666',
  borderColor = '#e0e0e0',
  isDarkMode = false,
}) => {
  // Function to get icon based on activity type
  const getActivityIcon = () => {
    const iconSize = screenWidth < 380 ? 16 : 20;
    const iconColor = getActivityColor();
    
    switch (activity.type) {
      case 'login':
        return <LogIn size={iconSize} color={iconColor} />;
      case 'logout':
        return <LogOut size={iconSize} color={iconColor} />;
      case 'inventory_created':
      case 'inventory_updated':
      case 'inventory_deleted':
        return <Package size={iconSize} color={iconColor} />;
      case 'request_created':
      case 'request_updated':
      case 'request_deleted':
      case 'request_approved':
      case 'request_rejected':
        return <ClipboardList size={iconSize} color={iconColor} />;
      case 'note_created':
      case 'note_updated':
      case 'note_deleted':
        return <FileText size={iconSize} color={iconColor} />;
      case 'user_created':
      case 'user_updated':
      case 'user_deleted':
        return <User size={iconSize} color={iconColor} />;
      case 'farm_created':
      case 'farm_updated':
      case 'farm_deleted':
        return <MapPin size={iconSize} color={iconColor} />;
      case 'report_generated':
      case 'report_deleted':
        return <FileBarChart size={iconSize} color={iconColor} />;
      case 'report_scheduled':
      case 'report_unscheduled':
        return <Calendar size={iconSize} color={iconColor} />;
      case 'transfer_approved':
        return <CheckCircle size={iconSize} color={iconColor} />;
      case 'transfer_rejected':
        return <XCircle size={iconSize} color={iconColor} />;
      case 'transfer_received':
        return <ArrowRight size={iconSize} color={iconColor} />;
      default:
        return <Clock size={iconSize} color={iconColor} />;
    }
  };
  
  // Function to get color based on activity type
  const getActivityColor = () => {
    const type = activity.type;
    
    if (type.includes('created') || type.includes('approved') || type.includes('received')) {
      return isDarkMode ? '#4CAF50' : '#2E7D32';
    } else if (type.includes('updated') || type.includes('scheduled')) {
      return isDarkMode ? '#2196F3' : '#1976D2';
    } else if (type.includes('deleted') || type.includes('rejected') || type.includes('unscheduled')) {
      return isDarkMode ? '#F44336' : '#D32F2F';
    } else if (type.includes('login')) {
      return isDarkMode ? '#4CAF50' : '#2E7D32';
    } else if (type.includes('logout')) {
      return isDarkMode ? '#FF9800' : '#F57C00';
    } else {
      return isDarkMode ? '#9E9E9E' : '#757575';
    }
  };
  
  // Function to format activity type for display
  const formatActivityType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  // Function to format timestamp
  const formatTimestamp = (timestamp: string | Date | Timestamp) => {
    if (!timestamp) return 'Unknown time';
    
    // Handle Firebase Timestamp objects
    let date: Date;
    if (timestamp instanceof Timestamp) {
      date = timestamp.toDate();
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      // Fallback for any other type
      date = new Date();
    }
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);
    
    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor, borderColor }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${getActivityColor()}20` }]}>
        {getActivityIcon()}
      </View>
      
      <View style={styles.contentContainer}>
        <Text style={[styles.title, { color: textColor }]}>
          {formatActivityType(activity.type)}
        </Text>
        
        <Text style={[styles.details, { color: secondaryTextColor }]} numberOfLines={2}>
          {activity.details || `Activity related to ${activity.itemName || 'unknown item'}`}
        </Text>
        
        <View style={styles.metaContainer}>
          <View style={styles.metaItem}>
            <Clock size={screenWidth < 380 ? 10 : 12} color={secondaryTextColor} style={styles.metaIcon} />
            <Text style={[styles.metaText, { color: secondaryTextColor }]}>
              {formatTimestamp(activity.timestamp)}
            </Text>
          </View>

          {activity.userName && (
            <View style={styles.metaItem}>
              <User size={screenWidth < 380 ? 10 : 12} color={secondaryTextColor} style={styles.metaIcon} />
              <Text style={[styles.metaText, { color: secondaryTextColor }]}>
                {activity.userName}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: screenWidth < 380 ? 10 : 12,
    borderRadius: screenWidth < 380 ? 10 : 12,
    marginBottom: screenWidth < 380 ? 10 : 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: screenWidth < 380 ? 32 : 40,
    height: screenWidth < 380 ? 32 : 40,
    borderRadius: screenWidth < 380 ? 16 : 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: screenWidth < 380 ? 8 : 12,
  },
  contentContainer: {
    flex: 1,
    minWidth: 0, // Allows text to shrink
  },
  title: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: '600',
    marginBottom: screenWidth < 380 ? 3 : 4,
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  details: {
    fontSize: screenWidth < 380 ? 12 : 14,
    marginBottom: screenWidth < 380 ? 6 : 8,
    lineHeight: screenWidth < 380 ? 16 : 18,
  },
  metaContainer: {
    flexDirection: screenWidth < 380 ? 'column' : 'row',
    flexWrap: 'wrap',
    gap: screenWidth < 380 ? 2 : 0,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: screenWidth < 380 ? 0 : 12,
    marginBottom: screenWidth < 380 ? 2 : 0,
  },
  metaIcon: {
    marginRight: screenWidth < 380 ? 3 : 4,
  },
  metaText: {
    fontSize: screenWidth < 380 ? 10 : 12,
    lineHeight: screenWidth < 380 ? 14 : 16,
  },
});

export default ActivityItem;