// Utility functions for services

// Generate a mock ID for testing purposes
export const generateMockId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Generate unique ID with timestamp
export const generateUniqueId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `${timestamp}_${randomPart}`;
};

// Generate ID function (main export)
export const generateId = (): string => {
  return generateUniqueId();
};

// Format date to ISO string
export const formatDate = (date: Date): string => {
  return date.toISOString();
};

// Parse date from string
export const parseDate = (dateString: string): Date => {
  return new Date(dateString);
};

// Check if a value is empty (null, undefined, empty string, or empty array)
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === "string" && value.trim() === "") return true;
  if (Array.isArray(value) && value.length === 0) return true;
  return false;
};

// Default export for compatibility
const utils = {
  generateId,
  generateMockId,
  generateUniqueId,
  formatDate,
  parseDate,
  isEmpty,
};
export function toCamelCase(str) {
  return str
    .split(' ')
    .map((word, index) =>
      index === 0
        ? word.toLowerCase()
        : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join('');
}
export default utils;