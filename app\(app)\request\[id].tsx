import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Animated,
  Platform,
  Dimensions,
} from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { getRequestById, approveRequest, rejectRequest, Request, updateRequestPrice } from "@/services/request-service";
import { getInventoryItemById, InventoryItem } from "@/services/inventory-service";
import { useLookupStore } from "@/services/lookup_service";
import { CheckCircle, XCircle, Clock, ArrowLeft, Calendar, User, Package, FileText, Building, Tag, Truck, Wrench, Fuel, DollarSign } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { LinearGradient } from "expo-linear-gradient";
import { useFarm } from "@/context/farm-context";

const { width: screenWidth } = Dimensions.get('window');

export default function RequestDetailScreen() {
  const { id, farmId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colors } = useTheme();
  const { selectedFarm } = useFarm();
  const { t } = useLanguage();
  
  const [request, setRequest] = useState<Request | null>(null);
  const [inventoryItem, setInventoryItem] = useState<InventoryItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [machineryTypes, setMachineryTypes] = useState<any[]>([]);
  const [inventoryCategories, setInventoryCategories] = useState<any[]>([]);
  const { getLookupsByCategory } = useLookupStore();
  
  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;
  
  useEffect(() => {
    loadRequest();
    loadLookupData();
  }, [id, farmId, selectedFarm]);

  const loadLookupData = () => {
    try {
      const machineryTypesData = getLookupsByCategory('machineryType');
      const inventoryCategoriesData = getLookupsByCategory('inventoryCategory');
      setMachineryTypes(machineryTypesData);
      setInventoryCategories(inventoryCategoriesData);
    } catch (error) {
      console.error('Error loading lookup data:', error);
    }
  };

  const getMachineryTypeTitle = (typeId: string) => {
    const typeItem = machineryTypes.find(item => item.id === typeId);
    if (typeItem) {
      const title = typeItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`machinery.types.${title}`) || typeItem.title;
    }
    return typeId.charAt(0).toUpperCase() + typeId.slice(1);
  };

  const getCategoryTitle = (categoryId: string) => {
    const categoryItem = inventoryCategories.find(item => item.id === categoryId);
    if (categoryItem) {
      const title = categoryItem.title.toLowerCase();
      return t(`inventory.categories.${title}`) || t(`inventory.${title}`) || categoryItem.title;
    }
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1);
  };

  const getRequestTypeTitle = () => {
    if (request?.requestType === "machinery") {
      if (request.category === "machinery") {
        // Try multiple possible translation keys
        return t('machinery.newMachineryItem') ||
               t('requests.newMachineryItem') ||
               t('machinery.requestNewItem') ||
               "New Machinery Item";
      } else if (request.requestSubType === 'use') {
        return t('machinery.requestUse') ||
               t('machinery.requestForUse') ||
               "Request Use";
      } else if (request.requestSubType === 'fuel') {
        return t('machinery.requestFuel') ||
               t('machinery.requestForFuel') ||
               "Request Fuel";
      } else if (request.requestSubType === 'maintenance') {
        return t('machinery.requestMaintenance') ||
               t('machinery.requestForMaintenance') ||
               "Request Maintenance";
      } else if (request.requestSubType === 'transfer') {
        return t('machinery.requestTransfer') ||
               t('machinery.requestForTransfer') ||
               "Request Transfer";
      } else {
        return t('requests.machineryRequest') || "Machinery Request";
      }
    } else if (request?.requestType === "inventory") {
      return t('requests.newItem') || "New Item";
    } else if (request?.requestType === "existing") {
      return t('requests.existingItem') || "Existing Item";
    } else {
      return t('common.unknown') || "Unknown";
    }
  };
  
  useEffect(() => {
    if (!loading) {
      // Start animations when data is loaded
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [loading, fadeAnim, slideAnim]);
  
  const loadRequest = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const requestId = Array.isArray(id) ? id[0] : id.toString();
      
      // Use farmId from params or selectedFarm
      let farmIdToUse;
      if (farmId) {
        farmIdToUse = Array.isArray(farmId) ? farmId[0] : farmId.toString();
      } else if (selectedFarm) {
        farmIdToUse = selectedFarm.id;
      } else {
        console.error("No farm ID available");
        setLoading(false);
        return;
      }
      
      console.log("Loading request details for ID:", requestId, "farmId:", farmIdToUse);
      const data = await getRequestById(requestId, farmIdToUse);
      
      if (data) {
        console.log("Request loaded:", data.id, data.itemName || data.machineryName);
        setRequest(data);

        // If it's an inventory request with an itemId, fetch the inventory item details
        if (data.requestType === "inventory" && data.itemId && farmIdToUse) {
          try {
            const itemData = await getInventoryItemById(data.itemId, farmIdToUse);
            if (itemData) {
              setInventoryItem(itemData);

              // Calculate and save totalPrice if not already present
              if (!data.totalPrice && itemData.price) {
                const totalPrice = itemData.price * data.quantity;
                const unitPrice = itemData.price;

                // Update the request with calculated price
                try {
                  await updateRequestPrice(data.id, farmIdToUse, unitPrice, totalPrice);
                  // Update local state
                  setRequest(prev => prev ? { ...prev, unitPrice, totalPrice } : prev);
                } catch (error) {
                  console.error("Error updating request price:", error);
                }
              }
            }
          } catch (error) {
            console.error("Error loading inventory item:", error);
            // Don't fail the whole request if inventory item can't be loaded
          }
        }
      } else {
        console.log("Request not found");
      }
    } catch (error) {
      console.error("Error loading request:", error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleApprove = async () => {
    if (!request || !user) return;
    
    try {
      setProcessing(true);
      
      // Make sure we have a farmId
      if (!request.farmId) {
        Alert.alert(t('common.error'), t('common.farmIdRequired'));
        setProcessing(false);
        return;
      }
      
      console.log("Starting approval process for request:", request.id);
      
      await approveRequest(request.id, user.uid, user.displayName || "", request.farmId);
      
      console.log("Request approved successfully");
      
      Alert.alert(
        t('common.success'),
        t('requests.approveSuccess'),
        [{ 
          text: t('common.ok'), 
          onPress: () => {
            console.log("Reloading request data after approval");
            loadRequest();
          }
        }]
      );
    } catch (error) {
      console.error("Error approving request:", error);
      Alert.alert(
        t('common.error'), 
        error instanceof Error ? error.message : t('requests.approveError')
      );
    } finally {
      setProcessing(false);
    }
  };
  
  const handleReject = async () => {
    if (!request || !user) return;
    
    if (!rejectionReason.trim()) {
      Alert.alert(t('common.error'), t('requests.enterRejectionReason'));
      return;
    }
    
    try {
      setProcessing(true);
      
      // Make sure we have a farmId
      if (!request.farmId) {
        Alert.alert(t('common.error'), t('common.farmIdRequired'));
        setProcessing(false);
        return;
      }
      
      await rejectRequest(request.id, user.uid, user.displayName || "", rejectionReason, request.farmId);
      
      Alert.alert(
        t('common.success'),
        t('requests.rejectSuccess'),
        [{ text: t('common.ok'), onPress: () => {
          setShowRejectionForm(false);
          loadRequest();
        }}]
      );
    } catch (error) {
      console.error("Error rejecting request:", error);
      Alert.alert(t('common.error'), t('requests.rejectError'));
    } finally {
      setProcessing(false);
    }
  };
  
  const formatDate = (dateString?: string): string => {
    if (!dateString) return t('common.notAvailable');
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const getStatusColor = () => {
    if (!request) return "#757575";
    
    switch (request.status) {
      case "approved":
        return "#4CAF50";
      case "pending":
        return "#FF9800";
      case "rejected":
        return "#D32F2F";
      default:
        return "#757575";
    }
  };
  
  const getStatusGradient = (): readonly [string, string] => {
    if (!request) return ["#f5f5f5", "#f5f5f5"] as const;
    
    switch (request.status) {
      case "approved":
        return colors.background === '#121212' 
          ? ["#003300", "#002200"] as const
          : ["#E8F5E9", "#C8E6C9"] as const;
      case "pending":
        return colors.background === '#121212' 
          ? ["#4F3200", "#2C1C00"] as const
          : ["#FFF8E1", "#FFECB3"] as const;
      case "rejected":
        return colors.background === '#121212' 
          ? ["#330000", "#220000"] as const
          : ["#FFEBEE", "#FFCDD2"] as const;
      default:
        return colors.background === '#121212' 
          ? ["#1e1e1e", "#1e1e1e"] as const
          : ["#f5f5f5", "#f5f5f5"] as const;
    }
  };
  
  const getStatusIcon = () => {
    if (!request) return null;
    
    switch (request.status) {
      case "approved":
        return <CheckCircle size={24} color="#4CAF50" />;
      case "pending":
        return <Clock size={24} color="#FF9800" />;
      case "rejected":
        return <XCircle size={24} color="#D32F2F" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    if (!request) return "";
    
    switch (request.status) {
      case "approved":
        return t('common.approved');
      case "pending":
        return t('common.pending');
      case "rejected":
        return t('common.rejected');
      default:
        return request.status;
    }
  };
  
  // Check if user can approve/reject this request
  const canApproveOrReject = () => {
    if (!user || !request) return false;
    
    // Only allow approval/rejection if request is pending
    if (request.status !== "pending") return false;
    
    // Admin can approve caretaker requests
    if (user.role === "admin" && request.requestorRole === "caretaker") {
      return true;
    }
    
    // Owner can approve admin requests
    if (user.role === "owner" && request.requestorRole === "admin") {
      return true;
    }
    
    return false;
  };
  
  const renderMachineryDetails = () => {
    if (request?.requestType !== "machinery") return null;

    return (
      <View style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('requests.machineryRequestDetails')}</Text>
        
        <View style={styles.infoGrid}>
          {request.machineryName && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Truck size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('requests.machinery')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {request.machineryName}
                </Text>
              </View>
            </View>
          )}

          {request.machineryType && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.machineryType')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {getMachineryTypeTitle(request.machineryType)}
                </Text>
              </View>
            </View>
          )}

          {request.category === 'machinery' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Package size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.category')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {getRequestTypeTitle()}
                </Text>
              </View>
            </View>
          )}
          
          {request.requestSubType && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('requests.requestType')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {getRequestTypeTitle()}
                </Text>
              </View>
            </View>
          )}
          
          {request.startDate && request.category !== 'machinery' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Calendar size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.startDate')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {new Date(request.startDate).toLocaleDateString()}
                </Text>
              </View>
            </View>
          )}

          {request.endDate && request.category !== 'machinery' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Calendar size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.endDate')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {new Date(request.endDate).toLocaleDateString()}
                </Text>
              </View>
            </View>
          )}
          
          {request.fuelAmount && (request.requestSubType === 'fuel' || request.requestSubType === 'use') && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Fuel size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.fuelAmount')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {request.fuelAmount}L
                </Text>
              </View>
            </View>
          )}

          {request.litterPrice && request.requestSubType === 'use' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.litterPrice')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  Rs {request.litterPrice}/L
                </Text>
              </View>
            </View>
          )}

          {request.totalPrice && request.requestSubType === 'use' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Package size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.totalPrice')}</Text>
                <Text style={[styles.infoValue, { color: "#4CAF50", fontWeight: "600" }]}>
                  Rs {Number(request.totalPrice).toLocaleString()}
                </Text>
              </View>
            </View>
          )}
          
          {request.maintenanceType && request.requestSubType === 'maintenance' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Wrench size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.maintenanceType')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {request.maintenanceType.charAt(0).toUpperCase() + request.maintenanceType.slice(1)}
                </Text>
              </View>
            </View>
          )}

          {request.maintenancePrice && request.requestSubType === 'maintenance' && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Package size={18} color={colors.background === '#121212' ? "#FF9800" : "#F57C00"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.maintenancePrice')}</Text>
                <Text style={[styles.infoValue, { color: "#FF9800", fontWeight: "600" }]}>
                  Rs {Number(request.maintenancePrice).toLocaleString()}
                </Text>
              </View>
            </View>
          )}
          
          {request.urgency && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.urgencyLevel')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {request.urgency.charAt(0).toUpperCase() + request.urgency.slice(1)}
                </Text>
              </View>
            </View>
          )}
          
          {request.targetFarmName && (
            <View style={styles.infoItem}>
              <View style={styles.infoIconContainer}>
                <Building size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
              </View>
              <View>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('machinery.destinationFarm')}</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {request.targetFarmName}
                </Text>
              </View>
            </View>
          )}
        </View>
      </View>
    );
  };
  
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  if (!request) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('requests.requestDetails'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: "#D32F2F" }]}>{t('requests.requestNotFound')}</Text>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: colors.primary }]} 
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <Stack.Screen options={{
        title: request.requestType === "machinery" ? t('requests.machineryRequest') : t('requests.requestDetails'),
        headerStyle: { backgroundColor: colors.primary },
        headerTintColor: 'white',
        headerTitleStyle: { color: 'white', fontWeight: '600' },
      }} />
      
      <ScrollView style={styles.scrollView}>
        <Animated.View 
          style={[
            styles.contentContainer,
            { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
          ]}
        >
          <LinearGradient
            colors={getStatusGradient()}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={[styles.statusBanner, { borderColor: colors.border }]}
          >
            <View style={styles.statusIconContainer}>{getStatusIcon()}</View>
            <Text style={[styles.statusBannerText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </LinearGradient>
          
          <View style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <Text style={[styles.title, { color: colors.text }]}>
              {request.requestType === "machinery" ? request.machineryName || t('requests.machineryRequest') : request.itemName}
            </Text>
            
            <View style={styles.infoGrid}>
              {request.requestType !== "machinery" && (
                <>
                  <View style={styles.infoItem}>
                    <View style={styles.infoIconContainer}>
                      <Package size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                    </View>
                    <View>
                      <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.quantity')}</Text>
                      <Text style={[styles.infoValue, { color: colors.text }]}>
                        {request.quantity} {request.unit}
                      </Text>
                    </View>
                  </View>
                  
                  {request.category && (
                    <View style={styles.infoItem}>
                      <View style={styles.infoIconContainer}>
                        <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                      </View>
                      <View>
                        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.category')}</Text>
                        <Text style={[styles.infoValue, { color: colors.text }]}>
                          {getCategoryTitle(request.category)}
                        </Text>
                      </View>
                    </View>
                  )}

                  {request.unitPrice && request.requestType !== "machinery" && (
                    <View style={styles.infoItem}>
                      <View style={styles.infoIconContainer}>
                        <Package size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
                      </View>
                      <View>
                        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('inventory.unitPrice')}</Text>
                        <Text style={[styles.infoValue, { color: colors.text }]}>
                          Rs {Number(request.unitPrice).toLocaleString()}
                        </Text>
                      </View>
                    </View>
                  )}

                  {request.totalPrice && request.requestType !== "machinery" && (
                    <View style={styles.infoItem}>
                      <View style={styles.infoIconContainer}>
                        <Package size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
                      </View>
                      <View>
                        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('inventory.totalPrice')}</Text>
                        <Text style={[styles.infoValue, { color: "#4CAF50", fontWeight: "600" }]}>
                          Rs {Number(request.totalPrice).toLocaleString()}
                        </Text>
                      </View>
                    </View>
                  )}

                  {request.purchaseDate && request.requestType !== "machinery" && (
                    <View style={styles.infoItem}>
                      <View style={styles.infoIconContainer}>
                        <Calendar size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
                      </View>
                      <View>
                        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('inventory.purchaseDate')}</Text>
                        <Text style={[styles.infoValue, { color: colors.text }]}>
                          {new Date(request.purchaseDate).toLocaleDateString()}
                        </Text>
                      </View>
                    </View>
                  )}

                  {request.supplier && request.requestType !== "machinery" && (
                    <View style={styles.infoItem}>
                      <View style={styles.infoIconContainer}>
                        <User size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
                      </View>
                      <View>
                        <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('inventory.supplier')}</Text>
                        <Text style={[styles.infoValue, { color: colors.text }]}>
                          {request.supplier}
                        </Text>
                      </View>
                    </View>
                  )}
                </>
              )}
              
              <View style={styles.infoItem}>
                <View style={styles.infoIconContainer}>
                  <Building size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                </View>
                <View>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.farm')}</Text>
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {request.farmName}
                  </Text>
                </View>
              </View>
              
              <View style={styles.infoItem}>
                <View style={styles.infoIconContainer}>
                  <User size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                </View>
                <View>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('requests.requestedBy')}</Text>
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {request.requestedByName}
                  </Text>
                </View>
              </View>
              
              {/* Show price for inventory requests, type for others */}
              {request.requestType === "inventory" ? (
                <View style={styles.infoItem}>
                  <View style={styles.infoIconContainer}>
                    <DollarSign size={18} color={colors.background === '#121212' ? "#4CAF50" : "#2E7D32"} />
                  </View>
                  <View>
                    <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('inventory.estimatedPrice')}</Text>
                    <Text style={[styles.infoValue, { color: "#4CAF50", fontWeight: "600" }]}>
                      {(() => {
                        // First try to use request's total price
                        if (request.totalPrice) {
                          return `Rs ${Number(request.totalPrice).toLocaleString()}`;
                        }

                        // If no price in request, calculate from inventory item
                        if (inventoryItem && inventoryItem.price) {
                          const totalPrice = inventoryItem.price * request.quantity;
                          return `Rs ${Number(totalPrice).toLocaleString()}`;
                        }

                        // Fallback
                        return t('inventory.priceNotSpecified');
                      })()}
                    </Text>
                  </View>
                </View>
              ) : (
                <View style={styles.infoItem}>
                  <View style={styles.infoIconContainer}>
                    <Tag size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                  </View>
                  <View>
                    <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.type')}</Text>
                    <Text style={[styles.infoValue, { color: colors.text }]}>
                      {getRequestTypeTitle()}
                    </Text>
                  </View>
                </View>
              )}
              
              <View style={styles.infoItem}>
                <View style={styles.infoIconContainer}>
                  <Calendar size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                </View>
                <View>
                  <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>{t('common.created')}</Text>
                  <Text style={[styles.infoValue, { color: colors.text }]}>
                    {formatDate(request.createdAt)}
                  </Text>
                </View>
              </View>
            </View>
            
            {request.reason && (
              <View style={[styles.reasonContainer, { backgroundColor: colors.background === '#121212' ? "#252525" : "#f5f5f5", borderColor: colors.border }]}>
                <View style={styles.reasonHeader}>
                  <FileText size={18} color={colors.background === '#121212' ? "#90caf9" : "#1976D2"} />
                  <Text style={[styles.reasonLabel, { color: colors.text }]}>{t('requests.reasonForRequest')}</Text>
                </View>
                <Text style={[styles.reasonText, { color: colors.text }]}>{request.reason}</Text>
              </View>
            )}
          </View>
          
          {/* Render machinery-specific details */}
          {renderMachineryDetails()}
          
          {request.status === "approved" && (
            <View style={[styles.statusInfoContainer, { backgroundColor: colors.background === '#121212' ? "#252525" : "#f5f5f5", borderColor: colors.border }]}>
              <View style={styles.statusInfoHeader}>
                <CheckCircle size={18} color="#4CAF50" />
                <Text style={[styles.statusInfoTitle, { color: colors.text }]}>{t('requests.approvalInformation')}</Text>
              </View>
              <View style={styles.statusInfoContent}>
                <View style={styles.statusInfoRow}>
                  <Text style={[styles.statusInfoLabel, { color: colors.textSecondary }]}>{t('requests.approvedBy')}:</Text>
                  <Text style={[styles.statusInfoValue, { color: colors.text }]}>{request.approvedByName}</Text>
                </View>
                <View style={styles.statusInfoRow}>
                  <Text style={[styles.statusInfoLabel, { color: colors.textSecondary }]}>{t('requests.approvedAt')}:</Text>
                  <Text style={[styles.statusInfoValue, { color: colors.text }]}>{formatDate(request.approvedAt || "")}</Text>
                </View>
              </View>
            </View>
          )}
          
          {request.status === "rejected" && (
            <View style={[styles.statusInfoContainer, { backgroundColor: colors.background === '#121212' ? "#252525" : "#f5f5f5", borderColor: colors.border }]}>
              <View style={styles.statusInfoHeader}>
                <XCircle size={18} color="#D32F2F" />
                <Text style={[styles.statusInfoTitle, { color: colors.text }]}>{t('requests.rejectionInformation')}</Text>
              </View>
              <View style={styles.statusInfoContent}>
                <View style={styles.statusInfoRow}>
                  <Text style={[styles.statusInfoLabel, { color: colors.textSecondary }]}>{t('requests.rejectedBy')}:</Text>
                  <Text style={[styles.statusInfoValue, { color: colors.text }]}>{request.rejectedByName}</Text>
                </View>
                <View style={styles.statusInfoRow}>
                  <Text style={[styles.statusInfoLabel, { color: colors.textSecondary }]}>{t('requests.rejectedAt')}:</Text>
                  <Text style={[styles.statusInfoValue, { color: colors.text }]}>{formatDate(request.rejectedAt || "")}</Text>
                </View>
                {request.rejectionReason && (
                  <View style={styles.rejectionReasonContainer}>
                    <Text style={[styles.statusInfoLabel, { color: colors.textSecondary }]}>{t('requests.rejectionReason')}:</Text>
                    <Text style={[styles.rejectionReasonText, { color: colors.text }]}>{request.rejectionReason}</Text>
                  </View>
                )}
              </View>
            </View>
          )}
          
          {canApproveOrReject() && !showRejectionForm && (
            <View style={styles.actionContainer}>
              <TouchableOpacity 
                style={styles.rejectButton} 
                onPress={() => setShowRejectionForm(true)}
                disabled={processing}
                activeOpacity={0.8}
              >
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <XCircle size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>{t('requests.reject')}</Text>
                  </>
                )}
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.approveButton} 
                onPress={handleApprove}
                disabled={processing}
                activeOpacity={0.8}
              >
                {processing ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <CheckCircle size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>{t('requests.approve')}</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          )}
          
          {showRejectionForm && (
            <View style={styles.rejectionFormContainer}>
              <Text style={[styles.rejectionFormLabel, { color: colors.text }]}>{t('requests.reasonForRejection')}:</Text>
              <TextInput
                style={[
                  styles.rejectionInput, 
                  { 
                    backgroundColor: colors.background === '#121212' ? "#252525" : "#f9f9f9",
                    borderColor: colors.border,
                    color: colors.text,
                  }
                ]}
                value={rejectionReason}
                onChangeText={setRejectionReason}
                placeholder={t('requests.enterRejectionReason')}
                placeholderTextColor={colors.textSecondary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
              
              <View style={styles.rejectionFormButtons}>
                <TouchableOpacity 
                  style={[
                    styles.cancelButton, 
                    { 
                      backgroundColor: colors.background === '#121212' ? "#252525" : "#f5f5f5",
                      borderColor: colors.border,
                    }
                  ]} 
                  onPress={() => setShowRejectionForm(false)}
                  disabled={processing}
                  activeOpacity={0.8}
                >
                  <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>{t('common.cancel')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.confirmRejectButton} 
                  onPress={handleReject}
                  disabled={processing}
                  activeOpacity={0.8}
                >
                  {processing ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.confirmRejectButtonText}>{t('requests.confirmRejection')}</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
          
          <TouchableOpacity 
            style={[
              styles.backButton, 
              { backgroundColor: colors.primary }
            ]} 
            onPress={() => router.back()}
            activeOpacity={0.8}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>{t('common.goBack')}</Text>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginBottom: 20,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  statusBanner: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  statusIconContainer: {
    marginRight: 8,
  },
  statusBannerText: {
    fontSize: 18,
    fontWeight: "700",
  },
  card: {
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 16 : 20,
    marginBottom: screenWidth < 380 ? 16 : 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: screenWidth < 380 ? 20 : 24,
    fontWeight: "bold",
    marginBottom: screenWidth < 380 ? 16 : 20,
    lineHeight: screenWidth < 380 ? 26 : 30,
  },
  sectionTitle: {
    fontSize: screenWidth < 380 ? 18 : 20,
    fontWeight: "600",
    marginBottom: screenWidth < 380 ? 12 : 16,
    lineHeight: screenWidth < 380 ? 22 : 26,
  },
  infoGrid: {
    flexDirection: screenWidth < 380 ? "column" : "row",
    flexWrap: "wrap",
    marginBottom: 20,
  },
  infoItem: {
    flexDirection: "row",
    width: screenWidth < 380 ? "100%" : screenWidth < 500 ? "100%" : "50%",
    marginBottom: screenWidth < 380 ? 12 : 16,
    alignItems: "flex-start",
    paddingRight: screenWidth < 380 ? 0 : 8,
  },
  infoIconContainer: {
    width: screenWidth < 380 ? 24 : 30,
    alignItems: "center",
    marginRight: screenWidth < 380 ? 6 : 8,
    flexShrink: 0,
  },
  infoLabel: {
    fontSize: screenWidth < 380 ? 12 : 14,
    marginBottom: screenWidth < 380 ? 2 : 4,
    lineHeight: screenWidth < 380 ? 16 : 18,
  },
  infoValue: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "500",
    lineHeight: screenWidth < 380 ? 18 : 20,
    flexShrink: 1,
    flexWrap: 'wrap',
  },
  reasonContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
  },
  reasonHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  reasonLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  reasonText: {
    fontSize: 16,
    lineHeight: 24,
  },
  statusInfoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
  },
  statusInfoHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  statusInfoTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  statusInfoContent: {
    gap: 8,
  },
  statusInfoRow: {
    marginBottom: 4,
  },
  statusInfoLabel: {
    fontSize: 14,
    marginBottom: 2,
  },
  statusInfoValue: {
    fontSize: 16,
  },
  rejectionReasonContainer: {
    marginTop: 8,
  },
  rejectionReasonText: {
    fontSize: 16,
    fontStyle: "italic",
    marginTop: 4,
  },
  actionContainer: {
    flexDirection: screenWidth < 380 ? "column" : "row",
    justifyContent: "space-between",
    marginBottom: 8,
    gap: screenWidth < 380 ? 8 : 12,
  },
  approveButton: {
    flex: 1,
    backgroundColor: "#4CAF50",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  rejectButton: {
    flex: 1,
    backgroundColor: "#D32F2F",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "600",
  },
  rejectionFormContainer: {
    marginBottom: 8,
  },
  rejectionFormLabel: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  rejectionInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
    marginBottom: 12,
  },
  rejectionFormButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  confirmRejectButton: {
    flex: 2,
    backgroundColor: "#D32F2F",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  confirmRejectButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});