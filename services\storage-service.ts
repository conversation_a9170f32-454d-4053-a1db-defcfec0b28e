import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { Platform } from 'react-native';
import { storage, auth } from './firebase-config';

// Use the centralized Firebase Storage instance

/**
 * Check if user is authenticated before attempting storage operations
 * More lenient for production builds to avoid false negatives
 * @returns Promise<boolean>
 */
const checkAuthentication = async (): Promise<boolean> => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error('No authenticated user found for storage operation');
      return false;
    }

    console.log('User found:', currentUser.uid);

    // For production builds, be more lenient with token validation
    // Sometimes token refresh can fail in APK builds even when user is valid
    try {
      const token = await currentUser.getIdToken(false); // Don't force refresh initially
      if (token) {
        console.log('User authentication verified for storage operation');
        return true;
      }
    } catch (tokenError) {
      console.warn('Token validation failed, trying refresh:', tokenError);

      // Try force refresh as fallback
      try {
        await currentUser.getIdToken(true);
        console.log('User authentication verified after token refresh');
        return true;
      } catch (refreshError) {
        console.error('Token refresh also failed:', refreshError);
        // In production, if user exists but token fails, still allow upload
        // This is more permissive for APK builds where token refresh can be unreliable
        console.warn('Allowing upload despite token issues - user exists');
        return true;
      }
    }

    return true;
  } catch (error) {
    console.error('Authentication check failed:', error);
    return false;
  }
};

/**
 * Upload image to Firebase Storage and return download URL
 * @param imageUri - Local file URI from camera/gallery
 * @param folder - Storage folder (e.g., 'users', 'farms', 'inventory', 'machinery', 'notes')
 * @param fileName - Optional custom filename, if not provided, generates unique name
 * @returns Promise<string> - Download URL from Firebase Storage
 */
export const uploadImageToStorage = async (
  imageUri: string,
  folder: string,
  fileName?: string
): Promise<string> => {
  try {
    console.log(`Uploading image to Storage: ${folder}/${fileName || 'auto-generated'}`);
    console.log('Image URI:', imageUri);
    console.log('Platform:', Platform.OS);

    // Validate inputs
    if (!imageUri) {
      throw new Error('Image URI is required');
    }
    if (!folder) {
      throw new Error('Folder is required');
    }

    // Check authentication before proceeding (critical for production builds)
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
      throw new Error('User must be authenticated to upload images. Please log in again.');
    }

    // Generate unique filename if not provided
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const finalFileName = fileName || `image_${timestamp}_${randomId}.jpg`;

    console.log('Final filename:', finalFileName);

    // Create storage reference
    const storageRef = ref(storage, `${folder}/${finalFileName}`);
    console.log('Storage reference created:', `${folder}/${finalFileName}`);

    // Convert image URI to blob for upload
    console.log('Converting image to blob...');
    const blob = await uriToBlob(imageUri);

    // Validate blob
    if (!blob || blob.size === 0) {
      throw new Error('Invalid image data - blob is empty');
    }

    // Upload the blob to Firebase Storage
    console.log('Starting upload to Firebase Storage...');
    console.log('Blob details:', { size: blob.size, type: blob.type });

    try {
      const snapshot = await uploadBytes(storageRef, blob);
      console.log('Upload completed successfully, getting download URL...');
      console.log('Upload snapshot:', {
        fullPath: snapshot.ref.fullPath,
        name: snapshot.ref.name,
        bucket: snapshot.ref.bucket
      });

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      console.log('Download URL obtained:', downloadURL);

      // Validate the download URL
      if (!downloadURL || !downloadURL.startsWith('http')) {
        throw new Error('Invalid download URL received from Firebase Storage');
      }

      return downloadURL;

    } catch (uploadError) {
      console.error('Upload failed:', uploadError);

      // Try one more time for network-related errors
      if (uploadError instanceof Error &&
          (uploadError.message.includes('network') ||
           uploadError.message.includes('timeout') ||
           uploadError.message.includes('fetch'))) {

        console.log('Retrying upload due to network error...');
        try {
          const retrySnapshot = await uploadBytes(storageRef, blob);
          const retryDownloadURL = await getDownloadURL(retrySnapshot.ref);
          console.log('Retry upload successful:', retryDownloadURL);
          return retryDownloadURL;
        } catch (retryError) {
          console.error('Retry upload also failed:', retryError);
          throw new Error('Network error: Upload failed after retry. Please check your internet connection.');
        }
      }

      // Handle specific Firebase errors
      if (uploadError instanceof Error) {
        if (uploadError.message.includes('storage/unauthorized')) {
          throw new Error('Upload permission denied. Please log out and log back in.');
        } else if (uploadError.message.includes('storage/quota-exceeded')) {
          throw new Error('Storage quota exceeded. Please contact support.');
        } else if (uploadError.message.includes('storage/invalid-format')) {
          throw new Error('Invalid image format. Please select a different image.');
        }
      }

      throw uploadError;
    }

  } catch (error) {
    console.error('Error uploading image to Storage:', error);
    console.error('Upload details:', { imageUri, folder, fileName, platform: Platform.OS });

    // Provide user-friendly error messages
    if (error instanceof Error) {
      if (error.message.includes('Authentication failed') ||
          error.message.includes('auth') ||
          error.message.includes('permission denied')) {
        throw new Error('Authentication failed. Please log out and log back in.');
      } else if (error.message.includes('Network error') ||
                 error.message.includes('network') ||
                 error.message.includes('fetch') ||
                 error.message.includes('internet connection')) {
        throw new Error('Network error. Please check your internet connection and try again.');
      } else if (error.message.includes('Image processing failed') ||
                 error.message.includes('Blob conversion') ||
                 error.message.includes('image file')) {
        throw new Error('Image processing failed. Please select a different image.');
      }
    }

    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Delete image from Firebase Storage
 * @param imageUrl - Full download URL from Firebase Storage
 */
export const deleteImageFromStorage = async (imageUrl: string): Promise<void> => {
  try {
    if (!imageUrl || (!imageUrl.includes('firebasestorage.googleapis.com') && !imageUrl.includes('kissandost-9570f.firebasestorage.app'))) {
      console.log('Not a Firebase Storage URL, skipping deletion:', imageUrl);
      return;
    }

    console.log('Attempting to delete image from Storage:', imageUrl);

    // For Firebase Storage URLs, we need to extract the path from the download URL
    // Firebase Storage download URLs have the format:
    // https://firebasestorage.googleapis.com/v0/b/{bucket}/o/{path}?alt=media&token={token}
    let storagePath = '';

    if (imageUrl.includes('firebasestorage.googleapis.com')) {
      // Extract path from download URL
      const urlParts = imageUrl.split('/o/')[1];
      if (urlParts) {
        storagePath = decodeURIComponent(urlParts.split('?')[0]);
      }
    } else {
      // For direct storage URLs, extract the path
      const urlParts = imageUrl.split('.firebasestorage.app/')[1];
      if (urlParts) {
        storagePath = decodeURIComponent(urlParts.split('?')[0]);
      }
    }

    if (!storagePath) {
      console.log('Could not extract storage path from URL:', imageUrl);
      return;
    }

    console.log('Extracted storage path:', storagePath);
    const storageRef = ref(storage, storagePath);
    await deleteObject(storageRef);
    console.log('Image deleted from Storage successfully');
  } catch (error) {
    console.error('Error deleting image from Storage:', error);
    console.error('URL that failed to delete:', imageUrl);
    // Don't throw error for deletion failures to avoid blocking other operations
  }
};

/**
 * Convert local file URI to Blob for upload
 * Simplified and more reliable for production APK builds
 * @param uri - Local file URI
 * @returns Promise<Blob>
 */
const uriToBlob = async (uri: string): Promise<Blob> => {
  console.log('Converting URI to blob:', uri);
  console.log('Platform:', Platform.OS);

  if (!uri) {
    throw new Error('URI is empty or undefined');
  }

  // Simplified approach that works better in production APK builds
  try {
    console.log('Attempting to fetch image data...');

    const response = await fetch(uri);
    console.log('Fetch response status:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const blob = await response.blob();
    console.log('Blob conversion successful:', {
      size: blob.size,
      type: blob.type,
      platform: Platform.OS
    });

    // Basic validation
    if (!blob) {
      throw new Error('Blob conversion returned null');
    }

    if (blob.size === 0) {
      throw new Error('Blob is empty (0 bytes)');
    }

    // For very large images, warn but don't fail
    if (blob.size > 10 * 1024 * 1024) { // 10MB
      console.warn('Large image detected:', blob.size, 'bytes');
    }

    return blob;

  } catch (error) {
    console.error('Blob conversion failed:', error);
    console.error('Failed URI:', uri);
    console.error('Platform:', Platform.OS);

    // Provide more specific error message
    if (error instanceof Error) {
      if (error.message.includes('Network request failed')) {
        throw new Error('Network error: Cannot access image file. Please check your internet connection.');
      } else if (error.message.includes('HTTP 404')) {
        throw new Error('Image file not found. Please select a different image.');
      } else if (error.message.includes('HTTP 403')) {
        throw new Error('Access denied to image file. Please check file permissions.');
      }
    }

    throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Upload multiple images to Firebase Storage
 * @param imageUris - Array of local file URIs
 * @param folder - Storage folder
 * @returns Promise<string[]> - Array of download URLs
 */
export const uploadMultipleImages = async (
  imageUris: string[],
  folder: string
): Promise<string[]> => {
  try {
    console.log(`Uploading ${imageUris.length} images to ${folder}`);
    
    const uploadPromises = imageUris.map((uri, index) => 
      uploadImageToStorage(uri, folder, `image_${Date.now()}_${index}.jpg`)
    );
    
    const downloadURLs = await Promise.all(uploadPromises);
    console.log(`Successfully uploaded ${downloadURLs.length} images`);
    
    return downloadURLs;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
};

/**
 * Check if a URL is a Firebase Storage URL
 * @param url - URL to check
 * @returns boolean
 */
export const isFirebaseStorageUrl = (url: string): boolean => {
  return url.includes('firebasestorage.googleapis.com') || url.includes('kissandost-9570f.firebasestorage.app');
};

/**
 * Upload user profile image
 * @param imageUri - Local file URI
 * @param userId - User ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadUserProfileImage = async (imageUri: string, userId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'users', `profile_${userId}.jpg`);
};

/**
 * Upload farm image
 * @param imageUri - Local file URI
 * @param farmId - Farm ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadFarmImage = async (imageUri: string, farmId: string): Promise<string> => {
  return uploadImageToStorage(imageUri, 'farms', `farm_${farmId}.jpg`);
};

/**
 * Upload inventory item image
 * @param imageUri - Local file URI
 * @param itemId - Item ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadInventoryImage = async (imageUri: string, itemId: string): Promise<string> => {
  console.log('uploadInventoryImage called with:', { imageUri, itemId });
  try {
    const result = await uploadImageToStorage(imageUri, 'inventory', `item_${itemId}.jpg`);
    console.log('uploadInventoryImage successful:', result);
    return result;
  } catch (error) {
    console.error('uploadInventoryImage failed:', error);
    throw error;
  }
};

/**
 * Upload machinery image
 * @param imageUri - Local file URI
 * @param machineryId - Machinery ID for filename
 * @returns Promise<string> - Download URL
 */
export const uploadMachineryImage = async (imageUri: string, machineryId: string): Promise<string> => {
  console.log('uploadMachineryImage called with:', { imageUri, machineryId });
  try {
    const result = await uploadImageToStorage(imageUri, 'machinery', `machinery_${machineryId}.jpg`);
    console.log('uploadMachineryImage successful:', result);
    return result;
  } catch (error) {
    console.error('uploadMachineryImage failed:', error);
    throw error;
  }
};

/**
 * Upload note attachments
 * @param imageUris - Array of local file URIs
 * @param noteId - Note ID for folder organization
 * @returns Promise<string[]> - Array of download URLs
 */
export const uploadNoteImages = async (imageUris: string[], noteId: string): Promise<string[]> => {
  console.log('uploadNoteImages called with:', { imageUris: imageUris.length, noteId });
  try {
    const result = await uploadMultipleImages(imageUris, `notes/${noteId}`);
    console.log('uploadNoteImages successful:', result.length, 'images uploaded');
    return result;
  } catch (error) {
    console.error('uploadNoteImages failed:', error);
    throw error;
  }
};

/**
 * Test Firebase Storage connectivity (simplified for APK compatibility)
 * @returns Promise<boolean> - True if storage is accessible
 */
export const testStorageConnectivity = async (): Promise<boolean> => {
  try {
    console.log('Testing Firebase Storage connectivity...');
    console.log('Platform:', Platform.OS);

    // Check authentication first
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
      console.error('Storage connectivity test failed: Not authenticated');
      return false;
    }

    // Test 1: Try to create a storage reference (this should always work)
    try {
      const testRef = ref(storage, 'test/connectivity-test.txt');
      console.log('✅ Storage reference created successfully');
    } catch (refError) {
      console.error('❌ Failed to create storage reference:', refError);
      return false;
    }

    // Test 2: Try to access storage bucket info (lightweight test)
    try {
      const storageInstance = storage;
      console.log('✅ Storage instance accessible:', {
        app: storageInstance.app.name,
        bucket: storageInstance._bucket
      });
    } catch (instanceError) {
      console.error('❌ Storage instance not accessible:', instanceError);
      return false;
    }

    // Test 3: Try a simple upload test (most likely to reveal permission issues)
    try {
      const testRef = ref(storage, 'test/connectivity-test.txt');
      const testBlob = new Blob(['connectivity-test'], { type: 'text/plain' });

      console.log('Attempting test upload...');
      const snapshot = await uploadBytes(testRef, testBlob);
      console.log('✅ Test upload successful');

      // Try to get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      console.log('✅ Download URL obtained');

      // Clean up test file
      try {
        await deleteObject(testRef);
        console.log('✅ Test file cleaned up');
      } catch (deleteError) {
        console.warn('⚠️ Could not delete test file (not critical):', deleteError);
      }

      return true;

    } catch (uploadError) {
      console.error('❌ Test upload failed:', uploadError);

      // Provide specific error information
      if (uploadError instanceof Error) {
        if (uploadError.message.includes('storage/unauthorized')) {
          console.error('🔒 PERMISSION ISSUE: Firebase Storage rules may be blocking uploads');
          console.error('📋 Check Firebase Console > Storage > Rules');
          console.error('📋 Rules should allow: allow read, write: if request.auth != null;');
        } else if (uploadError.message.includes('storage/quota-exceeded')) {
          console.error('💾 QUOTA ISSUE: Firebase Storage quota exceeded');
        } else if (uploadError.message.includes('network')) {
          console.error('🌐 NETWORK ISSUE: Check internet connection');
        }
      }

      return false;
    }

  } catch (error) {
    console.error('❌ Storage connectivity test failed:', error);
    return false;
  }
};

/**
 * Comprehensive debug function for production image upload issues
 * @returns Promise<object> - Debug information
 */
export const debugImageUploadIssues = async (): Promise<object> => {
  const debugInfo = {
    platform: Platform.OS,
    timestamp: new Date().toISOString(),
    authentication: {
      isAuthenticated: false,
      userId: null,
      tokenValid: false,
      error: null
    },
    storage: {
      connectivity: false,
      error: null
    },
    network: {
      online: true, // Assume online, can't easily test in RN
      error: null
    }
  };

  try {
    // Test authentication
    console.log('🔍 Debugging authentication...');
    const currentUser = auth.currentUser;
    if (currentUser) {
      debugInfo.authentication.isAuthenticated = true;
      debugInfo.authentication.userId = currentUser.uid;

      try {
        await currentUser.getIdToken(true);
        debugInfo.authentication.tokenValid = true;
        console.log('✅ Authentication: Valid');
      } catch (tokenError) {
        debugInfo.authentication.error = (tokenError as Error).message;
        console.log('❌ Authentication: Token invalid');
      }
    } else {
      debugInfo.authentication.error = 'No current user';
      console.log('❌ Authentication: No user');
    }

    // Test storage connectivity
    console.log('🔍 Debugging storage connectivity...');
    try {
      debugInfo.storage.connectivity = await testStorageConnectivity();
      console.log('✅ Storage: Connected');
    } catch (storageError) {
      debugInfo.storage.error = (storageError as Error).message;
      console.log('❌ Storage: Connection failed');
    }

    console.log('🔍 Debug Summary:', debugInfo);
    return debugInfo;

  } catch (error) {
    console.error('Debug function failed:', error);
    return {
      ...debugInfo,
      debugError: (error as Error).message
    };
  }
};

// Export storage instance for advanced usage
export { storage };
