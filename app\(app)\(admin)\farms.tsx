import React, { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useFocusEffect } from "@react-navigation/native";
import { getFarmsByUserId, Farm } from "@/services/farm-service";
import { getUsersByFarm } from "@/services/user-service";
import { Search, MapPin, Users, Package, Eye, ArrowLeft } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { router, Stack } from "expo-router";

export default function AdminFarmsScreen() {
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { refreshFarms } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === "dark";
  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#374151" : "#e2e8f0";
  const primaryColor = colors.primary;

  const [farms, setFarms] = useState<Farm[]>([]);
  const [filteredFarms, setFilteredFarms] = useState<Farm[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [caretakerCounts, setCaretakerCounts] = useState<{[farmId: string]: number}>({});

  // Helper function to safely get location display text
  const getLocationDisplay = (farm: Farm): string => {
    if (farm.locationData && typeof farm.locationData === 'object') {
      return farm.locationData.address || t('common.notSet');
    }
    
    if (farm.location) {
      if (typeof farm.location === 'object') {
        return farm.location.address || t('common.notSet');
      }
      if (typeof farm.location === 'string') {
        return farm.location;
      }
    }
    
    return t('common.notSet');
  };

  const loadFarms = async () => {
    try {
      setLoading(true);
      if (user) {
        const data = await getFarmsByUserId(user.uid, user.role);
        setFarms(data);
        setFilteredFarms(data);

        // Get caretaker counts for each farm (each farm has its own count)
        console.log("Loading caretaker counts for each farm...");
        const counts: {[farmId: string]: number} = {};
        for (const farm of data) {
          try {
            const caretakers = await getUsersByFarm(farm.id);
            counts[farm.id] = caretakers.length;
            console.log(`Farm ${farm.name} has ${caretakers.length} caretakers`);
          } catch (error) {
            console.error(`Error getting caretakers for farm ${farm.id}:`, error);
            counts[farm.id] = 0;
          }
        }
        setCaretakerCounts(counts);
      }
    } catch (error) {
      console.error("Error loading farms:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFarms();
  }, [user?.uid]); // Only reload when user ID changes, not on every user object change

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log("Farms screen focused, refreshing data...");
      loadFarms();
    }, [])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadFarms();
    setRefreshing(false);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === "") {
      setFilteredFarms(farms);
    } else {
      const filtered = farms.filter(farm =>
        farm.name.toLowerCase().includes(text.toLowerCase()) ||
        getLocationDisplay(farm).toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFarms(filtered);
    }
  };

  const handleViewFarmDetails = (farm: Farm) => {
    // Navigate to farm detail screen (read-only for admins)
    router.push({
      pathname: "/(app)/(admin)/farms/[id]",
      params: { id: farm.id }
    });
  };

  const renderFarmCard = ({ item }: { item: Farm }) => {
    return (
      <TouchableOpacity
        style={[styles.farmCard, { backgroundColor: cardColor, borderColor }]}
        onPress={() => handleViewFarmDetails(item)}
      >
        <View style={[styles.farmCardHeader, isRTL && styles.rtlFarmCardHeader]}>
          <Text style={[styles.farmName, { color: textColor }]}>{item.name}</Text>
          <View style={[styles.farmActions, isRTL && styles.rtlFarmActions]}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary + "20" }]}
              onPress={() => handleViewFarmDetails(item)}
            >
              <Eye size={16} color={colors.primary} />
            </TouchableOpacity>
            {/* Admin can only view farms - no edit/delete buttons */}
          </View>
        </View>

        <View style={[styles.farmInfo, isRTL && styles.rtlFarmInfo]}>
          <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
            <MapPin size={16} color={colors.primary} />
            <Text style={[styles.infoText, { color: secondaryTextColor }, isRTL && styles.rtlInfoText]}>
              {getLocationDisplay(item)}
            </Text>
          </View>
          {item.size && (
            <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
              <Text style={[styles.infoText, { color: secondaryTextColor }]}>
                {item.size} {item.sizeUnit || t('farm.acres')}
              </Text>
            </View>
          )}
          {item.type && (
            <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
              <Text style={[styles.farmType, { color: colors.primary }]}>
                {t(`farm.${item.type}Farm`) || item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Text>
            </View>
          )}
        </View>

        {item.description && (
          <Text style={[styles.description, { color: secondaryTextColor }, isRTL && styles.rtlDescription]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        <View style={[styles.farmStats, isRTL && styles.rtlFarmStats]}>
          <View style={[styles.statItem, isRTL && styles.rtlStatItem]}>
            <Users size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: textColor }, isRTL && styles.rtlStatText]}>
              {caretakerCounts[item.id] || 0} {t('farm.caretakers')}
            </Text>
          </View>
          <View style={[styles.statItem, isRTL && styles.rtlStatItem]}>
            <Package size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: textColor }, isRTL && styles.rtlStatText]}>
              {item.inventoryCount || 0} {t('common.totalInventory')}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyComponent = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
            {t("common.loading")}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <MapPin size={64} color={secondaryTextColor} />
        <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
          {searchQuery ? t("farms.noFarmsFound") : t("farms.noAssignedFarms")}
        </Text>
        {searchQuery && (
          <Text style={[styles.emptySubtext, { color: secondaryTextColor }]}>
            {t("farms.tryDifferentSearch")}
          </Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('farm.title'),
          headerStyle: {
            backgroundColor: primaryColor,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
        <Search size={20} color={secondaryTextColor} />
        <TextInput
          style={[styles.searchInput, { color: textColor }]}
          placeholder={t("farm.searchFarms")}
          placeholderTextColor={secondaryTextColor}
          value={searchQuery}
          onChangeText={handleSearch}
        />
      </View>

      {/* Farms List */}
      <FlatList
        data={filteredFarms}
        renderItem={renderFarmCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={handleRefresh}
            colors={[primaryColor]}
            tintColor={primaryColor}
          />
        }
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  farmCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  farmHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  farmInfo: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 12,
    gap: 12,
  },
  farmName: {
    fontSize: 18,
    fontWeight: "600",
    flex: 1,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  farmLocation: {
    fontSize: 14,
    marginLeft: 6,
  },
  farmStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  statText: {
    fontSize: 14,
    marginLeft: 6,
  },
  farmActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 6,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
  },
  farmCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  rtlFarmCardHeader: {
    flexDirection: "row-reverse",
  },
  farmActions: {
    flexDirection: "row",
    gap: 8,
  },
  rtlFarmActions: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  rtlFarmInfo: {
    alignItems: "flex-end",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rtlInfoItem: {
    flexDirection: "row-reverse",
  },
  infoText: {
    fontSize: 14,
  },
  rtlInfoText: {
    textAlign: "right",
  },
  farmType: {
    fontSize: 12,
    fontWeight: "600",
    textTransform: "capitalize",
  },
  rtlFarmStats: {
    flexDirection: "row-reverse",
  },
  rtlStatItem: {
    flexDirection: "row-reverse",
  },
  rtlStatText: {
    textAlign: "right",
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  rtlDescription: {
    textAlign: "right",
  },
});
