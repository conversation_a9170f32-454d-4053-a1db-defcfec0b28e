import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl, Dimensions, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { getInventoryReport, getUsersReport, getRequestsReport, getActivitiesReport, exportReport } from "@/services/reports-service";
import { getMachineryReport } from "@/services/machinery-service";
import { Download, Share, BarChart3, Users, AlertTriangle, TrendingUp, Activity, ArrowLeft } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

const { width } = Dimensions.get('window');

export default function ReportDetailsScreen() {
  const { type, farmId } = useLocalSearchParams();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  const [exporting, setExporting] = useState(false);
  
  useEffect(() => {
    loadReportData();
  }, [type, farmId, selectedFarm]);
  
  const loadReportData = async () => {
    setLoading(true);
    try {
      const targetFarmId = farmId as string || selectedFarm?.id;
      
      if (type === "inventory") {
        const data = await getInventoryReport(targetFarmId);
        setReportData(data);
      } else if (type === "users") {
        const data = await getUsersReport(targetFarmId);
        setReportData(data);
      } else if (type === "requests") {
        const data = await getRequestsReport(targetFarmId);
        setReportData(data);
      } else if (type === "machinery") {
        const data = await getMachineryReport(targetFarmId);
        setReportData(data);
      } else if (type === "activity") {
        const data = await getActivitiesReport(targetFarmId);
        setReportData(data);
      }
    } catch (error) {
      console.error("Error loading report data:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadReportData();
  };

  const handleExport = async (format: 'pdf' | 'excel') => {
    setExporting(true);
    try {
      await exportReport(type as string, format);
      // Show success message
    } catch (error) {
      console.error("Export failed:", error);
    } finally {
      setExporting(false);
    }
  };
  
  const getReportTitle = () => {
    switch (type) {
      case "inventory":
        return "Inventory Report";
      case "users":
        return "Team Report";
      case "requests":
        return "Requests Report";
      case "machinery":
        return "Machinery Report";
      case "activity":
        return "Activity Report";
      default:
        return "Report Details";
    }
  };

  const getReportIcon = () => {
    switch (type) {
      case "inventory":
        return BarChart3;
      case "users":
        return Users;
      case "requests":
        return AlertTriangle;
      case "machinery":
        return TrendingUp;
      case "activity":
        return Activity;
      default:
        return BarChart3;
    }
  };
  
  const renderInventoryReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.success + '20', colors.success + '10']} style={styles.summaryGradient}>
              <BarChart3 size={24} color={colors.success} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.totalItems || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Items</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.warning + '20', colors.warning + '10']} style={styles.summaryGradient}>
              <AlertTriangle size={24} color={colors.warning} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.lowStockItems || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Low Stock</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.primary + '20', colors.primary + '10']} style={styles.summaryGradient}>
              <TrendingUp size={24} color={colors.primary} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                ${(reportData.summary?.totalValue || 0).toFixed(0)}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Value</Text>
            </LinearGradient>
          </View>
        </View>

        {/* Category Breakdown */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Inventory by Category</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.categoryData && reportData.categoryData.length > 0 ? (
              <View style={styles.categoryList}>
                {reportData.categoryData.map((item: any, index: number) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                    <Text style={[styles.categoryName, { color: colors.text }]}>{item.name}</Text>
                    <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>{item.count}</Text>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No category data available</Text>
            )}
          </View>
        </View>
        
        {/* Top Items */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Top Items by Quantity</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.inventoryLevels && reportData.inventoryLevels.labels.length > 0 ? (
              <View style={styles.barChartContainer}>
                {reportData.inventoryLevels.labels.map((label: string, index: number) => (
                  <View key={index} style={styles.barItem}>
                    <Text style={[styles.barLabel, { color: colors.text }]}>{label}</Text>
                    <View style={styles.barContainer}>
                      <View 
                        style={[
                          styles.bar, 
                          { 
                            width: `${(reportData.inventoryLevels.datasets[0].data[index] / Math.max(...reportData.inventoryLevels.datasets[0].data)) * 100}%`,
                            backgroundColor: colors.primary
                          }
                        ]} 
                      />
                      <Text style={[styles.barValue, { color: colors.text }]}>
                        {reportData.inventoryLevels.datasets[0].data[index]}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No inventory data available</Text>
            )}
          </View>
        </View>
      </View>
    );
  };
  
  const renderUsersReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.primary + '20', colors.primary + '10']} style={styles.summaryGradient}>
              <Users size={24} color={colors.primary} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.totalUsers || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Users</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.success + '20', colors.success + '10']} style={styles.summaryGradient}>
              <TrendingUp size={24} color={colors.success} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.activeUsers || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Active Users</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.info + '20', colors.info + '10']} style={styles.summaryGradient}>
              <BarChart3 size={24} color={colors.info} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.admins || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Admins</Text>
            </LinearGradient>
          </View>
        </View>

        {/* Role Distribution */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Users by Role</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.roleData && reportData.roleData.length > 0 ? (
              <View style={styles.categoryList}>
                {reportData.roleData.map((item: any, index: number) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                    <Text style={[styles.categoryName, { color: colors.text }]}>{item.name}</Text>
                    <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>{item.count}</Text>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No user role data available</Text>
            )}
          </View>
        </View>
      </View>
    );
  };
  
  const renderRequestsReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.primary + '20', colors.primary + '10']} style={styles.summaryGradient}>
              <BarChart3 size={24} color={colors.primary} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.totalRequests || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Requests</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.warning + '20', colors.warning + '10']} style={styles.summaryGradient}>
              <AlertTriangle size={24} color={colors.warning} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.pendingRequests || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Pending</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.success + '20', colors.success + '10']} style={styles.summaryGradient}>
              <TrendingUp size={24} color={colors.success} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.approvedRequests || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Approved</Text>
            </LinearGradient>
          </View>
        </View>

        {/* Status Distribution */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Requests by Status</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.statusData && reportData.statusData.length > 0 ? (
              <View style={styles.categoryList}>
                {reportData.statusData.map((item: any, index: number) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                    <Text style={[styles.categoryName, { color: colors.text }]}>{item.name}</Text>
                    <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>{item.count}</Text>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No request status data available</Text>
            )}
          </View>
        </View>
      </View>
    );
  };
  
  const renderActivityReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.primary + '20', colors.primary + '10']} style={styles.summaryGradient}>
              <BarChart3 size={24} color={colors.primary} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.totalActivities || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Activities</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.success + '20', colors.success + '10']} style={styles.summaryGradient}>
              <Activity size={24} color={colors.success} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.todayActivities || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Today</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.info + '20', colors.info + '10']} style={styles.summaryGradient}>
              <TrendingUp size={24} color={colors.info} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.weekActivities || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>This Week</Text>
            </LinearGradient>
          </View>
        </View>

        {/* Activity Types */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Activity by Type</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.activityTypeData && reportData.activityTypeData.length > 0 ? (
              <View style={styles.categoryList}>
                {reportData.activityTypeData.map((item: any, index: number) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                    <Text style={[styles.categoryName, { color: colors.text }]}>{item.name}</Text>
                    <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>{item.count}</Text>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No activity data available</Text>
            )}
          </View>
        </View>
      </View>
    );
  };
  
  const renderMachineryReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.primary + '20', colors.primary + '10']} style={styles.summaryGradient}>
              <BarChart3 size={24} color={colors.primary} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.totalMachinery || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Total Machinery</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.success + '20', colors.success + '10']} style={styles.summaryGradient}>
              <TrendingUp size={24} color={colors.success} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.activeMachinery || 0}
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Active</Text>
            </LinearGradient>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <LinearGradient colors={[colors.info + '20', colors.info + '10']} style={styles.summaryGradient}>
              <Activity size={24} color={colors.info} />
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {reportData.summary?.utilizationRate || 0}%
              </Text>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Utilization</Text>
            </LinearGradient>
          </View>
        </View>

        {/* Status Distribution */}
        <View style={[styles.chartCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Machinery by Status</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.statusData && reportData.statusData.length > 0 ? (
              <View style={styles.categoryList}>
                {reportData.statusData.map((item: any, index: number) => (
                  <View key={index} style={styles.categoryItem}>
                    <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                    <Text style={[styles.categoryName, { color: colors.text }]}>{item.name}</Text>
                    <Text style={[styles.categoryCount, { color: colors.textSecondary }]}>{item.count}</Text>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No machinery status data available</Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  const ReportIcon = getReportIcon();
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: getReportTitle(),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerActions}>
              <TouchableOpacity 
                style={[styles.headerButton, { backgroundColor: colors.primary + '20' }]}
                onPress={() => handleExport('pdf')}
                disabled={exporting}
              >
                <Download size={18} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.headerButton, { backgroundColor: colors.info + '20' }]}
              >
                <Share size={18} color={colors.info} />
              </TouchableOpacity>
            </View>
          ),
        }} 
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading report...</Text>
          </View>
        ) : (
          <>
            {/* Header */}
            <View style={styles.reportHeader}>
              <LinearGradient
                colors={[colors.primary + '20', colors.primary + '10']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.reportHeaderGradient}
              >
                <View style={styles.reportHeaderContent}>
                  <View style={[styles.reportHeaderIcon, { backgroundColor: colors.primary + '20' }]}>
                    <ReportIcon size={32} color={colors.primary} />
                  </View>
                  <View style={styles.reportHeaderText}>
                    <Text style={[styles.reportHeaderTitle, { color: colors.text }]}>
                      {getReportTitle()}
                    </Text>
                    <Text style={[styles.reportHeaderSubtitle, { color: colors.textSecondary }]}>
                      {selectedFarm ? `${selectedFarm.name} • Detailed Analysis` : "All Farms"}
                    </Text>
                  </View>
                </View>
              </LinearGradient>
            </View>
            
            {/* Report Content */}
            {type === "inventory" && renderInventoryReport()}
            {type === "users" && renderUsersReport()}
            {type === "requests" && renderRequestsReport()}
            {type === "activity" && renderActivityReport()}
            {type === "machinery" && renderMachineryReport()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: "500",
  },
  reportHeader: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  reportHeaderGradient: {
    padding: 20,
  },
  reportHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportHeaderIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  reportHeaderText: {
    flex: 1,
  },
  reportHeaderTitle: {
    fontSize: 24,
    fontWeight: "800",
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  reportHeaderSubtitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  reportContainer: {
    paddingHorizontal: 16,
    gap: 20,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    width: (width - 44) / 3,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryGradient: {
    padding: 16,
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "800",
    marginTop: 8,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: 'center',
  },
  chartCard: {
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  chartPlaceholder: {
    minHeight: 120,
  },
  emptyText: {
    fontSize: 14,
    fontStyle: "italic",
    textAlign: 'center',
    marginTop: 40,
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
  },
  categoryCount: {
    fontSize: 16,
    fontWeight: "600",
  },
  barChartContainer: {
    gap: 12,
  },
  barItem: {
    gap: 8,
  },
  barLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  barContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 32,
  },
  bar: {
    height: 24,
    borderRadius: 12,
    marginRight: 12,
  },
  barValue: {
    fontSize: 14,
    fontWeight: "600",
    minWidth: 40,
  },
});