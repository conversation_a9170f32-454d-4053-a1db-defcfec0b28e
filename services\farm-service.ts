import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, documentId, serverTimestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getInventoryItems, InventoryItem } from "./inventory-service";

const db = getFirestore();

// Define Farm type with all the new properties
export interface Farm {
  id: string;
  name: string;
  location: string | {
    address?: string;
    latitude?: number;
    longitude?: number;
  };
  locationData?: {
    address?: string;
    latitude?: number;
    longitude?: number;
  };
  ownerId?: string;
  ownerUid?: string; // New field
  createdBy?: string; // New field
  managerIds?: string[];
  caretakerIds?: string[];
  size?: string | number;
  sizeUnit?: string;
  type?: string; // New field - farm type (crop, livestock, mixed, etc.)
  soilType?: string;
  description?: string;
  caretakerCount?: number;
  inventoryCount?: number;
  createdAt?: string | any; // Can be string or Firestore timestamp
  updatedAt?: string | any;
  status?: "active" | "inactive";
  notes?: string;
  photoURL?: string; // New field
}

// Helper function to safely get location display text
export const getLocationDisplay = (farm: Farm): string => {
  try {
    if (!farm.location && !farm.locationData) {
      return 'No location';
    }
    
    if (farm.location) {
      if (typeof farm.location === 'string') {
        return farm.location;
      }
      
      if (typeof farm.location === 'object' && farm.location !== null) {
        const locationObj = farm.location as any;
        if (locationObj.address && typeof locationObj.address === 'string') {
          return locationObj.address;
        }
        
        if (locationObj.latitude && locationObj.longitude) {
          return `${String(locationObj.latitude)}, ${String(locationObj.longitude)}`;
        }
      }
    }
    
    if (farm.locationData) {
      if (typeof farm.locationData === 'string') {
        return farm.locationData;
      }
      
      if (typeof farm.locationData === 'object' && farm.locationData !== null) {
        const locationDataObj = farm.locationData as any;
        if (locationDataObj.address && typeof locationDataObj.address === 'string') {
          return locationDataObj.address;
        }
        
        if (locationDataObj.latitude && locationDataObj.longitude) {
          return `${String(locationDataObj.latitude)}, ${String(locationDataObj.longitude)}`;
        }
      }
    }
    
    return 'No address available';
  } catch (error) {
    console.error('Error getting location display:', error);
    return 'Location unavailable';
  }
};

// Mock data for offline development
const mockFarms: Farm[] = [
  {
    id: "1",
    name: "North Farm",
    location: {
      address: "123 North Road, Northern District",
      latitude: 34.0522,
      longitude: -118.2437
    },
    locationData: {
      address: "123 North Road, Northern District",
      latitude: 34.0522,
      longitude: -118.2437
    },
    ownerId: "owner123",
    ownerUid: "owner123",
    createdBy: "owner123",
    managerIds: ["admin1", "admin2"],
    caretakerIds: ["caretaker1", "caretaker2", "caretaker3"],
    size: 120,
    sizeUnit: "acres",
    type: "mixed",
    soilType: "Loamy",
    description: "Main crop production farm with irrigation system",
    caretakerCount: 3,
    inventoryCount: 45,
    status: "active",
    photoURL: "https://images.unsplash.com/photo-1500382017468-9049fed747ef?q=80&w=800&auto=format&fit=crop",
    createdAt: "2023-01-15T08:30:00Z",
    updatedAt: "2023-06-20T14:15:00Z"
  },
  {
    id: "2",
    name: "South Farm",
    location: {
      address: "456 South Avenue, Southern District",
      latitude: 33.8121,
      longitude: -117.9190
    },
    locationData: {
      address: "456 South Avenue, Southern District",
      latitude: 33.8121,
      longitude: -117.9190
    },
    ownerId: "owner123",
    ownerUid: "owner123",
    createdBy: "owner123",
    managerIds: ["admin1"],
    caretakerIds: ["caretaker2", "caretaker4"],
    size: 85,
    sizeUnit: "acres",
    type: "crop",
    soilType: "Sandy loam",
    description: "Specialized in vegetable production",
    caretakerCount: 2,
    inventoryCount: 32,
    status: "active",
    photoURL: "https://images.unsplash.com/photo-1574943320219-553eb213f72d?q=80&w=800&auto=format&fit=crop",
    createdAt: "2023-02-10T10:45:00Z",
    updatedAt: "2023-07-05T09:20:00Z"
  },
  {
    id: "3",
    name: "East Farm",
    location: {
      address: "789 East Boulevard, Eastern District",
      latitude: 34.1478,
      longitude: -118.1445
    },
    locationData: {
      address: "789 East Boulevard, Eastern District",
      latitude: 34.1478,
      longitude: -118.1445
    },
    ownerId: "owner123",
    ownerUid: "owner123",
    createdBy: "owner123",
    managerIds: ["admin3"],
    caretakerIds: ["caretaker1"],
    size: 60,
    sizeUnit: "acres",
    type: "orchard",
    soilType: "Clay",
    description: "Fruit orchard and nursery",
    caretakerCount: 1,
    inventoryCount: 28,
    status: "active",
    photoURL: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?q=80&w=800&auto=format&fit=crop",
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-08-12T16:30:00Z"
  },
  {
    id: "4",
    name: "West Farm",
    location: {
      address: "101 West Street, Western District",
      latitude: 34.0211,
      longitude: -118.4814
    },
    locationData: {
      address: "101 West Street, Western District",
      latitude: 34.0211,
      longitude: -118.4814
    },
    ownerId: "owner123",
    ownerUid: "owner123",
    createdBy: "owner123",
    managerIds: ["admin2"],
    caretakerIds: ["caretaker3", "caretaker5"],
    size: 95,
    sizeUnit: "acres",
    type: "livestock",
    soilType: "Silt loam",
    description: "Livestock and fodder production",
    caretakerCount: 2,
    inventoryCount: 38,
    status: "active",
    photoURL: "https://images.unsplash.com/photo-1500595046743-cd271d694d30?q=80&w=800&auto=format&fit=crop",
    createdAt: "2023-04-20T11:30:00Z",
    updatedAt: "2023-09-01T13:45:00Z"
  },
];

// Helper function to filter out undefined values from objects
const filterUndefinedValues = (obj: any): any => {
  const filtered: any = {};
  for (const key in obj) {
    if (obj[key] !== undefined && obj[key] !== null) {
      filtered[key] = obj[key];
    }
  }
  return filtered;
};

// Helper function to clear user-specific farm caches
const clearUserFarmCaches = async (userId?: string) => {
  try {
    const keysToRemove = ['farms'];
    if (userId) {
      keysToRemove.push(`userFarms_${userId}`);
    }
    
    // Get all keys and filter for user-specific farm caches
    const allKeys = await AsyncStorage.getAllKeys();
    const farmCacheKeys = allKeys.filter(key => 
      key.startsWith('userFarms_') || key === 'farms'
    );
    
    await AsyncStorage.multiRemove(farmCacheKeys);
    console.log("Cleared farm caches:", farmCacheKeys);
  } catch (error) {
    console.error("Error clearing farm caches:", error);
  }
};

// Get all farms
export const getFarms = async (): Promise<Farm[]> => {
  try {
    // Fetch from Firebase
    const farmsRef = collection(db, "farms");
    const farmsSnapshot = await getDocs(farmsRef);
    const farms = farmsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        // Ensure caretakerIds is always an array
        caretakerIds: data.caretakerIds || [],
      } as Farm;
    });

    // For each farm, get caretaker count
    for (const farm of farms) {
      farm.caretakerCount = farm.caretakerIds?.length || 0;
      
      // Get inventory count
      try {
        const inventoryRef = collection(db, `farms/${farm.id}/inventory`);
        const inventorySnapshot = await getDocs(inventoryRef);
        farm.inventoryCount = inventorySnapshot.size;
      } catch (error) {
        console.error(`Error getting inventory for farm ${farm.id}:`, error);
        farm.inventoryCount = 0;
      }
    }

    // Cache the data
    await AsyncStorage.setItem("farms", JSON.stringify(farms));
    
    return farms.length > 0 ? farms : mockFarms;
  } catch (error) {
    console.error("Error getting farms:", error);
    
    // Try to get from cache first
    const cachedFarms = await AsyncStorage.getItem("farms");
    if (cachedFarms) {
      return JSON.parse(cachedFarms);
    }
    
    // Return mock data as fallback
    return mockFarms;
  }
};

// Get farm by ID
export const getFarmById = async (farmId: string): Promise<Farm | null> => {
  try {
    // Fetch from Firebase
    const farmRef = doc(db, "farms", farmId);
    const farmSnapshot = await getDoc(farmRef);
    
    if (farmSnapshot.exists()) {
      const data = farmSnapshot.data();
      const farm = {
        ...data,
        id: farmSnapshot.id,
        // Ensure caretakerIds is always an array
        caretakerIds: data.caretakerIds || [],
      } as Farm;

      // Get caretaker count
      farm.caretakerCount = farm.caretakerIds?.length || 0;
      
      // Get inventory count
      try {
        const inventoryRef = collection(db, `farms/${farm.id}/inventory`);
        const inventorySnapshot = await getDocs(inventoryRef);
        farm.inventoryCount = inventorySnapshot.size;
      } catch (error) {
        console.error(`Error getting inventory for farm ${farm.id}:`, error);
        farm.inventoryCount = 0;
      }
      
      return farm;
    }

    // Try to get from cache
    const cachedFarms = await AsyncStorage.getItem("farms");
    if (cachedFarms) {
      const farms: Farm[] = JSON.parse(cachedFarms);
      const farm = farms.find((farm) => farm.id === farmId);
      if (farm) return farm;
    }

    // Return mock data as fallback
    return mockFarms.find((farm) => farm.id === farmId) || null;
  } catch (error) {
    console.error("Error getting farm:", error);
    
    // Try to get from cache
    const cachedFarms = await AsyncStorage.getItem("farms");
    if (cachedFarms) {
      const farms: Farm[] = JSON.parse(cachedFarms);
      const farm = farms.find((farm) => farm.id === farmId);
      if (farm) return farm;
    }
    
    // Return mock data as fallback
    return mockFarms.find((farm) => farm.id === farmId) || null;
  }
};

// Get farms by array of IDs
export const getFarmsByIds = async (farmIds: string[]): Promise<Farm[]> => {
  try {
    if (!farmIds || farmIds.length === 0) {
      return [];
    }

    console.log("Getting farms by IDs:", farmIds);

    // Firestore 'in' queries are limited to 10 items, so we need to batch them
    const batchSize = 10;
    const batches: string[][] = [];
    
    for (let i = 0; i < farmIds.length; i += batchSize) {
      batches.push(farmIds.slice(i, i + batchSize));
    }

    const allFarms: Farm[] = [];

    // Process each batch
    for (const batch of batches) {
      try {
        const farmsRef = collection(db, "farms");
        const farmsQuery = query(farmsRef, where(documentId(), "in", batch));
        const farmsSnapshot = await getDocs(farmsQuery);
        
        const batchFarms = farmsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            ...data,
            id: doc.id,
            // Ensure caretakerIds is always an array
            caretakerIds: data.caretakerIds || [],
            caretakerCount: (data.caretakerIds?.length || 0),
            inventoryCount: 0 // Will be updated below
          } as Farm;
        });

        allFarms.push(...batchFarms);
      } catch (error) {
        console.error("Error getting batch of farms:", error);
      }
    }

    // For each farm, get inventory count
    for (const farm of allFarms) {
      try {
        const inventoryRef = collection(db, `farms/${farm.id}/inventory`);
        const inventorySnapshot = await getDocs(inventoryRef);
        farm.inventoryCount = inventorySnapshot.size;
      } catch (error) {
        console.error(`Error getting inventory for farm ${farm.id}:`, error);
        farm.inventoryCount = 0;
      }
    }

    console.log("Found farms:", allFarms.length);
    return allFarms;
  } catch (error) {
    console.error("Error getting farms by IDs:", error);
    
    // Try to get from cache as fallback
    try {
      const cachedFarms = await AsyncStorage.getItem("farms");
      if (cachedFarms) {
        const farms: Farm[] = JSON.parse(cachedFarms);
        return farms.filter(farm => farmIds.includes(farm.id));
      }
    } catch (cacheError) {
      console.error("Error getting cached farms:", cacheError);
    }
    
    // Return mock data as fallback
    return mockFarms.filter(farm => farmIds.includes(farm.id));
  }
};

// Get farms by user ID using assignedFarmIds
export const getFarmsByUserId = async (userId: string, role: string): Promise<Farm[]> => {
  try {
    console.log("Getting farms for user:", userId, "role:", role);
    
    // First get the user document to retrieve assignedFarmIds
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log("User document not found");
      
      // Don't use cache for new users - they should start fresh
      console.log("New user detected, returning empty farms list");
      return [];
    }
    
    // Extract the assignedFarmIds array
    const userData = userDoc.data();
    const assignedFarmIds = userData.assignedFarmIds || [];
    
    console.log("User assigned farm IDs:", assignedFarmIds);
    
    // For owners, always get all farms they own (regardless of assignedFarmIds)
    if (role === "owner") {
      console.log("Owner user detected, getting all owned farms");

      // Try to get farms where this user is the owner
      const farmsRef = collection(db, "farms");
      const ownerQuery = query(farmsRef, where("ownerId", "==", userId));
      const ownerSnapshot = await getDocs(ownerQuery);

      // Also check for ownerUid field
      const ownerUidQuery = query(farmsRef, where("ownerUid", "==", userId));
      const ownerUidSnapshot = await getDocs(ownerUidQuery);

      const ownedFarms: Farm[] = [];

      // Process ownerId results
      ownerSnapshot.forEach((farmDoc) => {
        const farmData = farmDoc.data() as Farm;
        ownedFarms.push({
          ...farmData,
          id: farmDoc.id,
          // Ensure caretakerIds is always an array
          caretakerIds: farmData.caretakerIds || [],
          caretakerCount: farmData.caretakerIds?.length || 0,
          inventoryCount: 0 // Will be updated below
        });
      });

      // Process ownerUid results (avoid duplicates)
      ownerUidSnapshot.forEach((farmDoc) => {
        const farmData = farmDoc.data() as Farm;
        const existingFarm = ownedFarms.find(f => f.id === farmDoc.id);
        if (!existingFarm) {
          ownedFarms.push({
            ...farmData,
            id: farmDoc.id,
            // Ensure caretakerIds is always an array
            caretakerIds: farmData.caretakerIds || [],
            caretakerCount: farmData.caretakerIds?.length || 0,
            inventoryCount: 0 // Will be updated below
          });
        }
      });
      
      // For each farm, get inventory count
      for (const farm of ownedFarms) {
        try {
          const inventoryRef = collection(db, `farms/${farm.id}/inventory`);
          const inventorySnapshot = await getDocs(inventoryRef);
          farm.inventoryCount = inventorySnapshot.size;
        } catch (error) {
          console.error(`Error getting inventory for farm ${farm.id}:`, error);
          farm.inventoryCount = 0;
        }
      }
      
      console.log("Found owned farms:", ownedFarms.length);
      return ownedFarms;
    }
    
    // If no farms assigned to this user
    if (assignedFarmIds.length === 0) {
      console.log("No farms assigned to this user");
      return [];
    }
    
    // Use the new getFarmsByIds function
    const farms = await getFarmsByIds(assignedFarmIds);
    
    // Cache the farms with user-specific key
    await AsyncStorage.setItem(`userFarms_${userId}`, JSON.stringify(farms));
    
    console.log("Returning farms:", farms.length);
    return farms;
  } catch (error) {
    console.error("Error getting farms by user ID:", error);
    
    // For new users or errors, don't return cached data - let them start fresh
    console.log("Error occurred, returning empty farms list for fresh start");
    return [];
  }
};

// Create farm - Only owners can create farms
export const createFarm = async (farmData: Partial<Farm>, userRole?: string): Promise<Farm> => {
  try {
    // Permission check: Only owners can create farms
    if (userRole && userRole !== 'owner') {
      throw new Error('Unauthorized: Only owners can create farms');
    }

    const newFarm = filterUndefinedValues({
      ...farmData,
      // Ensure caretakerIds is always an array (empty if not provided)
      caretakerIds: farmData.caretakerIds || [],
      caretakerCount: farmData.caretakerIds?.length || 0,
      inventoryCount: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      status: "active",
      // Ensure both ownerId and ownerUid are set for compatibility
      ownerId: farmData.ownerId || farmData.ownerUid,
      ownerUid: farmData.ownerUid || farmData.ownerId,
    });

    // Add to Firebase
    const farmsRef = collection(db, "farms");
    const docRef = await addDoc(farmsRef, newFarm);
    const createdFarm: Farm = {
      ...newFarm as Farm,
      id: docRef.id,
      createdAt: new Date().toISOString(), // For local representation
      updatedAt: new Date().toISOString(),
    };

    // Clear caches to force refresh
    await clearUserFarmCaches();

    return createdFarm;
  } catch (error) {
    console.error("Error creating farm:", error);
    throw error;
  }
};

// Update farm - Only owners can update farms
export const updateFarm = async (farmId: string, farmData: Partial<Farm>, userRole?: string): Promise<Farm> => {
  try {
    // Permission check: Only owners can update farms
    if (userRole && userRole !== 'owner') {
      throw new Error('Unauthorized: Only owners can update farms');
    }

    const updatedFarm = filterUndefinedValues({
      ...farmData,
      updatedAt: serverTimestamp(),
      // Ensure caretakerIds is always an array (empty if not provided)
      caretakerIds: farmData.caretakerIds !== undefined ? farmData.caretakerIds : undefined,
      // Ensure caretaker count is updated
      caretakerCount: farmData.caretakerIds?.length || 0,
    });

    // Update in Firebase
    const farmRef = doc(db, "farms", farmId);
    await updateDoc(farmRef, updatedFarm);

    // Clear caches to force refresh
    await clearUserFarmCaches();

    // Get the updated farm
    const farm = await getFarmById(farmId);
    if (!farm) {
      throw new Error("Farm not found after update");
    }

    return farm;
  } catch (error) {
    console.error("Error updating farm:", error);
    throw error;
  }
};

// Delete farm - Only owners can delete farms
export const deleteFarm = async (farmId: string, userRole?: string): Promise<boolean> => {
  try {
    // Permission check: Only owners can delete farms
    if (userRole && userRole !== 'owner') {
      throw new Error('Unauthorized: Only owners can delete farms');
    }

    // First, delete all inventory items for this farm
    try {
      const inventoryRef = collection(db, `farms/${farmId}/inventory`);
      const inventorySnapshot = await getDocs(inventoryRef);

      for (const doc of inventorySnapshot.docs) {
        await deleteDoc(doc.ref);
      }
    } catch (error) {
      console.error(`Error deleting inventory for farm ${farmId}:`, error);
    }
    
    // Then delete the farm itself
    const farmRef = doc(db, "farms", farmId);
    await deleteDoc(farmRef);

    // Clear caches to force refresh
    await clearUserFarmCaches();

    return true;
  } catch (error) {
    console.error("Error deleting farm:", error);
    throw error;
  }
};

// Get farm inventory statistics
export const getFarmInventoryStats = async (farmId: string): Promise<{
  totalItems: number;
  lowStockCount: number;
  expiryAlerts: number;
  categoryCounts: Record<string, number>;
}> => {
  try {
    // Get all inventory items for this farm
    let farmItems: InventoryItem[] = [];
    
    try {
      const inventoryRef = collection(db, `farms/${farmId}/inventory`);
      const inventorySnapshot = await getDocs(inventoryRef);
      farmItems = inventorySnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
        location: farmId // Ensure location is set to farmId
      } as InventoryItem));
    } catch (error) {
      console.error(`Error getting inventory for farm ${farmId}:`, error);
      
      // Fallback to the inventory service
      const allItems = await getInventoryItems();
      farmItems = allItems.filter(item => item.location === farmId);
    }
    
    // Calculate statistics
    const lowStockCount = farmItems.filter(item => item.quantity <= item.minQuantity).length;
    
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    const expiryAlerts = farmItems.filter(item => {
      if (!item.expiryDate) return false;
      const expiryDate = new Date(item.expiryDate);
      return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
    }).length;
    
    // Count items by category
    const categoryCounts: Record<string, number> = {};
    farmItems.forEach(item => {
      const category = item.category;
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });
    
    return {
      totalItems: farmItems.length,
      lowStockCount,
      expiryAlerts,
      categoryCounts
    };
  } catch (error) {
    console.error(`Error getting inventory stats for farm ${farmId}:`, error);
    return {
      totalItems: 0,
      lowStockCount: 0,
      expiryAlerts: 0,
      categoryCounts: {}
    };
  }
};

// Update farm inventory counts
export const updateFarmInventoryCounts = async (): Promise<void> => {
  try {
    // Get all farms
    const farms = await getFarms();
    
    // Update inventory counts for each farm
    for (const farm of farms) {
      try {
        const inventoryRef = collection(db, `farms/${farm.id}/inventory`);
        const inventorySnapshot = await getDocs(inventoryRef);
        
        // Update in Firebase
        const farmRef = doc(db, "farms", farm.id);
        await updateDoc(farmRef, filterUndefinedValues({ 
          inventoryCount: inventorySnapshot.size,
          updatedAt: serverTimestamp()
        }));
        
        // Update local farm object
        farm.inventoryCount = inventorySnapshot.size;
      } catch (error) {
        console.error(`Error updating inventory count for farm ${farm.id}:`, error);
      }
    }
    
    // Clear caches to force refresh
    await clearUserFarmCaches();
  } catch (error) {
    console.error("Error updating farm inventory counts:", error);
  }
};