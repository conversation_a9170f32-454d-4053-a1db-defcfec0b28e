import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { createMachineryRequest } from "@/services/machinery-service";

const requestTypes = [
  { value: 'use', label: 'Request New Machinery' },
  { value: 'maintenance', label: 'Request Maintenance Support' },
  { value: 'fuel', label: 'Request Fuel Support' },
];

const urgencyLevels = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'emergency', label: 'Emergency' },
];

export default function RequestFromOwnerScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [formData, setFormData] = useState({
    requestType: "use",
    reason: "",
    startDate: "",
    endDate: "",
    estimatedHours: "",
    fuelAmount: "",
    maintenanceType: "",
    urgency: "medium",
    machinerySpecs: "",
    budget: "",
  });
  const [loading, setLoading] = useState(false);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const inputBackgroundColor = isDarkMode ? "#2a2a2a" : "#f5f5f5";
  
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  const validateForm = () => {
    if (!formData.reason.trim()) {
      Alert.alert("Error", "Please provide a reason for the request");
      return false;
    }
    if (!formData.startDate.trim()) {
      Alert.alert("Error", "Please enter start date (YYYY-MM-DD format)");
      return false;
    }
    
    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(formData.startDate)) {
      Alert.alert("Error", "Please enter start date in YYYY-MM-DD format");
      return false;
    }
    
    if (formData.endDate && !dateRegex.test(formData.endDate)) {
      Alert.alert("Error", "Please enter end date in YYYY-MM-DD format");
      return false;
    }
    
    if (formData.requestType === 'use') {
      if (!formData.machinerySpecs.trim()) {
        Alert.alert("Error", "Please specify machinery requirements");
        return false;
      }
    }
    if (formData.requestType === 'fuel' && !formData.fuelAmount.trim()) {
      Alert.alert("Error", "Please enter fuel amount");
      return false;
    }
    if (formData.requestType === 'maintenance' && !formData.maintenanceType.trim()) {
      Alert.alert("Error", "Please specify maintenance type");
      return false;
    }
    
    // Validate numeric fields
    if (formData.estimatedHours && (isNaN(Number(formData.estimatedHours)) || Number(formData.estimatedHours) <= 0)) {
      Alert.alert("Error", "Please enter a valid estimated hours");
      return false;
    }
    if (formData.fuelAmount && (isNaN(Number(formData.fuelAmount)) || Number(formData.fuelAmount) <= 0)) {
      Alert.alert("Error", "Please enter a valid fuel amount");
      return false;
    }
    
    return true;
  };
  
  const handleSubmit = async () => {
    if (!selectedFarm || !user) {
      Alert.alert("Error", "Missing required information");
      return;
    }
    
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      const requestData = {
        machineryId: 'owner-request', // Special ID for owner requests
        requesterId: user.uid,
        requesterName: user.displayName || user.email || "Unknown User",
        fromFarmId: selectedFarm.id,
        toFarmId: 'owner', // Request to owner
        requestType: formData.requestType as any,
        reason: formData.reason.trim(),
        startDate: formData.startDate,
        endDate: formData.endDate || formData.startDate,
        estimatedHours: formData.estimatedHours ? Number(formData.estimatedHours) : undefined,
        fuelAmount: formData.fuelAmount ? Number(formData.fuelAmount) : undefined,
        maintenanceType: formData.maintenanceType.trim() || undefined,
        urgency: formData.urgency as any,
        status: 'pending' as const,
      };
      
      await createMachineryRequest(requestData);
      
      Alert.alert("Success", "Request submitted to owner successfully", [
        { 
          text: "OK", 
          onPress: () => {
            // Reset form
            setFormData({
              requestType: "use",
              reason: "",
              startDate: "",
              endDate: "",
              estimatedHours: "",
              fuelAmount: "",
              maintenanceType: "",
              urgency: "medium",
              machinerySpecs: "",
              budget: "",
            });
            router.back();
          }
        }
      ]);
    } catch (error) {
      console.error("Error creating request:", error);
      Alert.alert("Error", "Failed to submit request. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  
  // Auto-fill today's date
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    setFormData(prev => ({ ...prev, startDate: today }));
  }, []);
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: "Request from Owner",
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#2E7D32",
          },
          headerTintColor: "#fff",
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={[styles.formContainer, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Request Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor }]}>Request Type *</Text>
            <View style={styles.pickerContainer}>
              {requestTypes.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.pickerOption,
                    {
                      backgroundColor: formData.requestType === type.value ? "#2E7D32" : inputBackgroundColor,
                      borderColor: formData.requestType === type.value ? "#2E7D32" : borderColor,
                    }
                  ]}
                  onPress={() => handleInputChange("requestType", type.value)}
                >
                  <Text style={[
                    styles.pickerOptionText,
                    { color: formData.requestType === type.value ? "#fff" : textColor }
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor }]}>Reason *</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
              value={formData.reason}
              onChangeText={(value) => handleInputChange("reason", value)}
              placeholder="Please explain why you need this from the owner..."
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={4}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor }]}>Urgency Level *</Text>
            <View style={styles.pickerContainer}>
              {urgencyLevels.map((level) => (
                <TouchableOpacity
                  key={level.value}
                  style={[
                    styles.pickerOption,
                    {
                      backgroundColor: formData.urgency === level.value ? "#2E7D32" : inputBackgroundColor,
                      borderColor: formData.urgency === level.value ? "#2E7D32" : borderColor,
                    }
                  ]}
                  onPress={() => handleInputChange("urgency", level.value)}
                >
                  <Text style={[
                    styles.pickerOptionText,
                    { color: formData.urgency === level.value ? "#fff" : textColor }
                  ]}>
                    {level.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
        
        <View style={[styles.formContainer, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>Schedule & Specifics</Text>
          
          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
              <Text style={[styles.label, { color: textColor }]}>Start Date *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                value={formData.startDate}
                onChangeText={(value) => handleInputChange("startDate", value)}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={secondaryTextColor}
              />
            </View>
            
            <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
              <Text style={[styles.label, { color: textColor }]}>End Date</Text>
              <TextInput
                style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                value={formData.endDate}
                onChangeText={(value) => handleInputChange("endDate", value)}
                placeholder="YYYY-MM-DD"
                placeholderTextColor={secondaryTextColor}
              />
            </View>
          </View>
          
          {formData.requestType === 'use' && (
            <>
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: textColor }]}>Machinery Specifications *</Text>
                <TextInput
                  style={[styles.textArea, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                  value={formData.machinerySpecs}
                  onChangeText={(value) => handleInputChange("machinerySpecs", value)}
                  placeholder="Describe the type of machinery needed, specifications, features required..."
                  placeholderTextColor={secondaryTextColor}
                  multiline
                  numberOfLines={3}
                />
              </View>
              
              <View style={styles.row}>
                <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={[styles.label, { color: textColor }]}>Estimated Hours</Text>
                  <TextInput
                    style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                    value={formData.estimatedHours}
                    onChangeText={(value) => handleInputChange("estimatedHours", value)}
                    placeholder="e.g., 40"
                    placeholderTextColor={secondaryTextColor}
                    keyboardType="numeric"
                  />
                </View>
                
                <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
                  <Text style={[styles.label, { color: textColor }]}>Budget Range</Text>
                  <TextInput
                    style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                    value={formData.budget}
                    onChangeText={(value) => handleInputChange("budget", value)}
                    placeholder="e.g., $5000-10000"
                    placeholderTextColor={secondaryTextColor}
                  />
                </View>
              </View>
            </>
          )}
          
          {formData.requestType === 'fuel' && (
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>Fuel Amount (L) *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                value={formData.fuelAmount}
                onChangeText={(value) => handleInputChange("fuelAmount", value)}
                placeholder="e.g., 500"
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
            </View>
          )}
          
          {formData.requestType === 'maintenance' && (
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>Maintenance Type *</Text>
              <TextInput
                style={[styles.input, { backgroundColor: inputBackgroundColor, color: textColor, borderColor }]}
                value={formData.maintenanceType}
                onChangeText={(value) => handleInputChange("maintenanceType", value)}
                placeholder="e.g., Specialized equipment repair, Expert consultation"
                placeholderTextColor={secondaryTextColor}
              />
            </View>
          )}
        </View>
        
        <TouchableOpacity
          style={[styles.submitButton, { opacity: loading ? 0.7 : 1 }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? "Submitting Request..." : "Submit Request to Owner"}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  formContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  row: {
    flexDirection: "row",
  },
  pickerContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  pickerOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  pickerOptionText: {
    fontSize: 14,
    fontWeight: "500",
  },
  submitButton: {
    backgroundColor: "#2E7D32",
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    marginTop: 8,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
});