import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AlertTriangle, CheckCircle, AlertCircle, Wrench } from 'lucide-react-native';
import { useTheme } from '@/context/theme-context';
import { Machinery } from '@/services/machinery-service';
import { checkMachineryHealth } from '@/services/machinery-triggers';

interface MachineryHealthIndicatorProps {
  machinery: Machinery;
  onPress?: () => void;
  compact?: boolean;
}

export default function MachineryHealthIndicator({ 
  machinery, 
  onPress, 
  compact = false 
}: MachineryHealthIndicatorProps) {
  const { colors } = useTheme();
  const [healthStatus, setHealthStatus] = useState<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } | null>(null);

  useEffect(() => {
    checkHealth();
  }, [machinery]);

  const checkHealth = async () => {
    try {
      const health = await checkMachineryHealth(machinery);
      setHealthStatus(health);
    } catch (error) {
      console.error('Error checking machinery health:', error);
    }
  };

  if (!healthStatus) return null;

  const getHealthIcon = () => {
    const iconProps = { size: compact ? 16 : 20 };
    
    switch (healthStatus.status) {
      case 'healthy':
        return <CheckCircle {...iconProps} color="#4CAF50" />;
      case 'warning':
        return <AlertCircle {...iconProps} color="#FF9800" />;
      case 'critical':
        return <AlertTriangle {...iconProps} color="#F44336" />;
      default:
        return <Wrench {...iconProps} color={colors.textSecondary} />;
    }
  };

  const getHealthColor = () => {
    switch (healthStatus.status) {
      case 'healthy':
        return '#4CAF50';
      case 'warning':
        return '#FF9800';
      case 'critical':
        return '#F44336';
      default:
        return colors.textSecondary;
    }
  };

  const getHealthText = () => {
    switch (healthStatus.status) {
      case 'healthy':
        return 'Healthy';
      case 'warning':
        return 'Needs Attention';
      case 'critical':
        return 'Critical';
      default:
        return 'Unknown';
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[
          styles.compactContainer,
          { backgroundColor: getHealthColor() + '20' }
        ]}
        onPress={onPress}
        disabled={!onPress}
      >
        {getHealthIcon()}
        <Text style={[
          styles.compactText,
          { color: getHealthColor() }
        ]}>
          {getHealthText()}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { 
          backgroundColor: colors.surface,
          borderColor: getHealthColor(),
          borderLeftColor: getHealthColor(),
        }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.header}>
        {getHealthIcon()}
        <Text style={[styles.title, { color: colors.text }]}>
          Health Status
        </Text>
        <Text style={[
          styles.status,
          { color: getHealthColor() }
        ]}>
          {getHealthText()}
        </Text>
      </View>

      {healthStatus.issues.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Issues:
          </Text>
          {healthStatus.issues.map((issue, index) => (
            <Text key={index} style={[styles.issueText, { color: colors.textSecondary }]}>
              • {issue}
            </Text>
          ))}
        </View>
      )}

      {healthStatus.recommendations.length > 0 && (
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Recommendations:
          </Text>
          {healthStatus.recommendations.map((recommendation, index) => (
            <Text key={index} style={[styles.recommendationText, { color: colors.textSecondary }]}>
              • {recommendation}
            </Text>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    borderLeftWidth: 4,
    padding: 16,
    marginVertical: 8,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  status: {
    fontSize: 14,
    fontWeight: '600',
  },
  compactText: {
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  issueText: {
    fontSize: 13,
    lineHeight: 18,
    marginLeft: 8,
  },
  recommendationText: {
    fontSize: 13,
    lineHeight: 18,
    marginLeft: 8,
  },
});