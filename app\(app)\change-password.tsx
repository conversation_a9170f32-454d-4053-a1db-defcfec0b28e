import React, { useState } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { Lock, Eye, EyeOff, X, Shield, AlertCircle, CheckCircle2 } from "lucide-react-native";

export default function ChangePasswordScreen() {
  const { changePassword, user } = useAuth();
  const { theme } = useTheme();
  const { isRTL, t } = useLanguage();
  const isDarkMode = theme === "dark";
  
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Role-based theme colors
  const getRoleColors = () => {
    switch (user?.role) {
      case 'owner':
        return {
          primary: '#2E7D32',
          secondary: '#4CAF50',
          light: isDarkMode ? '#2E7D3220' : '#E8F5E8',
        };
      case 'admin':
        return {
          primary: '#1976D2',
          secondary: '#2196F3',
          light: isDarkMode ? '#1976D220' : '#E3F2FD',
        };
      case 'caretaker':
        return {
          primary: '#FF9800',
          secondary: '#FFC107',
          light: isDarkMode ? '#FF980020' : '#FFF3E0',
        };
      default:
        return {
          primary: '#2E7D32',
          secondary: '#4CAF50',
          light: isDarkMode ? '#2E7D3220' : '#E8F5E8',
        };
    }
  };

  const roleColors = getRoleColors();
  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#2d2d2d" : "#e2e8f0";
  const primaryColor = roleColors.primary;
  const primaryLight = roleColors.light;
  const errorColor = "#ef4444";
  const successColor = "#10b981";
  const warningColor = "#f59e0b";

  // Password validation
  const validatePassword = (password: string) => {
    const minLength = password.length >= 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
      isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
    };
  };

  const passwordValidation = validatePassword(newPassword);
  
  // Calculate password strength
  const getPasswordStrength = () => {
    const checks = [
      passwordValidation.minLength,
      passwordValidation.hasUpperCase,
      passwordValidation.hasLowerCase,
      passwordValidation.hasNumbers,
      passwordValidation.hasSpecialChar
    ];
    const score = checks.filter(Boolean).length;
    
    if (score <= 2) return { level: 'weak', color: errorColor, text: t("changePassword.weak"), progress: 0.2 };
    if (score <= 3) return { level: 'medium', color: warningColor, text: t("changePassword.medium"), progress: 0.5 };
    if (score <= 4) return { level: 'good', color: '#3b82f6', text: t("changePassword.good"), progress: 0.75 };
    return { level: 'strong', color: successColor, text: t("changePassword.strong"), progress: 1.0 };
  };

  const passwordStrength = getPasswordStrength();

  const handleChangePassword = async () => {
    // Reset errors
    setErrors({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });

    // Validate inputs
    let hasErrors = false;
    const newErrors = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };

    if (!currentPassword.trim()) {
      newErrors.currentPassword = t("changePassword.currentPasswordRequired");
      hasErrors = true;
    }

    if (!newPassword.trim()) {
      newErrors.newPassword = t("changePassword.newPasswordRequired");
      hasErrors = true;
    } else if (!passwordValidation.isValid) {
      newErrors.newPassword = t("changePassword.passwordRequirements");
      hasErrors = true;
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = t("changePassword.confirmPasswordRequired");
      hasErrors = true;
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = t("changePassword.passwordsDoNotMatch");
      hasErrors = true;
    }

    if (currentPassword === newPassword) {
      newErrors.newPassword = t("changePassword.newPasswordSameAsCurrent");
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    try {
      await changePassword(currentPassword, newPassword);
      
      Alert.alert(
        t("changePassword.success"),
        t("changePassword.passwordChangedSuccessfully"),
        [
          {
            text: t("common.ok"),
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      Alert.alert(
        t("changePassword.error"),
        error.message || t("changePassword.failedToChangePassword")
      );
    } finally {
      setLoading(false);
    }
  };

  const PasswordRequirement = ({ met, text }: { met: boolean; text: string }) => (
    <View style={[styles.requirementRow, isRTL && styles.requirementRowRTL]}>
      <View style={[styles.requirementIcon, { backgroundColor: met ? `${successColor}15` : `${errorColor}15` }]}>
        {met ? (
          <CheckCircle2 size={14} color={successColor} />
        ) : (
          <X size={14} color={errorColor} />
        )}
      </View>
      <Text style={[
        styles.requirementText,
        { color: met ? successColor : errorColor, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }
      ]}>
        {text}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("changePassword.title"),
          headerStyle: {
            backgroundColor: primaryColor,
          },
          headerTintColor: "#fff",
          headerTitleStyle: {
            color: "#fff",
            fontWeight: "600",
          },
          headerShadowVisible: false,
        }}
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, isRTL && styles.headerRTL]}>
          <View style={[styles.headerIcon, { backgroundColor: primaryLight }]}>
            <Shield size={32} color={primaryColor} />
          </View>
          <Text style={[styles.title, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("changePassword.title")}
          </Text>
          <Text style={[styles.subtitle, { color: secondaryTextColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("changePassword.subtitle")}
          </Text>
        </View>

        <View style={[styles.formCard, { backgroundColor: cardColor }]}>
          {/* Current Password */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("changePassword.currentPassword")}
            </Text>
            <View style={[
              styles.inputContainer, 
              { 
                borderColor: errors.currentPassword ? errorColor : (currentPassword ? primaryColor : borderColor),
                backgroundColor: isDarkMode ? "#262626" : "#f8fafc"
              }
            ]}>
              <View style={[styles.inputIconContainer, { backgroundColor: primaryLight }]}>
                <Lock size={18} color={primaryColor} />
              </View>
              <TextInput
                style={[styles.textInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
                value={currentPassword}
                onChangeText={setCurrentPassword}
                placeholder={t("changePassword.enterCurrentPassword")}
                placeholderTextColor={secondaryTextColor}
                secureTextEntry={!showCurrentPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? (
                  <EyeOff size={18} color={secondaryTextColor} />
                ) : (
                  <Eye size={18} color={secondaryTextColor} />
                )}
              </TouchableOpacity>
            </View>
            {errors.currentPassword ? (
              <View style={[styles.errorContainer, isRTL && styles.errorContainerRTL]}>
                <AlertCircle size={16} color={errorColor} />
                <Text style={[styles.errorText, { textAlign: isRTL ? 'right' : 'left', marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 }]}>
                  {errors.currentPassword}
                </Text>
              </View>
            ) : null}
          </View>

          {/* New Password */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("changePassword.newPassword")}
            </Text>
            <View style={[
              styles.inputContainer, 
              { 
                borderColor: errors.newPassword ? errorColor : (newPassword ? passwordStrength.color : borderColor),
                backgroundColor: isDarkMode ? "#262626" : "#f8fafc"
              }
            ]}>
              <View style={[styles.inputIconContainer, { backgroundColor: primaryLight }]}>
                <Lock size={18} color={primaryColor} />
              </View>
              <TextInput
                style={[styles.textInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
                value={newPassword}
                onChangeText={setNewPassword}
                placeholder={t("changePassword.enterNewPassword")}
                placeholderTextColor={secondaryTextColor}
                secureTextEntry={!showNewPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? (
                  <EyeOff size={18} color={secondaryTextColor} />
                ) : (
                  <Eye size={18} color={secondaryTextColor} />
                )}
              </TouchableOpacity>
            </View>
            
            {/* Password Strength Indicator */}
            {newPassword.length > 0 && (
              <View style={styles.strengthContainer}>
                <View style={[styles.strengthHeader, isRTL && styles.strengthHeaderRTL]}>
                  <Text style={[styles.strengthLabel, { color: secondaryTextColor }]}>
                    {t("changePassword.passwordStrength")}:
                  </Text>
                  <Text style={[styles.strengthValue, { color: passwordStrength.color }]}>
                    {passwordStrength.text}
                  </Text>
                </View>
                <View style={[styles.strengthBar, { backgroundColor: isDarkMode ? "#374151" : "#e5e7eb" }]}>
                  <View 
                    style={[
                      styles.strengthProgress, 
                      { 
                        backgroundColor: passwordStrength.color,
                        width: `${passwordStrength.progress * 100}%`
                      }
                    ]} 
                  />
                </View>
              </View>
            )}
            
            {errors.newPassword ? (
              <View style={[styles.errorContainer, isRTL && styles.errorContainerRTL]}>
                <AlertCircle size={16} color={errorColor} />
                <Text style={[styles.errorText, { textAlign: isRTL ? 'right' : 'left', marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 }]}>
                  {errors.newPassword}
                </Text>
              </View>
            ) : null}
          </View>



          {/* Confirm Password */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("changePassword.confirmPassword")}
            </Text>
            <View style={[
              styles.inputContainer, 
              { 
                borderColor: errors.confirmPassword ? errorColor : (confirmPassword && newPassword === confirmPassword ? successColor : borderColor),
                backgroundColor: isDarkMode ? "#262626" : "#f8fafc"
              }
            ]}>
              <View style={[styles.inputIconContainer, { backgroundColor: primaryLight }]}>
                <Lock size={18} color={primaryColor} />
              </View>
              <TextInput
                style={[styles.textInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder={t("changePassword.confirmNewPassword")}
                placeholderTextColor={secondaryTextColor}
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff size={18} color={secondaryTextColor} />
                ) : (
                  <Eye size={18} color={secondaryTextColor} />
                )}
              </TouchableOpacity>
            </View>
            {confirmPassword && newPassword === confirmPassword && (
              <View style={[styles.successMessage, isRTL && styles.successMessageRTL]}>
                <CheckCircle2 size={16} color={successColor} />
                <Text style={[styles.successText, { color: successColor, marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 }]}>
                  {t("changePassword.passwordsMatch")}
                </Text>
              </View>
            )}
            {errors.confirmPassword ? (
              <View style={[styles.errorContainer, isRTL && styles.errorContainerRTL]}>
                <AlertCircle size={16} color={errorColor} />
                <Text style={[styles.errorText, { textAlign: isRTL ? 'right' : 'left', marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 }]}>
                  {errors.confirmPassword}
                </Text>
              </View>
            ) : null}
          </View>

          {/* Password Requirements */}
          {newPassword.length > 0 && (
            <View style={[styles.requirementsContainer, { backgroundColor: isDarkMode ? "#262626" : "#f8fafc" }]}>
              <Text style={[styles.requirementsTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
                {t("changePassword.passwordRequirements")}:
              </Text>
              <PasswordRequirement
                met={passwordValidation.minLength}
                text={t("changePassword.minLength")}
              />
              <PasswordRequirement
                met={passwordValidation.hasUpperCase}
                text={t("changePassword.hasUpperCase")}
              />
              <PasswordRequirement
                met={passwordValidation.hasLowerCase}
                text={t("changePassword.hasLowerCase")}
              />
              <PasswordRequirement
                met={passwordValidation.hasNumbers}
                text={t("changePassword.hasNumbers")}
              />
              <PasswordRequirement
                met={passwordValidation.hasSpecialChar}
                text={t("changePassword.hasSpecialChar")}
              />
            </View>
          )}

          {/* Change Password Button */}
          <TouchableOpacity
            style={[
              styles.changePasswordButton,
              { 
                backgroundColor: passwordValidation.isValid && confirmPassword === newPassword ? primaryColor : `${primaryColor}40`,
                opacity: loading ? 0.8 : 1
              }
            ]}
            onPress={handleChangePassword}
            disabled={loading || !passwordValidation.isValid || confirmPassword !== newPassword}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <>
                <Shield size={20} color="#fff" />
                <Text style={[styles.changePasswordButtonText, { marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                  {t("changePassword.changePassword")}
                </Text>
              </>
            )}
          </TouchableOpacity>

          {/* Security Tips */}
          <View style={[styles.securityTips, { backgroundColor: primaryLight, borderColor: `${primaryColor}15` }]}>
            <View style={[styles.securityTipsHeader, isRTL && styles.securityTipsHeaderRTL]}>
              <Shield size={16} color={primaryColor} />
              <Text style={[styles.securityTipsTitle, { color: primaryColor, marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0 }]}>
                {t("changePassword.securityTips")}
              </Text>
            </View>
            <Text style={[styles.securityTipsText, { color: secondaryTextColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("changePassword.securityTipsDescription")}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },

  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
    alignItems: "center",
  },
  headerRTL: {
    alignItems: 'center',
  },
  headerIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    letterSpacing: -0.5,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    maxWidth: 300,
  },
  formCard: {
    marginHorizontal: 24,
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 2,
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 60,
  },
  inputIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: "500",
  },
  eyeButton: {
    padding: 8,
    marginLeft: 8,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  errorContainerRTL: {
    flexDirection: "row-reverse",
  },
  errorText: {
    color: "#ef4444",
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  strengthContainer: {
    marginTop: 12,
  },
  strengthHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  strengthHeaderRTL: {
    flexDirection: "row-reverse",
  },
  strengthLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  strengthValue: {
    fontSize: 14,
    fontWeight: "600",
  },
  strengthBar: {
    height: 6,
    borderRadius: 3,
    overflow: "hidden",
  },
  strengthProgress: {
    height: "100%",
    borderRadius: 3,
  },
  successMessage: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  successMessageRTL: {
    flexDirection: "row-reverse",
  },
  successText: {
    fontSize: 14,
    fontWeight: "500",
  },
  requirementsContainer: {
    marginTop: 16,
    padding: 20,
    borderRadius: 16,
  },
  requirementsTitle: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 16,
  },
  requirementRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  requirementRowRTL: {
    flexDirection: "row-reverse",
  },
  requirementIcon: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: "center",
    alignItems: "center",
  },
  requirementText: {
    fontSize: 14,
    fontWeight: "500",
  },
  changePasswordButton: {
    borderRadius: 16,
    paddingVertical: 18,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
    flexDirection: "row",
    //shadowColor: "#3b82f6",
    //shadowOffset: { width: 0, height: 4 },
    // shadowOpacity: 0.1,
    // shadowRadius: 1,
    // elevation: 1,
  },
  changePasswordButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  securityTips: {
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  securityTipsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  securityTipsHeaderRTL: {
    flexDirection: "row-reverse",
  },
  securityTipsTitle: {
    fontSize: 14,
    fontWeight: "600",
  },
  securityTipsText: {
    fontSize: 13,
    lineHeight: 18,
  },
});