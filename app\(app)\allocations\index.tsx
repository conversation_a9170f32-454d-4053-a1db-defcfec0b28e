import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from "react-native";
import { useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import {
  getAllocationsByUser,
  getAllocationSummary,
  updateOverdueAllocations,
  Allocation,
  AllocationSummary
} from "@/services/allocation-service";
import { getUsersByFarm } from "@/services/user-service";
import {
  Package,
  Truck,
  Users,
  Calendar,
  Filter,
  Search,
  ArrowLeft,
  Clock,
  CheckCircle,
  AlertTriangle,
  ArrowUpDown,
  User,
  Crown
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

interface UserWithAllocations {
  id: string;
  displayName: string;
  email: string;
  role: string;
  allocations: Allocation[];
}

export default function AllocationsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors, theme } = useTheme();
  const { t } = useLanguage();

  const [usersWithAllocations, setUsersWithAllocations] = useState<UserWithAllocations[]>([]);
  const [summary, setSummary] = useState<AllocationSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);


  useEffect(() => {
    if (selectedFarm && user) {
      loadUsersWithAllocations();
      loadSummary();
      updateOverdueItems();
    }
  }, [selectedFarm, user]);

  const loadUsersWithAllocations = async () => {
    if (!selectedFarm || !user) return;

    try {
      setLoading(true);

      if (user.role === 'caretaker') {
        // Caretakers only see their own allocations
        const allocations = await getAllocationsByUser(selectedFarm.id, user.uid);
        setUsersWithAllocations([{
          id: user.uid,
          displayName: user.displayName || user.email || 'Unknown User',
          email: user.email || '',
          role: user.role || 'caretaker',
          allocations: allocations,
        }]);
      } else {
        // Owners and admins see all users with allocations
        const farmUsers = await getUsersByFarm(selectedFarm.id);
        const usersData: UserWithAllocations[] = [];

        // Include caretakers, admins, and owners
        const relevantUsers = farmUsers.filter(u => u.role === 'caretaker' || u.role === 'admin' || u.role === 'owner');

        for (const farmUser of relevantUsers) {
          const allocations = await getAllocationsByUser(selectedFarm.id, farmUser.uid);
          usersData.push({
            id: farmUser.uid,
            displayName: farmUser.displayName || farmUser.email || 'Unknown User',
            email: farmUser.email || '',
            role: farmUser.role || 'unknown',
            allocations: allocations,
          });
        }

        setUsersWithAllocations(usersData);
      }
    } catch (error) {
      console.error('Error loading users with allocations:', error);
      Alert.alert(t('common.error'), t('allocations.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const loadSummary = async () => {
    if (!selectedFarm || !user) return;

    try {
      const summaryData = await getAllocationSummary(selectedFarm.id);
      setSummary(summaryData);
    } catch (error) {
      console.error('Error loading allocation summary:', error);
    }
  };

  const updateOverdueItems = async () => {
    if (!selectedFarm) return;

    try {
      await updateOverdueAllocations(selectedFarm.id);
    } catch (error) {
      console.error('Error updating overdue allocations:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([loadUsersWithAllocations(), loadSummary(), updateOverdueItems()]);
    setRefreshing(false);
  };

  const handleUserPress = (user: UserWithAllocations) => {
    router.push(`/allocations/user/${user.id}?name=${encodeURIComponent(user.displayName)}&role=${user.role}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'allocated': return <Clock size={16} color={colors.primary} />;
      case 'partially_returned': return <ArrowUpDown size={16} color={colors.warning} />;
      case 'returned': return <CheckCircle size={16} color={colors.success} />;
      case 'overdue': return <AlertTriangle size={16} color={colors.error} />;
      case 'damaged': return <AlertTriangle size={16} color={colors.warning} />;
      case 'lost': return <AlertTriangle size={16} color={colors.error} />;
      default: return <Clock size={16} color={colors.textSecondary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'allocated': return colors.primary;
      case 'partially_returned': return colors.warning;
      case 'returned': return colors.success;
      case 'overdue': return colors.error;
      case 'damaged': return colors.warning;
      case 'lost': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getUserRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return colors.primary;
      case 'caretaker': return colors.secondary;
      case 'owner': return colors.success;
      default: return colors.textSecondary;
    }
  };

  const getUserRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Users size={20} color={getUserRoleColor(role)} />;
      case 'owner':
        return <Crown size={20} color={getUserRoleColor(role)} />;
      default:
        return <User size={20} color={getUserRoleColor(role)} />;
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'inventory' ? 
      <Package size={20} color={colors.primary} /> : 
      <Truck size={20} color={colors.secondary} />;
  };



  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('allocations.title'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.centerContainer}>
          <Text style={[styles.message, { color: colors.text }]}>
            {t('farm.selectFarmFirst')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: user?.role === 'caretaker' ? t('allocations.myAllocations') : t('allocations.title'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >


        {/* Summary Cards */}
        {summary && (
          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <LinearGradient
                colors={[colors.primary, colors.primary + '80']}
                style={[styles.summaryCard, styles.totalCard]}
              >
                <Users size={24} color="white" />
                <Text style={styles.summaryAmount}>
                  {summary.totalAllocations}
                </Text>
                <Text style={styles.summaryLabel}>{t('allocations.totalAllocations')}</Text>
              </LinearGradient>

              <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Package size={20} color={colors.primary} />
                <Text style={[styles.summaryAmount, { color: colors.text, fontSize: 18 }]}>
                  {usersWithAllocations.reduce((total, user) => total + user.allocations.filter(a => a.type === 'inventory').length, 0)}
                </Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  {t('allocations.inventoryAllocations')}
                </Text>
              </View>
            </View>

            <View style={styles.summaryRow}>
              <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <Truck size={20} color={colors.secondary} />
                <Text style={[styles.summaryAmount, { color: colors.text, fontSize: 18 }]}>
                  {usersWithAllocations.reduce((total, user) => total + user.allocations.filter(a => a.type === 'machinery').length, 0)}
                </Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  {t('allocations.machineryAllocations')}
                </Text>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <CheckCircle size={20} color={colors.success} />
                <Text style={[styles.summaryAmount, { color: colors.text, fontSize: 18 }]}>
                  {usersWithAllocations.reduce((total, user) => {
                    return total + user.allocations.filter(a => {
                      // Count allocations that are returned or partially returned
                      return a.status === 'returned' || a.status === 'partially_returned';
                    }).length;
                  }, 0)}
                </Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  {t('allocations.returnedAllocations')}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Users with Allocations */}
        <View style={[styles.card, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {user?.role === 'caretaker' ? t('allocations.myAllocations') : t('allocations.userAllocations')}
            </Text>
            <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
              {usersWithAllocations.length} {t('common.users')}
            </Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : usersWithAllocations.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Users size={48} color={colors.textSecondary} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                {t('allocations.noAllocations')}
              </Text>
            </View>
          ) : (
            <View style={styles.usersList}>
              {usersWithAllocations.map((userWithAllocations) => (
                <TouchableOpacity
                  key={userWithAllocations.id}
                  style={[styles.userCard, { borderColor: colors.border }]}
                  onPress={() => handleUserPress(userWithAllocations)}
                >
                  <View style={styles.userInfo}>
                    <View style={[styles.userAvatar, { backgroundColor: getUserRoleColor(userWithAllocations.role) + '20' }]}>
                      {getUserRoleIcon(userWithAllocations.role)}
                    </View>
                    <View style={styles.userDetails}>
                      <Text style={[styles.userName, { color: colors.text }]}>
                        {userWithAllocations.displayName}
                      </Text>
                      <Text style={[styles.userRole, { color: colors.textSecondary }]}>
                        {t(`common.${userWithAllocations.role}`)} • {userWithAllocations.allocations.length} {t('allocations.allocations')}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.userStats}>
                    <Text style={[styles.statsText, { color: colors.text }]}>
                      {userWithAllocations.allocations.length}
                    </Text>
                    <Text style={[styles.statsLabel, { color: colors.textSecondary }]}>
                      {t('allocations.allocations')}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  summaryContainer: {
    padding: 16,
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    gap: 8,
  },
  totalCard: {
    flex: 2,
  },
  summaryAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  summaryLabel: {
    fontSize: 12,
    color: 'white',
    opacity: 0.9,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 8,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  card: {
    margin: 16,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 14,
  },

  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  usersList: {
    padding: 16,
  },
  userCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  userRole: {
    fontSize: 12,
  },
  userStats: {
    alignItems: 'center',
  },
  statsText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsLabel: {
    fontSize: 10,
  },


});
