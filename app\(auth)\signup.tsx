import { useState } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Image, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView, Alert } from "react-native";
import { useRouter } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { uploadUserProfileImage } from "@/services/storage-service";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { Eye, EyeOff, ArrowLeft, CheckCircle, Camera, User } from "lucide-react-native";
import * as ImagePicker from 'expo-image-picker';

const getErrorMessage = (error: string) => {
  if (error.includes("auth/email-already-in-use")) {
    return "An account with this email already exists. Please use a different email or try signing in.";
  }
  if (error.includes("auth/invalid-email")) {
    return "Please enter a valid email address.";
  }
  if (error.includes("auth/weak-password")) {
    return "Password is too weak. Please use at least 6 characters with a mix of letters and numbers.";
  }
  if (error.includes("auth/operation-not-allowed")) {
    return "Account creation is currently disabled. Please contact support.";
  }
  if (error.includes("auth/network-request-failed")) {
    return "Network error. Please check your connection and try again.";
  }
  if (error.includes("auth/too-many-requests")) {
    return "Too many attempts. Please try again later.";
  }
  return "Failed to create account. Please try again.";
};

export default function SignupScreen() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [phone, setPhone] = useState("");
  const [bio, setBio] = useState("");
  const [cnic, setCnic] = useState("");
  const [address, setAddress] = useState("");
  const [photoURL, setPhotoURL] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { signUp } = useAuth();
  const router = useRouter();

  const handleImagePicker = async () => {
    try {
      Alert.alert(
        "Select Profile Photo",
        "Choose how you would like to add your profile photo",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Camera", onPress: openCamera },
          { text: "Gallery", onPress: openGallery },
        ]
      );
    } catch (error) {
      console.error("Error showing image picker options:", error);
      Alert.alert("Error", "Failed to show image picker options");
    }
  };

  const openCamera = async () => {
    try {
      // Request camera permissions
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      
      if (cameraPermission.status !== 'granted') {
        Alert.alert(
          'Permission Required', 
          'Camera permission is required to take photos. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              if (Platform.OS === 'ios') {
                // On iOS, we can't directly open settings, but we can show instructions
                Alert.alert('Enable Camera Permission', 'Go to Settings > Privacy & Security > Camera and enable access for this app.');
              }
            }}
          ]
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error opening camera:", error);
      Alert.alert("Error", "Failed to open camera. Please try again.");
    }
  };

  const openGallery = async () => {
    try {
      // Request media library permissions
      const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (mediaPermission.status !== 'granted') {
        Alert.alert(
          'Permission Required', 
          'Gallery permission is required to select photos. Please enable it in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              if (Platform.OS === 'ios') {
                // On iOS, we can't directly open settings, but we can show instructions
                Alert.alert('Enable Gallery Permission', 'Go to Settings > Privacy & Security > Photos and enable access for this app.');
              }
            }}
          ]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error opening gallery:", error);
      Alert.alert("Error", "Failed to open gallery. Please try again.");
    }
  };

  const handleSignup = async () => {
    if (!name || !email || !password || !confirmPassword) {
      setError("Please fill in all required fields");
      return;
    }

    if (name.length < 2) {
      setError("Name must be at least 2 characters long");
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (phone && phone.length < 10) {
      setError("Please enter a valid phone number");
      return;
    }

    if (cnic && cnic.length < 13) {
      setError("Please enter a valid CNIC (13 digits)");
      return;
    }

    setLoading(true);
    setError("");

    try {
      let uploadedPhotoURL = photoURL;

      // Upload image to Firebase Storage if a local image is selected
      if (photoURL && photoURL.startsWith('file://')) {
        console.log("Uploading signup profile image to Storage...");
        const tempUserId = `signup_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        uploadedPhotoURL = await uploadUserProfileImage(photoURL, tempUserId);
        console.log("Signup profile image uploaded:", uploadedPhotoURL);
      }

      const additionalData = {
        phone,
        phoneNumber: phone,
        bio,
        photoURL: uploadedPhotoURL,
        cnic,
        address,
      };

      await signUp(email, password, name, "owner", additionalData);
      // Navigation to farms/create is handled by the auth context
    } catch (err: any) {
      setError(getErrorMessage(err.message || ""));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#6B7280" />
            </TouchableOpacity>
            
            <View style={styles.logoContainer}>
              <View style={styles.logoWrapper}>
                <Image
                  source={{ uri: "https://images.unsplash.com/photo-*************-5cee4dff5d13?q=80&w=200&auto=format&fit=crop" }}
                  style={styles.logo}
                />
                <View style={styles.logoOverlay}>
                  <CheckCircle size={32} color="#059669" />
                </View>
              </View>
            </View>
            <Text style={styles.title}>Create Your Farm Account</Text>
            <Text style={styles.subtitle}>Join thousands of farmers managing their operations digitally</Text>
          </View>

          <View style={styles.formContainer}>
            {/* Profile Photo */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Profile Photo</Text>
              <TouchableOpacity style={styles.photoUpload} onPress={handleImagePicker}>
                {photoURL ? (
                  <Image source={{ uri: photoURL }} style={styles.profilePhoto} />
                ) : (
                  <View style={styles.photoPlaceholder}>
                    <User size={32} color="#9CA3AF" />
                    <Text style={styles.photoText}>Add Photo</Text>
                  </View>
                )}
                <View style={styles.cameraIcon}>
                  <Camera size={16} color="#fff" />
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Full Name *</Text>
              <TextInput
                style={[styles.input, name.length >= 2 && styles.inputValid]}
                placeholder="Enter your full name"
                placeholderTextColor="#9CA3AF"
                value={name}
                onChangeText={setName}
                autoComplete="name"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email Address *</Text>
              <TextInput
                style={[styles.input, email.includes('@') && styles.inputValid]}
                placeholder="Enter your email"
                placeholderTextColor="#9CA3AF"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone Number</Text>
              <TextInput
                style={[styles.input, phone.length >= 10 && styles.inputValid]}
                placeholder="Enter your phone number"
                placeholderTextColor="#9CA3AF"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                autoComplete="tel"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>CNIC</Text>
              <TextInput
                style={[styles.input, cnic.length >= 13 && styles.inputValid]}
                placeholder="Enter your CNIC (13 digits)"
                placeholderTextColor="#9CA3AF"
                value={cnic}
                onChangeText={setCnic}
                keyboardType="numeric"
                maxLength={15}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Address</Text>
              <TextInput
                style={[styles.textArea, address.length > 0 && styles.inputValid]}
                placeholder="Enter your complete address"
                placeholderTextColor="#9CA3AF"
                value={address}
                onChangeText={setAddress}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Bio</Text>
              <TextInput
                style={[styles.textArea, bio.length > 0 && styles.inputValid]}
                placeholder="Tell us about yourself and your farming experience"
                placeholderTextColor="#9CA3AF"
                value={bio}
                onChangeText={setBio}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Password *</Text>
              <View style={[styles.passwordContainer, password.length >= 6 && styles.inputValid]}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="Create a strong password"
                  placeholderTextColor="#9CA3AF"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoComplete="new-password"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff size={20} color="#9CA3AF" />
                  ) : (
                    <Eye size={20} color="#9CA3AF" />
                  )}
                </TouchableOpacity>
              </View>
              <Text style={styles.passwordHint}>At least 6 characters</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Confirm Password *</Text>
              <View style={[styles.passwordContainer, password === confirmPassword && confirmPassword.length > 0 && styles.inputValid]}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="Confirm your password"
                  placeholderTextColor="#9CA3AF"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  autoComplete="new-password"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={20} color="#9CA3AF" />
                  ) : (
                    <Eye size={20} color="#9CA3AF" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.roleInfo}>
              <View style={styles.roleHeader}>
                <CheckCircle size={20} color="#059669" />
                <Text style={styles.roleText}>Farm Owner Account</Text>
              </View>
              <Text style={styles.roleDescription}>
                You will have full access to create and manage farms, inventory, and team members. Start your digital farming journey today!
              </Text>
            </View>

            {error ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            <TouchableOpacity
              style={[styles.signupButton, loading && styles.signupButtonDisabled]}
              onPress={handleSignup}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color="#fff" size="small" />
                  <Text style={styles.loadingText}>Creating Account...</Text>
                </View>
              ) : (
                <Text style={styles.signupButtonText}>Create Account</Text>
              )}
            </TouchableOpacity>

            <View style={styles.loginPrompt}>
              <Text style={styles.loginText}>
                Already have an account?{" "}
                <Text
                  style={styles.loginLink}
                  onPress={() => router.push("/(auth)/login")}
                >
                  Sign In
                </Text>
              </Text>
            </View>

            <View style={styles.termsContainer}>
              <Text style={styles.termsText}>
                By creating an account, you agree to our Terms of Service and Privacy Policy
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAFAFA",
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 32,
  },
  backButton: {
    position: "absolute",
    left: 0,
    top: 0,
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#F3F4F6",
  },
  logoContainer: {
    marginBottom: 24,
  },
  logoWrapper: {
    position: "relative",
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 20,
  },
  logoOverlay: {
    position: "absolute",
    bottom: -8,
    right: -8,
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    color: "#1F2937",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  formContainer: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 8,
  },
  photoUpload: {
    alignSelf: "center",
    position: "relative",
    marginBottom: 8,
  },
  profilePhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#F3F4F6",
  },
  photoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#F3F4F6",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#E5E7EB",
    borderStyle: "dashed",
  },
  photoText: {
    fontSize: 12,
    color: "#9CA3AF",
    marginTop: 4,
  },
  cameraIcon: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#059669",
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  input: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 2,
    borderColor: "#E5E7EB",
    color: "#1F2937",
  },
  textArea: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 2,
    borderColor: "#E5E7EB",
    color: "#1F2937",
    minHeight: 80,
  },
  inputValid: {
    borderColor: "#059669",
  },
  passwordContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#E5E7EB",
  },
  passwordInput: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    color: "#1F2937",
  },
  eyeButton: {
    padding: 16,
  },
  passwordHint: {
    fontSize: 12,
    color: "#6B7280",
    marginTop: 4,
  },
  roleInfo: {
    backgroundColor: "#F0FDF4",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: "#059669",
  },
  roleHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  roleText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#065F46",
    marginLeft: 8,
  },
  roleDescription: {
    fontSize: 14,
    color: "#047857",
    lineHeight: 20,
  },
  errorContainer: {
    backgroundColor: "#FEF2F2",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: "#EF4444",
  },
  errorText: {
    color: "#DC2626",
    fontSize: 14,
    lineHeight: 20,
  },
  signupButton: {
    backgroundColor: "#059669",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginBottom: 24,
    shadowColor: "#059669",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  signupButtonDisabled: {
    opacity: 0.7,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  signupButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  loginPrompt: {
    alignItems: "center",
    paddingTop: 16,
    marginBottom: 16,
  },
  loginText: {
    fontSize: 14,
    color: "#6B7280",
  },
  loginLink: {
    color: "#059669",
    fontWeight: "600",
  },
  termsContainer: {
    alignItems: "center",
    paddingHorizontal: 16,
  },
  termsText: {
    fontSize: 12,
    color: "#9CA3AF",
    textAlign: "center",
    lineHeight: 16,
  },
});