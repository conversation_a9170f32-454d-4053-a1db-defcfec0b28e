# Image Upload Debug Guide

## Common Issues and Solutions

### 1. Firebase Configuration Issues
- **Problem**: Using wrong Firebase project configuration
- **Solution**: ✅ Fixed - Updated storage service to use correct Firebase config

### 2. Firebase Storage Rules
- **Problem**: Storage rules might be blocking uploads
- **Check**: Firebase Console > Storage > Rules
- **Temporary Fix**: Set rules to allow all uploads (for testing):
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true; // Temporary - allow all
    }
  }
}
```

### 3. Network/Connectivity Issues
- **Problem**: Network issues preventing upload
- **Debug**: Check console logs for network errors

### 4. Image URI Issues
- **Problem**: Invalid image URI from camera/gallery
- **Debug**: Check if URI starts with 'file://' and is valid

### 5. Blob Conversion Issues
- **Problem**: Failed to convert image URI to blob
- **Debug**: Check console logs for blob conversion errors

## Debug Steps

1. **Check Console Logs**: Look for detailed error messages in the console
2. **Test with Small Image**: Try uploading a very small image first
3. **Check Firebase Console**: Verify if any files are being created in Storage
4. **Test Network**: Ensure device has internet connectivity
5. **Check Permissions**: Ensure app has camera/gallery permissions

## Enhanced Error Logging

The storage service now includes enhanced logging:
- Image URI validation
- Blob size and type logging
- Detailed error messages
- Upload progress tracking

## Testing Steps

1. Open the app
2. Go to Profile section
3. Try to upload an image
4. Check console logs for detailed error information
5. If upload fails, check Firebase Console > Storage to see if any files were created

## Firebase Storage URL Format

Correct Firebase Storage URLs should look like:
- `https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.appspot.com/o/...`
- `https://kissandost-9570f.firebasestorage.app/...`

## Next Steps

If the issue persists:
1. Check Firebase Storage rules
2. Verify Firebase project configuration
3. Test with a minimal image upload example
4. Check device permissions and network connectivity
