import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, deleteDoc, orderBy, limit as firestoreLimit, onSnapshot, Timestamp } from "firebase/firestore";
import { getFirestore } from "firebase/firestore";
import AsyncStorage from '@react-native-async-storage/async-storage';

const db = getFirestore();

export interface Machinery {
  id: string;
  farmId: string;
  name: string;
  type: 'tractor' | 'harvester' | 'planter' | 'sprayer' | 'cultivator' | 'other';
  model: string;
  year: number;
  serialNumber: string;
  status: 'working' | 'maintenance' | 'malfunction' | 'in_use' | 'in_use_other_farm';
  fuelType: 'diesel' | 'gasoline' | 'electric' | 'hybrid';
  fuelCapacity: number;
  currentFuelLevel: number;
  odometerReading: number;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
  maintenanceIntervalHours: number;
  currentLocation?: string;
  assignedToFarm?: string; // When used in other farm
  imageUrl?: string; // Image URL for the machinery
  price?: number; // Purchase price for expense tracking
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface MachineryRequest {
  id: string;
  machineryId: string;
  requesterId: string;
  requesterName: string;
  fromFarmId: string;
  toFarmId: string;
  requestType: 'use' | 'fuel' | 'maintenance';
  reason: string;
  startDate: string;
  endDate: string;
  estimatedHours?: number;
  fuelAmount?: number;
  litterPrice?: number; // Price per liter in Rs
  totalPrice?: number; // Total fuel cost (fuelAmount * litterPrice)
  maintenanceType?: string;
  maintenancePrice?: number; // Price for maintenance in Rs
  urgency: 'low' | 'medium' | 'high' | 'emergency';
  status: 'pending' | 'approved' | 'rejected' | 'in_progress' | 'completed';
  approvedBy?: string;
  approvedAt?: string;
  rejectedReason?: string;
  completedAt?: string;
  returnOdometerReading?: number;
  returnFuelLevel?: number;
  returnNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MachineryReturn {
  id: string;
  requestId: string;
  machineryId: string;
  machineryName: string;
  caretakerId: string;
  caretakerName: string;
  farmId: string;
  farmName?: string;
  type: 'use' | 'maintenance'; // Support both use and maintenance requests
  returnCondition: 'working' | 'minor_issue' | 'needs_repair';
  hoursUsed?: number;
  fuelUsed?: number;
  odometerReading?: number;
  remarks?: string;
  returnedAt: string;
  returnStatus: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedByName?: string;
  reviewedAt?: string;
  rejectionReason?: string;
  adminDescription?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface MaintenanceRecord {
  id: string;
  machineryId: string;
  type: 'scheduled' | 'emergency' | 'repair';
  description: string;
  performedBy: string;
  performedAt: string;
  cost: number;
  partsUsed?: string[];
  odometerReading: number;
  nextMaintenanceDue: string;
  notes?: string;
  createdAt: string;
}

export interface FuelRecord {
  id: string;
  machineryId: string;
  amount: number;
  cost: number;
  fuelType: string;
  odometerReading: number;
  location: string;
  performedBy: string;
  performedAt: string;
  notes?: string;
  createdAt: string;
}

// Type for Firestore document data
interface FirestoreMachineryData {
  farmId?: string;
  name?: string;
  type?: string;
  model?: string;
  year?: number;
  serialNumber?: string;
  status?: string;
  fuelType?: string;
  fuelCapacity?: number;
  currentFuelLevel?: number;
  odometerReading?: number;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  maintenanceIntervalHours?: number;
  currentLocation?: string;
  assignedToFarm?: string;
  imageUrl?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

// Type for Firestore machinery return data
interface FirestoreMachineryReturnData {
  requestId?: string;
  machineryId?: string;
  machineryName?: string;
  caretakerId?: string;
  caretakerName?: string;
  farmId?: string;
  farmName?: string;
  type?: string;
  returnCondition?: string;
  hoursUsed?: number;
  fuelUsed?: number;
  odometerReading?: number;
  remarks?: string;
  returnedAt?: string;
  returnStatus?: string;
  reviewedBy?: string;
  reviewedByName?: string;
  reviewedAt?: string;
  rejectionReason?: string;
  adminDescription?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Get all machinery for a specific farm
export const getMachineryByFarm = async (farmId: string): Promise<Machinery[]> => {
  try {
    if (!farmId) {
      return [];
    }
    
    // Reference to the specific farm's machinery collection
    const machineryRef = collection(db, 'farms', farmId, 'machinery');
    
    // Get all documents in the machinery collection
    const machinerySnapshot = await getDocs(machineryRef);
    
    // Process each machinery document
    const machineryItems: Machinery[] = [];
    machinerySnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreMachineryData;
      machineryItems.push({
        id: docSnapshot.id,
        farmId: farmId,
        name: data.name || '',
        type: (data.type as Machinery['type']) || 'other',
        model: data.model || '',
        year: data.year || 0,
        serialNumber: data.serialNumber || '',
        status: (data.status as Machinery['status']) || 'working',
        fuelType: (data.fuelType as Machinery['fuelType']) || 'diesel',
        fuelCapacity: data.fuelCapacity || 0,
        currentFuelLevel: data.currentFuelLevel || 0,
        odometerReading: data.odometerReading || 0,
        lastMaintenanceDate: data.lastMaintenanceDate || '',
        nextMaintenanceDate: data.nextMaintenanceDate || '',
        maintenanceIntervalHours: data.maintenanceIntervalHours || 0,
        currentLocation: data.currentLocation,
        assignedToFarm: data.assignedToFarm,
        imageUrl: data.imageUrl,
        notes: data.notes,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
        createdBy: data.createdBy || '',
      });
    });
    
    // Cache the data
    await AsyncStorage.setItem(`machinery_${farmId}`, JSON.stringify(machineryItems));
    
    return machineryItems;
  } catch (error) {
    console.error("Error getting machinery items:", error);
    
    // Try to get from cache
    try {
      const cachedMachinery = await AsyncStorage.getItem(`machinery_${farmId}`);
      if (cachedMachinery) {
        return JSON.parse(cachedMachinery);
      }
    } catch (cacheError) {
      console.error("Error reading from cache:", cacheError);
    }
    
    return [];
  }
};

// Subscribe to machinery changes for a farm
export const subscribeToMachinery = (farmId: string, callback: (machinery: Machinery[]) => void): (() => void) => {
  if (!farmId) {
    callback([]);
    return () => {};
  }

  const machineryRef = collection(db, 'farms', farmId, 'machinery');

  const unsubscribe = onSnapshot(
    machineryRef,
    (snapshot) => {
      const machineryItems: Machinery[] = [];
      snapshot.forEach((docSnapshot) => {
        const data = docSnapshot.data() as FirestoreMachineryData;
        machineryItems.push({
          id: docSnapshot.id,
          farmId: farmId,
          name: data.name || '',
          type: (data.type as Machinery['type']) || 'other',
          model: data.model || '',
          year: data.year || new Date().getFullYear(),
          serialNumber: data.serialNumber || '',
          status: (data.status as Machinery['status']) || 'working',
          fuelType: (data.fuelType as Machinery['fuelType']) || 'diesel',
          fuelCapacity: data.fuelCapacity || 0,
          currentFuelLevel: data.currentFuelLevel || 0,
          odometerReading: data.odometerReading || 0,
          lastMaintenanceDate: data.lastMaintenanceDate || new Date().toISOString(),
          nextMaintenanceDate: data.nextMaintenanceDate || new Date().toISOString(),
          maintenanceIntervalHours: data.maintenanceIntervalHours || 100,
          currentLocation: data.currentLocation,
          assignedToFarm: data.assignedToFarm,
          imageUrl: data.imageUrl,
          price: data.price,
          notes: data.notes,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString(),
          createdBy: data.createdBy || '',
        });
      });

      callback(machineryItems);
    },
    (error) => {
      console.error("Error subscribing to machinery:", error);
      callback([]);
    }
  );

  return unsubscribe;
};

// Get available machinery for a specific farm (excludes in-use machinery)
export const getAvailableMachineryByFarm = async (farmId: string): Promise<Machinery[]> => {
  try {
    const allMachinery = await getMachineryByFarm(farmId);
    // Filter out machinery that is currently in use
    return allMachinery.filter(machinery => 
      machinery.status !== 'in_use' && 
      machinery.status !== 'in_use_other_farm' &&
      machinery.status === 'working' // Only show working machinery for requests
    );
  } catch (error) {
    console.error("Error getting available machinery:", error);
    return [];
  }
};

// Get all machinery across all farms (for owner role)
export const getAllMachinery = async (): Promise<Machinery[]> => {
  try {
    const allMachinery: Machinery[] = [];
    
    // Get all farms first to query their subcollections
    const farmsRef = collection(db, "farms");
    const farmsSnapshot = await getDocs(farmsRef);
    
    // For each farm, query its machinery subcollection
    const queryPromises = farmsSnapshot.docs.map(async (farmDoc) => {
      const farmId = farmDoc.id;
      const machineryRef = collection(db, "farms", farmId, "machinery");
      const machinerySnapshot = await getDocs(machineryRef);
      
      machinerySnapshot.forEach(docSnapshot => {
        const data = docSnapshot.data() as FirestoreMachineryData;
        allMachinery.push({
          id: docSnapshot.id,
          farmId: farmId,
          name: data.name || '',
          type: (data.type as Machinery['type']) || 'other',
          model: data.model || '',
          year: data.year || 0,
          serialNumber: data.serialNumber || '',
          status: (data.status as Machinery['status']) || 'working',
          fuelType: (data.fuelType as Machinery['fuelType']) || 'diesel',
          fuelCapacity: data.fuelCapacity || 0,
          currentFuelLevel: data.currentFuelLevel || 0,
          odometerReading: data.odometerReading || 0,
          lastMaintenanceDate: data.lastMaintenanceDate || '',
          nextMaintenanceDate: data.nextMaintenanceDate || '',
          maintenanceIntervalHours: data.maintenanceIntervalHours || 0,
          currentLocation: data.currentLocation,
          assignedToFarm: data.assignedToFarm,
          imageUrl: data.imageUrl,
          notes: data.notes,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString(),
          createdBy: data.createdBy || '',
        });
      });
    });
    
    // Wait for all queries to complete
    await Promise.all(queryPromises);
    
    return allMachinery;
  } catch (error) {
    console.error("Error getting all machinery:", error);
    return [];
  }
};

// Get machinery item by ID
export const getMachineryById = async (machineryId: string, farmId?: string): Promise<Machinery | null> => {
  try {
    if (farmId) {
      // If farmId is provided, get the item directly
      const machineryRef = doc(db, 'farms', farmId, 'machinery', machineryId);
      const machineryDoc = await getDoc(machineryRef);
      
      if (machineryDoc.exists()) {
        const data = machineryDoc.data() as FirestoreMachineryData;
        return {
          id: machineryDoc.id,
          farmId: farmId,
          name: data.name || '',
          type: (data.type as Machinery['type']) || 'other',
          model: data.model || '',
          year: data.year || 0,
          serialNumber: data.serialNumber || '',
          status: (data.status as Machinery['status']) || 'working',
          fuelType: (data.fuelType as Machinery['fuelType']) || 'diesel',
          fuelCapacity: data.fuelCapacity || 0,
          currentFuelLevel: data.currentFuelLevel || 0,
          odometerReading: data.odometerReading || 0,
          lastMaintenanceDate: data.lastMaintenanceDate || '',
          nextMaintenanceDate: data.nextMaintenanceDate || '',
          maintenanceIntervalHours: data.maintenanceIntervalHours || 0,
          currentLocation: data.currentLocation,
          assignedToFarm: data.assignedToFarm,
          imageUrl: data.imageUrl,
          notes: data.notes,
          createdAt: data.createdAt || new Date().toISOString(),
          updatedAt: data.updatedAt || new Date().toISOString(),
          createdBy: data.createdBy || '',
        };
      }
    } else {
      // If no farmId provided, search across all farms
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      for (const farmDoc of farmsSnapshot.docs) {
        const currentFarmId = farmDoc.id;
        const machineryRef = doc(db, 'farms', currentFarmId, 'machinery', machineryId);
        const machineryDoc = await getDoc(machineryRef);
        
        if (machineryDoc.exists()) {
          const data = machineryDoc.data() as FirestoreMachineryData;
          return {
            id: machineryDoc.id,
            farmId: currentFarmId,
            name: data.name || '',
            type: (data.type as Machinery['type']) || 'other',
            model: data.model || '',
            year: data.year || 0,
            serialNumber: data.serialNumber || '',
            status: (data.status as Machinery['status']) || 'working',
            fuelType: (data.fuelType as Machinery['fuelType']) || 'diesel',
            fuelCapacity: data.fuelCapacity || 0,
            currentFuelLevel: data.currentFuelLevel || 0,
            odometerReading: data.odometerReading || 0,
            lastMaintenanceDate: data.lastMaintenanceDate || '',
            nextMaintenanceDate: data.nextMaintenanceDate || '',
            maintenanceIntervalHours: data.maintenanceIntervalHours || 0,
            currentLocation: data.currentLocation,
            assignedToFarm: data.assignedToFarm,
            imageUrl: data.imageUrl,
            notes: data.notes,
            createdAt: data.createdAt || new Date().toISOString(),
            updatedAt: data.updatedAt || new Date().toISOString(),
            createdBy: data.createdBy || '',
          };
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error getting machinery item:", error);
    return null;
  }
};

// Create machinery item
export const addMachinery = async (machineryData: Omit<Machinery, 'id' | 'createdAt' | 'updatedAt'>): Promise<Machinery> => {
  try {
    if (!machineryData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = machineryData.farmId;
    
    // Remove undefined values and prepare clean data
    const cleanedMachineryData: Record<string, any> = {};
    Object.entries(machineryData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedMachineryData[key] = value;
      }
    });
    
    const newMachinery = {
      ...cleanedMachineryData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    console.log('Adding machinery to farm:', farmId);
    console.log('Machinery data:', newMachinery);
    
    // Add to Firestore
    const machineryRef = collection(db, 'farms', farmId, 'machinery');
    const docRef = await addDoc(machineryRef, newMachinery);
    
    const createdMachinery: Machinery = {
      id: docRef.id,
      farmId: farmId,
      ...newMachinery,
    } as Machinery;
    
    // Create farm expense if machinery has price
    if (machineryData.price && machineryData.price > 0 && machineryData.createdBy) {
      try {
        const { createFarmExpenseForNewItem } = await import('./expense-service');
        const { getFarmById } = await import('./farm-service');

        const farm = await getFarmById(farmId);
        if (farm) {
          await createFarmExpenseForNewItem(
            farmId,
            farm.name,
            'machinery',
            docRef.id,
            machineryData.name,
            machineryData.price,
            1, // Machinery quantity is always 1
            machineryData.createdBy,
            'Admin', // Assuming admin adds machinery
            'admin'
          );
        }
      } catch (expenseError) {
        console.error('Error creating farm expense for machinery:', expenseError);
        // Don't fail the machinery creation if expense creation fails
      }
    }

    // Update cache
    try {
      const cachedMachinery = await AsyncStorage.getItem(`machinery_${farmId}`);
      if (cachedMachinery) {
        const items: Machinery[] = JSON.parse(cachedMachinery);
        items.push(createdMachinery);
        await AsyncStorage.setItem(`machinery_${farmId}`, JSON.stringify(items));
      }
    } catch (cacheError) {
      console.error("Error updating cache:", cacheError);
    }

    console.log('Machinery added successfully:', createdMachinery);
    return createdMachinery;
  } catch (error) {
    console.error("Error creating machinery item:", error);
    throw error;
  }
};

// Update machinery item
export const updateMachinery = async (machineryId: string, machineryData: Partial<Machinery>): Promise<Machinery | null> => {
  try {
    if (!machineryData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = machineryData.farmId;
    
    // Get the current item
    const machineryRef = doc(db, 'farms', farmId, 'machinery', machineryId);
    const machineryDoc = await getDoc(machineryRef);
    
    if (!machineryDoc.exists()) {
      throw new Error("Machinery not found");
    }
    
    // Remove undefined values
    const cleanedMachineryData: Record<string, any> = {};
    Object.entries(machineryData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedMachineryData[key] = value;
      }
    });
    
    const updatedMachinery = {
      ...cleanedMachineryData,
      updatedAt: new Date().toISOString(),
    };
    
    // Update in Firestore
    await updateDoc(machineryRef, updatedMachinery);
    
    const currentData = machineryDoc.data() as FirestoreMachineryData;
    const result: Machinery = {
      id: machineryId,
      farmId: farmId,
      name: cleanedMachineryData.name || currentData.name || '',
      type: (cleanedMachineryData.type || currentData.type) as Machinery['type'] || 'other',
      model: cleanedMachineryData.model || currentData.model || '',
      year: cleanedMachineryData.year || currentData.year || 0,
      serialNumber: cleanedMachineryData.serialNumber || currentData.serialNumber || '',
      status: (cleanedMachineryData.status || currentData.status) as Machinery['status'] || 'working',
      fuelType: (cleanedMachineryData.fuelType || currentData.fuelType) as Machinery['fuelType'] || 'diesel',
      fuelCapacity: cleanedMachineryData.fuelCapacity || currentData.fuelCapacity || 0,
      currentFuelLevel: cleanedMachineryData.currentFuelLevel || currentData.currentFuelLevel || 0,
      odometerReading: cleanedMachineryData.odometerReading || currentData.odometerReading || 0,
      lastMaintenanceDate: cleanedMachineryData.lastMaintenanceDate || currentData.lastMaintenanceDate || '',
      nextMaintenanceDate: cleanedMachineryData.nextMaintenanceDate || currentData.nextMaintenanceDate || '',
      maintenanceIntervalHours: cleanedMachineryData.maintenanceIntervalHours || currentData.maintenanceIntervalHours || 0,
      currentLocation: cleanedMachineryData.currentLocation || currentData.currentLocation,
      assignedToFarm: cleanedMachineryData.assignedToFarm || currentData.assignedToFarm,
      imageUrl: cleanedMachineryData.imageUrl || currentData.imageUrl,
      notes: cleanedMachineryData.notes || currentData.notes,
      createdAt: currentData.createdAt || new Date().toISOString(),
      updatedAt: updatedMachinery.updatedAt,
      createdBy: cleanedMachineryData.createdBy || currentData.createdBy || '',
    };
    
    // Clear cache to ensure fresh data is loaded
    try {
      await AsyncStorage.removeItem(`machinery_${farmId}`);
      console.log(`Cleared machinery cache for farm ${farmId} after update`);
    } catch (cacheError) {
      console.error("Error clearing cache:", cacheError);
    }
    
    return result;
  } catch (error) {
    console.error("Error updating machinery item:", error);
    throw error;
  }
};

// Update machinery status (helper function)
export const updateMachineryStatus = async (
  machineryId: string, 
  farmId: string, 
  status: Machinery['status']
): Promise<void> => {
  try {
    const machineryRef = doc(db, 'farms', farmId, 'machinery', machineryId);
    await updateDoc(machineryRef, {
      status: status,
      updatedAt: new Date().toISOString(),
    });
    
    console.log(`Updated machinery ${machineryId} status to ${status}`);
    
    // Clear cache to ensure fresh data is loaded
    try {
      await AsyncStorage.removeItem(`machinery_${farmId}`);
      console.log(`Cleared machinery cache for farm ${farmId} after status update`);
    } catch (cacheError) {
      console.error("Error clearing cache:", cacheError);
    }
  } catch (error) {
    console.error("Error updating machinery status:", error);
    throw error;
  }
};

// Delete machinery item
export const deleteMachinery = async (machineryId: string, farmId?: string): Promise<boolean> => {
  try {
    if (farmId) {
      // If farmId is provided, delete the item directly
      const machineryRef = doc(db, 'farms', farmId, 'machinery', machineryId);
      await deleteDoc(machineryRef);
      
      // Update cache
      try {
        const cachedMachinery = await AsyncStorage.getItem(`machinery_${farmId}`);
        if (cachedMachinery) {
          const items: Machinery[] = JSON.parse(cachedMachinery);
          const updatedItems = items.filter(item => item.id !== machineryId);
          await AsyncStorage.setItem(`machinery_${farmId}`, JSON.stringify(updatedItems));
        }
      } catch (cacheError) {
        console.error("Error updating cache:", cacheError);
      }
      
      return true;
    } else {
      // If no farmId provided, search across all farms
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      for (const farmDoc of farmsSnapshot.docs) {
        const currentFarmId = farmDoc.id;
        const machineryRef = doc(db, 'farms', currentFarmId, 'machinery', machineryId);
        const machineryDoc = await getDoc(machineryRef);
        
        if (machineryDoc.exists()) {
          await deleteDoc(machineryRef);
          
          // Update cache
          try {
            const cachedMachinery = await AsyncStorage.getItem(`machinery_${currentFarmId}`);
            if (cachedMachinery) {
              const items: Machinery[] = JSON.parse(cachedMachinery);
              const updatedItems = items.filter(item => item.id !== machineryId);
              await AsyncStorage.setItem(`machinery_${currentFarmId}`, JSON.stringify(updatedItems));
            }
          } catch (cacheError) {
            console.error("Error updating cache:", cacheError);
          }
          
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error("Error deleting machinery item:", error);
    throw error;
  }
};

// Machinery Requests
export const getMachineryRequests = async (farmId?: string, status?: string): Promise<MachineryRequest[]> => {
  try {
    const requests: MachineryRequest[] = [];
    
    if (farmId) {
      // Get requests for this specific farm
      const requestsRef = collection(db, "farms", farmId, "machinery_requests");
      let requestsSnapshot;
      
      if (status) {
        const requestsQuery = query(requestsRef, where("status", "==", status));
        requestsSnapshot = await getDocs(requestsQuery);
      } else {
        requestsSnapshot = await getDocs(requestsRef);
      }
      
      requestsSnapshot.forEach(docSnapshot => {
        const data = docSnapshot.data();
        requests.push({
          id: docSnapshot.id,
          ...data
        } as MachineryRequest);
      });
    } else {
      // Get requests from all farms
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      const queryPromises = farmsSnapshot.docs.map(async (farmDoc) => {
        const currentFarmId = farmDoc.id;
        const requestsRef = collection(db, "farms", currentFarmId, "machinery_requests");
        let requestsSnapshot;
        
        if (status) {
          const requestsQuery = query(requestsRef, where("status", "==", status));
          requestsSnapshot = await getDocs(requestsQuery);
        } else {
          requestsSnapshot = await getDocs(requestsRef);
        }
        
        requestsSnapshot.forEach(docSnapshot => {
          const data = docSnapshot.data();
          requests.push({
            id: docSnapshot.id,
            ...data
          } as MachineryRequest);
        });
      });
      
      await Promise.all(queryPromises);
    }
    
    return requests.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error("Error getting machinery requests:", error);
    return [];
  }
};

export const createMachineryRequest = async (request: Omit<MachineryRequest, 'id' | 'createdAt' | 'updatedAt'>): Promise<MachineryRequest> => {
  try {
    if (!request.fromFarmId) {
      throw new Error("Source farm ID is required");
    }
    
    const farmId = request.fromFarmId;
    
    // Remove undefined values
    const cleanedRequestData: Record<string, any> = {};
    Object.entries(request).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedRequestData[key] = value;
      }
    });
    
    const newRequest = {
      ...cleanedRequestData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Add to Firestore
    const requestsRef = collection(db, "farms", farmId, "machinery_requests");
    const docRef = await addDoc(requestsRef, newRequest);
    
    const createdRequest: MachineryRequest = {
      id: docRef.id,
      ...newRequest,
    } as MachineryRequest;
    
    return createdRequest;
  } catch (error) {
    console.error("Error creating machinery request:", error);
    throw error;
  }
};

export const updateMachineryRequest = async (requestId: string, updates: Partial<MachineryRequest>, farmId?: string): Promise<MachineryRequest | null> => {
  try {
    if (farmId) {
      // If farmId is provided, update the request directly
      const requestRef = doc(db, "farms", farmId, "machinery_requests", requestId);
      const requestDoc = await getDoc(requestRef);
      
      if (!requestDoc.exists()) {
        throw new Error("Request not found");
      }
      
      // Remove undefined values
      const cleanedUpdates: Record<string, any> = {};
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          cleanedUpdates[key] = value;
        }
      });
      
      const updatedRequest = {
        ...cleanedUpdates,
        updatedAt: new Date().toISOString(),
      };
      
      await updateDoc(requestRef, updatedRequest);

      const currentData = requestDoc.data();
      const finalRequest = {
        id: requestId,
        ...currentData,
        ...cleanedUpdates,
        updatedAt: updatedRequest.updatedAt,
      } as MachineryRequest;

      // Create expense if request is approved and has cost
      if (updates.status === 'approved') {
        try {
          const { createExpense } = await import('./expense-service');
          const { getFarmById } = await import('./farm-service');
          const { getUserById } = await import('./user-service');

          const farm = await getFarmById(farmId);
          const requester = await getUserById(finalRequest.requesterId);

          if (farm && requester) {
            let expenseAmount = 0;
            let expenseDescription = '';
            let expenseCategory = '';

            if (finalRequest.requestType === 'fuel' && finalRequest.fuelPrice && finalRequest.fuelPrice > 0) {
              expenseAmount = finalRequest.fuelPrice;
              expenseDescription = `Fuel for ${finalRequest.machineryId} - ${finalRequest.fuelAmount || 'Unknown'} liters`;
              expenseCategory = 'fuel';
            } else if (finalRequest.requestType === 'maintenance' && finalRequest.maintenancePrice && finalRequest.maintenancePrice > 0) {
              expenseAmount = finalRequest.maintenancePrice;
              expenseDescription = `Maintenance for ${finalRequest.machineryId} - ${finalRequest.maintenanceType || 'General maintenance'}`;
              expenseCategory = 'maintenance';
            }

            if (expenseAmount > 0) {
              await createExpense({
                farmId: farmId,
                farmName: farm.name,
                type: 'user',
                category: expenseCategory,
                amount: expenseAmount,
                description: expenseDescription,
                date: new Date().toISOString().split('T')[0],
                relatedItemId: finalRequest.machineryId,
                relatedItemName: `Machinery ${finalRequest.machineryId}`,
                relatedItemType: 'machinery',
                notes: `Automatic expense from approved ${finalRequest.requestType} request`,
                createdBy: finalRequest.requesterId,
                createdByName: requester.displayName || requester.email || 'Unknown User',
                createdByRole: requester.role || 'unknown',
              });
            }
          }
        } catch (expenseError) {
          console.error('Error creating expense for machinery request:', expenseError);
          // Don't fail the request approval if expense creation fails
        }
      }

      return finalRequest;
    } else {
      // If no farmId provided, search across all farms
      const farmsRef = collection(db, "farms");
      const farmsSnapshot = await getDocs(farmsRef);
      
      for (const farmDoc of farmsSnapshot.docs) {
        const currentFarmId = farmDoc.id;
        const requestRef = doc(db, "farms", currentFarmId, "machinery_requests", requestId);
        const requestDoc = await getDoc(requestRef);
        
        if (requestDoc.exists()) {
          // Remove undefined values
          const cleanedUpdates: Record<string, any> = {};
          Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              cleanedUpdates[key] = value;
            }
          });
          
          const updatedRequest = {
            ...cleanedUpdates,
            updatedAt: new Date().toISOString(),
          };
          
          await updateDoc(requestRef, updatedRequest);
          
          const currentData = requestDoc.data();
          return {
            id: requestId,
            ...currentData,
            ...cleanedUpdates,
            updatedAt: updatedRequest.updatedAt,
          } as MachineryRequest;
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error updating machinery request:", error);
    throw error;
  }
};

// ===== MACHINERY RETURN FUNCTIONS =====

// Create machinery return
export const createMachineryReturn = async (returnData: {
  requestId: string;
  machineryId: string;
  machineryName: string;
  caretakerId: string;
  caretakerName: string;
  farmId: string;
  farmName?: string;
  type?: 'use' | 'maintenance'; // Optional, will be determined from request if not provided
  returnCondition: 'working' | 'minor_issue' | 'needs_repair';
  hoursUsed?: number;
  fuelUsed?: number;
  odometerReading?: number;
  remarks?: string;
}): Promise<MachineryReturn> => {
  try {
    if (!returnData.farmId) {
      throw new Error("Farm ID is required");
    }
    
    const farmId = returnData.farmId;

    // Determine the return type from the original request if not provided
    let returnType = returnData.type || 'use';
    if (!returnData.type) {
      try {
        // Get the original request to determine the type
        const { getRequestById } = await import('./request-service');
        const originalRequest = await getRequestById(returnData.requestId, farmId);
        if (originalRequest && originalRequest.requestSubType) {
          returnType = originalRequest.requestSubType as 'use' | 'maintenance';
        }
      } catch (error) {
        console.warn('Could not determine request type, defaulting to use:', error);
        returnType = 'use';
      }
    }

    // Create the new return data object, filtering out undefined values
    const newReturnData: Record<string, any> = {
      requestId: returnData.requestId,
      machineryId: returnData.machineryId,
      machineryName: returnData.machineryName,
      caretakerId: returnData.caretakerId,
      caretakerName: returnData.caretakerName,
      farmId: farmId,
      type: returnType,
      returnCondition: returnData.returnCondition,
      returnedAt: new Date().toISOString(),
      returnStatus: 'pending',
      createdAt: new Date().toISOString(),
    };
    
    // Only add optional fields if they have values
    if (returnData.farmName && returnData.farmName.trim()) {
      newReturnData.farmName = returnData.farmName.trim();
    }
    
    if (returnData.hoursUsed !== undefined && returnData.hoursUsed > 0) {
      newReturnData.hoursUsed = returnData.hoursUsed;
    }
    
    if (returnData.fuelUsed !== undefined && returnData.fuelUsed > 0) {
      newReturnData.fuelUsed = returnData.fuelUsed;
    }
    
    if (returnData.odometerReading !== undefined && returnData.odometerReading > 0) {
      newReturnData.odometerReading = returnData.odometerReading;
    }
    
    if (returnData.remarks && returnData.remarks.trim()) {
      newReturnData.remarks = returnData.remarks.trim();
    }
    
    // Add to Firestore
    const returnsRef = collection(db, "farms", farmId, "machineryReturns");
    const docRef = await addDoc(returnsRef, newReturnData);
    
    // Update the original request to mark it has a return
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId);
    await updateDoc(requestRef, {
      hasReturn: true,
      returnId: docRef.id,
      returnStatus: "pending",
      updatedAt: new Date().toISOString(),
    });
    
    const createdReturn: MachineryReturn = {
      id: docRef.id,
      requestId: newReturnData.requestId,
      machineryId: newReturnData.machineryId,
      machineryName: newReturnData.machineryName,
      caretakerId: newReturnData.caretakerId,
      caretakerName: newReturnData.caretakerName,
      farmId: newReturnData.farmId,
      farmName: newReturnData.farmName,
      type: newReturnData.type,
      returnCondition: newReturnData.returnCondition,
      hoursUsed: newReturnData.hoursUsed,
      fuelUsed: newReturnData.fuelUsed,
      odometerReading: newReturnData.odometerReading,
      remarks: newReturnData.remarks,
      returnedAt: newReturnData.returnedAt,
      returnStatus: newReturnData.returnStatus,
      createdAt: newReturnData.createdAt,
    };
    
    return createdReturn;
  } catch (error) {
    console.error("Error creating machinery return:", error);
    throw error;
  }
};

// Get machinery returns for a farm
export const getMachineryReturns = async (
  farmId: string,
  status?: "pending" | "approved" | "rejected"
): Promise<MachineryReturn[]> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returns: MachineryReturn[] = [];
    
    // Get returns for this specific farm
    const returnsRef = collection(db, "farms", farmId, "machineryReturns");
    
    // Build query based on filters
    let returnsQuery: any = returnsRef;
    
    if (status) {
      returnsQuery = query(returnsRef, where("returnStatus", "==", status));
    }
    
    const returnsSnapshot = await getDocs(returnsQuery);
    
    returnsSnapshot.forEach(docSnapshot => {
      const data = docSnapshot.data() as FirestoreMachineryReturnData;
      
      returns.push({
        id: docSnapshot.id,
        requestId: data.requestId || "",
        machineryId: data.machineryId || "",
        machineryName: data.machineryName || "",
        caretakerId: data.caretakerId || "",
        caretakerName: data.caretakerName || "",
        farmId: farmId,
        farmName: data.farmName,
        type: (data.type as 'use') || 'use',
        returnCondition: (data.returnCondition as 'working' | 'minor_issue' | 'needs_repair') || 'working',
        hoursUsed: data.hoursUsed,
        fuelUsed: data.fuelUsed,
        odometerReading: data.odometerReading,
        remarks: data.remarks,
        returnedAt: data.returnedAt || new Date().toISOString(),
        returnStatus: (data.returnStatus as 'pending' | 'approved' | 'rejected') || 'pending',
        reviewedBy: data.reviewedBy,
        reviewedByName: data.reviewedByName,
        reviewedAt: data.reviewedAt,
        rejectionReason: data.rejectionReason,
        adminDescription: data.adminDescription,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
      });
    });
    
    // Sort by createdAt
    returns.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return returns;
  } catch (error) {
    console.error("Error getting machinery returns:", error);
    return [];
  }
};

// Get machinery return by ID
export const getMachineryReturnById = async (returnId: string, farmId: string): Promise<MachineryReturn | null> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "machineryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (returnDoc.exists()) {
      const data = returnDoc.data() as FirestoreMachineryReturnData;
      
      return {
        id: returnDoc.id,
        requestId: data.requestId || "",
        machineryId: data.machineryId || "",
        machineryName: data.machineryName || "",
        caretakerId: data.caretakerId || "",
        caretakerName: data.caretakerName || "",
        farmId: farmId,
        farmName: data.farmName,
        type: (data.type as 'use') || 'use',
        returnCondition: (data.returnCondition as 'working' | 'minor_issue' | 'needs_repair') || 'working',
        hoursUsed: data.hoursUsed,
        fuelUsed: data.fuelUsed,
        odometerReading: data.odometerReading,
        remarks: data.remarks,
        returnedAt: data.returnedAt || new Date().toISOString(),
        returnStatus: (data.returnStatus as 'pending' | 'approved' | 'rejected') || 'pending',
        reviewedBy: data.reviewedBy,
        reviewedByName: data.reviewedByName,
        reviewedAt: data.reviewedAt,
        rejectionReason: data.rejectionReason,
        adminDescription: data.adminDescription,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt,
      };
    }
    
    return null;
  } catch (error) {
    console.error("Error getting machinery return by ID:", error);
    return null;
  }
};

// Approve machinery return
export const approveMachineryReturn = async (
  returnId: string,
  farmId: string,
  reviewedBy: string,
  reviewedByName: string,
  adminDescription?: string
): Promise<MachineryReturn> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "machineryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (!returnDoc.exists()) {
      throw new Error("Return not found");
    }

    const returnData = returnDoc.data() as FirestoreMachineryReturnData;
    
    // Update return status
    const updateData: Record<string, any> = {
      returnStatus: "approved",
      reviewedBy,
      reviewedByName,
      reviewedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    if (adminDescription && adminDescription.trim()) {
      updateData.adminDescription = adminDescription.trim();
    }
    
    await updateDoc(returnRef, updateData);
    
    // Update the original request
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId || "");
    await updateDoc(requestRef, {
      returnStatus: "approved",
      updatedAt: new Date().toISOString(),
    });
    
    // CRITICAL FIX: Update machinery status based on return condition
    if (returnData.machineryId) {
      let newStatus: Machinery['status'] = 'working';

      // Set status based on return condition
      if (returnData.returnCondition === 'needs_repair') {
        newStatus = 'maintenance';
      } else if (returnData.returnCondition === 'minor_issue') {
        newStatus = 'maintenance';
      } else {
        // For 'working' condition, set machinery back to 'working' status
        newStatus = 'working';
      }

      // Update machinery status to make it available again
      await updateMachineryStatus(returnData.machineryId, farmId, newStatus);

      console.log(`Updated machinery ${returnData.machineryId} status from 'in_use' to '${newStatus}' after return approval`);
    }

    // CRITICAL FIX: Update allocation status when machinery return is approved
    try {
      const { updateAllocation, getAllocationsByRequestId } = await import('./allocation-service');

      // Find the allocation for this request
      const allocations = await getAllocationsByRequestId(farmId, returnData.requestId || '');

      if (allocations.length > 0) {
        const allocation = allocations[0]; // Should only be one allocation per request

        // Prepare update data, filtering out undefined values
        const updateData: Record<string, any> = {
          status: 'returned',
          actualReturnDate: new Date().toISOString(),
          condition: returnData.returnCondition === 'working' ? 'excellent' :
                    returnData.returnCondition === 'minor_issue' ? 'good' : 'poor',
          updatedAt: new Date().toISOString(),
        };

        // Only add returnNotes if it has a value
        if (returnData.remarks && returnData.remarks.trim()) {
          updateData.returnNotes = returnData.remarks.trim();
        }

        // Update allocation status to returned
        await updateAllocation(allocation.id, updateData);

        console.log(`Updated allocation ${allocation.id} status to 'returned' after machinery return approval`);
      } else {
        console.warn(`No allocation found for machinery return requestId: ${returnData.requestId}`);
      }
    } catch (allocationError) {
      console.error('Error updating allocation status after machinery return approval:', allocationError);
      // Don't fail the return approval if allocation update fails
    }
    
    const result: MachineryReturn = {
      id: returnDoc.id,
      requestId: returnData.requestId || "",
      machineryId: returnData.machineryId || "",
      machineryName: returnData.machineryName || "",
      caretakerId: returnData.caretakerId || "",
      caretakerName: returnData.caretakerName || "",
      farmId: farmId,
      farmName: returnData.farmName,
      type: (returnData.type as 'use') || 'use',
      returnCondition: (returnData.returnCondition as 'working' | 'minor_issue' | 'needs_repair') || 'working',
      hoursUsed: returnData.hoursUsed,
      fuelUsed: returnData.fuelUsed,
      odometerReading: returnData.odometerReading,
      remarks: returnData.remarks,
      returnedAt: returnData.returnedAt || new Date().toISOString(),
      returnStatus: "approved",
      reviewedBy,
      reviewedByName,
      reviewedAt: updateData.reviewedAt,
      rejectionReason: returnData.rejectionReason,
      adminDescription: updateData.adminDescription || returnData.adminDescription,
      createdAt: returnData.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
    };
    
    return result;
  } catch (error) {
    console.error("Error approving machinery return:", error);
    throw error;
  }
};

// Reject machinery return
export const rejectMachineryReturn = async (
  returnId: string,
  farmId: string,
  reviewedBy: string,
  reviewedByName: string,
  rejectionReason: string,
  adminDescription?: string
): Promise<MachineryReturn> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }

    const returnRef = doc(db, "farms", farmId, "machineryReturns", returnId);
    const returnDoc = await getDoc(returnRef);
    
    if (!returnDoc.exists()) {
      throw new Error("Return not found");
    }

    const returnData = returnDoc.data() as FirestoreMachineryReturnData;
    
    // Update return status
    const updateData: Record<string, any> = {
      returnStatus: "rejected",
      reviewedBy,
      reviewedByName,
      reviewedAt: new Date().toISOString(),
      rejectionReason: rejectionReason.trim(),
      updatedAt: new Date().toISOString(),
    };
    
    if (adminDescription && adminDescription.trim()) {
      updateData.adminDescription = adminDescription.trim();
    }
    
    await updateDoc(returnRef, updateData);
    
    // Update the original request
    const requestRef = doc(db, "farms", farmId, "requests", returnData.requestId || "");
    await updateDoc(requestRef, {
      returnStatus: "rejected",
      updatedAt: new Date().toISOString(),
    });
    
    // NOTE: When return is rejected, machinery remains in 'in_use' status
    // Admin may need to manually update machinery status if needed
    
    const result: MachineryReturn = {
      id: returnDoc.id,
      requestId: returnData.requestId || "",
      machineryId: returnData.machineryId || "",
      machineryName: returnData.machineryName || "",
      caretakerId: returnData.caretakerId || "",
      caretakerName: returnData.caretakerName || "",
      farmId: farmId,
      farmName: returnData.farmName,
      type: (returnData.type as 'use') || 'use',
      returnCondition: (returnData.returnCondition as 'working' | 'minor_issue' | 'needs_repair') || 'working',
      hoursUsed: returnData.hoursUsed,
      fuelUsed: returnData.fuelUsed,
      odometerReading: returnData.odometerReading,
      remarks: returnData.remarks,
      returnedAt: returnData.returnedAt || new Date().toISOString(),
      returnStatus: "rejected",
      reviewedBy,
      reviewedByName,
      reviewedAt: updateData.reviewedAt,
      rejectionReason: updateData.rejectionReason,
      adminDescription: updateData.adminDescription || returnData.adminDescription,
      createdAt: returnData.createdAt || new Date().toISOString(),
      updatedAt: updateData.updatedAt,
    };
    
    return result;
  } catch (error) {
    console.error("Error rejecting machinery return:", error);
    throw error;
  }
};

// Maintenance Records
export const getMaintenanceRecords = async (machineryId?: string, farmId?: string): Promise<MaintenanceRecord[]> => {
  try {
    const records: MaintenanceRecord[] = [];
    
    if (farmId) {
      const recordsRef = collection(db, "farms", farmId, "maintenance_records");
      let recordsSnapshot;
      
      if (machineryId) {
        const recordsQuery = query(recordsRef, where("machineryId", "==", machineryId));
        recordsSnapshot = await getDocs(recordsQuery);
      } else {
        recordsSnapshot = await getDocs(recordsRef);
      }
      
      recordsSnapshot.forEach(docSnapshot => {
        const data = docSnapshot.data();
        records.push({
          id: docSnapshot.id,
          ...data
        } as MaintenanceRecord);
      });
    }
    
    return records.sort((a, b) => new Date(b.performedAt).getTime() - new Date(a.performedAt).getTime());
  } catch (error) {
    console.error("Error getting maintenance records:", error);
    return [];
  }
};

export const addMaintenanceRecord = async (record: Omit<MaintenanceRecord, 'id' | 'createdAt'>, farmId: string): Promise<MaintenanceRecord> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    // Remove undefined values
    const cleanedRecordData: Record<string, any> = {};
    Object.entries(record).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedRecordData[key] = value;
      }
    });
    
    const newRecord = {
      ...cleanedRecordData,
      createdAt: new Date().toISOString(),
    };
    
    const recordsRef = collection(db, "farms", farmId, "maintenance_records");
    const docRef = await addDoc(recordsRef, newRecord);
    
    return {
      id: docRef.id,
      ...newRecord,
    } as MaintenanceRecord;
  } catch (error) {
    console.error("Error adding maintenance record:", error);
    throw error;
  }
};

// Fuel Records
export const getFuelRecords = async (machineryId?: string, farmId?: string): Promise<FuelRecord[]> => {
  try {
    const records: FuelRecord[] = [];
    
    if (farmId) {
      const recordsRef = collection(db, "farms", farmId, "fuel_records");
      let recordsSnapshot;
      
      if (machineryId) {
        const recordsQuery = query(recordsRef, where("machineryId", "==", machineryId));
        recordsSnapshot = await getDocs(recordsQuery);
      } else {
        recordsSnapshot = await getDocs(recordsRef);
      }
      
      recordsSnapshot.forEach(docSnapshot => {
        const data = docSnapshot.data();
        records.push({
          id: docSnapshot.id,
          ...data
        } as FuelRecord);
      });
    }
    
    return records.sort((a, b) => new Date(b.performedAt).getTime() - new Date(a.performedAt).getTime());
  } catch (error) {
    console.error("Error getting fuel records:", error);
    return [];
  }
};

export const addFuelRecord = async (record: Omit<FuelRecord, 'id' | 'createdAt'>, farmId: string): Promise<FuelRecord> => {
  try {
    if (!farmId) {
      throw new Error("Farm ID is required");
    }
    
    // Remove undefined values
    const cleanedRecordData: Record<string, any> = {};
    Object.entries(record).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanedRecordData[key] = value;
      }
    });
    
    const newRecord = {
      ...cleanedRecordData,
      createdAt: new Date().toISOString(),
    };
    
    const recordsRef = collection(db, "farms", farmId, "fuel_records");
    const docRef = await addDoc(recordsRef, newRecord);
    
    return {
      id: docRef.id,
      ...newRecord,
    } as FuelRecord;
  } catch (error) {
    console.error("Error adding fuel record:", error);
    throw error;
  }
};

// Reports and Analytics
export const getMachineryReport = async (farmId?: string) => {
  try {
    const machinery = farmId ? await getMachineryByFarm(farmId) : await getAllMachinery();
    const requests = await getMachineryRequests(farmId);
    const maintenanceRecords = await getMaintenanceRecords(undefined, farmId);
    const fuelRecords = await getFuelRecords(undefined, farmId);
    
    const totalMachinery = machinery.length;
    const activeMachinery = machinery.filter(m => m.status === 'working').length;
    const maintenanceDue = machinery.filter(m => new Date(m.nextMaintenanceDate) <= new Date()).length;
    const malfunctionCount = machinery.filter(m => m.status === 'malfunction').length;
    
    const totalFuelConsumption = fuelRecords
      .filter(record => machinery.some(m => m.id === record.machineryId))
      .reduce((sum, record) => sum + record.amount, 0);
    
    const utilizationRate = totalMachinery > 0 ? (activeMachinery / totalMachinery) * 100 : 0;
    
    const interFarmRequests = requests.filter(req => req.requestType === 'use').length;
    
    const statusData = [
      { name: 'Working', count: activeMachinery, color: '#4CAF50' },
      { name: 'Maintenance', count: machinery.filter(m => m.status === 'maintenance').length, color: '#FF9800' },
      { name: 'Malfunction', count: malfunctionCount, color: '#F44336' },
      { name: 'In Use', count: machinery.filter(m => m.status === 'in_use' || m.status === 'in_use_other_farm').length, color: '#2196F3' },
    ];
    
    const typeData = machinery.reduce((acc, m) => {
      acc[m.type] = (acc[m.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const typeChartData = Object.entries(typeData).map(([type, count], index) => ({
      name: type.charAt(0).toUpperCase() + type.slice(1),
      count,
      color: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336', '#607D8B'][index % 6]
    }));
    
    return {
      summary: {
        totalMachinery,
        activeMachinery,
        maintenanceDue,
        malfunctionCount,
        fuelConsumption: totalFuelConsumption,
        utilizationRate: Math.round(utilizationRate * 10) / 10,
        interFarmRequests,
        pendingRequests: requests.filter(req => req.status === 'pending').length,
      },
      statusData,
      typeData: typeChartData,
      fuelData: {
        labels: machinery.map(m => m.name),
        datasets: [{
          data: machinery.map(m => m.currentFuelLevel)
        }]
      },
      utilizationData: {
        labels: machinery.map(m => m.name),
        datasets: [{
          data: machinery.map(m => m.status === 'working' ? 100 : m.status === 'maintenance' ? 50 : 0)
        }]
      }
    };
  } catch (error) {
    console.error("Error getting machinery report:", error);
    return {
      summary: {
        totalMachinery: 0,
        activeMachinery: 0,
        maintenanceDue: 0,
        malfunctionCount: 0,
        fuelConsumption: 0,
        utilizationRate: 0,
        interFarmRequests: 0,
        pendingRequests: 0,
      },
      statusData: [],
      typeData: [],
      fuelData: { labels: [], datasets: [{ data: [] }] },
      utilizationData: { labels: [], datasets: [{ data: [] }] }
    };
  }
};