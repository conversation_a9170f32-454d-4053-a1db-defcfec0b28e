import { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, ScrollView as RNScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { FilterButton } from "@/components/FilterButton";
import { NoteCard } from "@/components/NoteCard";
import { Search, Plus } from "lucide-react-native";
import { getNotesByFarm, Note, deleteNote } from "@/services/notes-service";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFocusEffect } from "@react-navigation/native";

export default function CaretakerNotesScreen() {
  const [notes, setNotes] = useState<Note[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<Note[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const router = useRouter();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { theme } = useTheme();
  const { t, isRTL } = useLanguage();
  const isDarkMode = theme === "dark";

  // Theme colors
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const primaryColor = "#FF9800"; // Orange for caretaker

  const loadNotes = useCallback(async () => {
    if (!selectedFarm) return;
    
    try {
      setLoading(true);
      console.log("Loading notes for farm:", selectedFarm.id);
      const data = await getNotesByFarm(selectedFarm.id);
      console.log("Loaded notes:", data.length);
      setNotes(data);
      applyCurrentFilter(data);
    } catch (error) {
      console.error("Error loading notes:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedFarm, activeFilter]);

  // Load notes when farm changes
  useEffect(() => {
    if (selectedFarm) {
      loadNotes();
    }
  }, [selectedFarm, loadNotes]);

  // Refresh notes when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (selectedFarm) {
        loadNotes();
      }
      return () => {};
    }, [selectedFarm, loadNotes])
  );

  const applyCurrentFilter = (notesToFilter: Note[] = notes) => {
    let filtered = notesToFilter;
    
    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (note) =>
          note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (note.createdByName || note.userName || "").toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply category filter
    if (activeFilter === "withAudio") {
      filtered = filtered.filter((note) => note.hasAudio);
    } else if (activeFilter === "withImages") {
      filtered = filtered.filter((note) => note.images && note.images.length > 0);
    } else if (activeFilter === "text") {
      filtered = filtered.filter((note) => !note.hasAudio && (!note.images || note.images.length === 0));
    }
    
    setFilteredNotes(filtered);
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    applyCurrentFilter();
  };

  useEffect(() => {
    applyCurrentFilter();
  }, [searchQuery, activeFilter, notes]);

  const applyFilter = (filter: string) => {
    setActiveFilter(filter);
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadNotes();
  };

  const handleAddNote = () => {
    if (!selectedFarm) {
      return;
    }
    router.push("/notes/create");
  };

  const handleNotePress = (noteId: string) => {
    if (!selectedFarm) return;
    router.push(`/notes/${noteId}?farmId=${selectedFarm.id}`);
  };

  const handleEditNote = (noteId: string) => {
    if (!selectedFarm) return;
    router.push(`/notes/edit?id=${noteId}&farmId=${selectedFarm.id}`);
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!selectedFarm) return;
    
    try {
      await deleteNote(noteId, selectedFarm.id);
      await loadNotes();
    } catch (error) {
      console.error("Error deleting note:", error);
    }
  };

  const renderNoteItem = ({ item }: { item: Note }) => (
    <NoteCard
      note={item}
      onPress={() => handleNotePress(item.id)}
      onEditPress={() => handleEditNote(item.id)}
      onDeletePress={() => handleDeleteNote(item.id)}
      showEditOptions={item.createdBy === user?.uid || item.userId === user?.uid}
      isDarkMode={isDarkMode}
      role="caretaker"
      currentUserId={user?.uid}
    />
  );

  const renderEmptyComponent = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
            {t("common.loading")}
          </Text>
        </View>
      );
    }
    
    if (!selectedFarm) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
            {t("common.noFarmSelected")}
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
          {searchQuery ? t("notes.noNotesFound") : t("notes.noNotes")}
        </Text>
        <TouchableOpacity 
          style={[styles.createNoteButton, { backgroundColor: primaryColor }]}
          onPress={handleAddNote}
        >
          <Text style={styles.createNoteText}>{t("notes.addNote")}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: cardColor, borderColor }]}>
          <Search size={20} color={secondaryTextColor} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
            placeholder={t("notes.searchNotes")}
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      <View style={styles.filterContainer}>
        <RNScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScroll}>
          <FilterButton
            label={t("notes.filters.all")}
            active={activeFilter === "all"}
            onPress={() => applyFilter("all")}
            activeColor={primaryColor}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.withAudio")}
            active={activeFilter === "withAudio"}
            onPress={() => applyFilter("withAudio")}
            activeColor={primaryColor}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.withImages")}
            active={activeFilter === "withImages"}
            onPress={() => applyFilter("withImages")}
            activeColor={primaryColor}
            isDarkMode={isDarkMode}
          />
          <FilterButton
            label={t("notes.filters.textOnly")}
            active={activeFilter === "text"}
            onPress={() => applyFilter("text")}
            activeColor={primaryColor}
            isDarkMode={isDarkMode}
          />
        </RNScrollView>
      </View>

      <FlatList
        data={filteredNotes}
        renderItem={renderNoteItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyComponent}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: primaryColor }]}
        onPress={handleAddNote}
        disabled={!selectedFarm}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  filterScroll: {
    paddingVertical: 8,
    gap: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  emptyContainer: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 20,
  },
  createNoteButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createNoteText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1000,
  },
});