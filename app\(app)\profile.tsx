import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Switch, TextInput, ScrollView, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import { Stack, router } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { updateUserProfile } from "@/services/user-service";
import { uploadUserProfileImage, deleteImageFromStorage } from "@/services/storage-service";
import { Moon, Sun, User, Mail, Phone, FileText, LogOut, Camera, Globe, Edit3, Calendar, Lock, Shield, ChevronRight } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import Constants from "expo-constants";

export default function ProfileScreen() {
  const { user, signOut, refreshUserData } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage, isRTL, t } = useLanguage();
  const isDarkMode = theme === "dark";
  
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.displayName || "",
    phone: user?.phone || "",
    bio: user?.bio || "",
  });

  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const primaryColor = "#3b82f6";
  
  const handleEditProfile = () => {
    setIsEditing(true);
  };
  
  const handleCancelEdit = () => {
    setFormData({
      displayName: user?.displayName || "",
      phone: user?.phone || "",
      bio: user?.bio || "",
    });
    setIsEditing(false);
  };
  
  const handleSaveProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log("💾 Saving profile changes:", {
        oldName: user.displayName,
        newName: formData.displayName,
        phone: formData.phone,
        bio: formData.bio
      });

      await updateUserProfile(user.uid, {
        name: formData.displayName,
        displayName: formData.displayName,
        phone: formData.phone,
        phoneNumber: formData.phone,
        bio: formData.bio,
      });

      console.log("🔄 Refreshing user data after profile update...");
      await refreshUserData();

      console.log("✅ Profile updated and user data refreshed");
      setIsEditing(false);

      // Show success message
      Alert.alert(t('common.success'), t('profile.profileUpdated'));
    } catch (error) {
      console.error("❌ Error updating profile:", error);
      Alert.alert(t('common.error'), t('profile.updateError'));
    } finally {
      setLoading(false);
    }
  };
  
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setUploading(true);
        const imageUri = result.assets[0].uri;

        try {
          // Delete old image from Storage if it exists
          if (user?.photoURL) {
            await deleteImageFromStorage(user.photoURL);
          }

          // Upload new image to Firebase Storage
          const downloadURL = await uploadUserProfileImage(imageUri, user?.uid || "");
          console.log("Image uploaded to Storage:", downloadURL);

          // Update user profile with the Firebase Storage download URL
          await updateUserProfile(user?.uid || "", {
            photoURL: downloadURL,
          });

          // Refresh user data to get the updated photoURL
          await refreshUserData();
          console.log("Profile image updated successfully");
        } catch (error) {
          console.error("Error uploading image:", error);
          Alert.alert("Error", "Failed to upload image. Please try again.");
        } finally {
          setUploading(false);
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
    }
  };
  
  const handleLogout = () => {
    signOut();
  };

  const handleLanguageToggle = async () => {
    const newLanguage = language === 'en' ? 'ur' : 'en';
    await setLanguage(newLanguage);
  };

  const handleChangePassword = () => {
    router.push("/(app)/change-password");
  };

  // Calculate member since date using createdAt field
  const memberSince = user?.createdAt 
    ? (typeof user.createdAt === 'string' ? new Date(user.createdAt).toLocaleDateString() : new Date().toLocaleDateString())
    : new Date().toLocaleDateString();
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={['top']}>
      <Stack.Screen 
        options={{
          title: t("profile.title"),
          headerStyle: {
            backgroundColor: primaryColor,
          },
          headerTintColor: "#fff",
        }} 
      />
      
      <ScrollView style={[styles.scrollView, { direction: isRTL ? 'rtl' : 'ltr' }]} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, isRTL && styles.headerRTL]}>
          <Text style={[styles.title, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("profile.title")}
          </Text>
        </View>

        {/* Profile Card */}
        <View style={[styles.profileCard, { backgroundColor: cardColor }]}>
          <View style={styles.profileImageContainer}>
            {uploading ? (
              <View style={[styles.profileImage, styles.uploadingContainer, { backgroundColor: `${primaryColor}20` }]}>
                <ActivityIndicator color={primaryColor} size="large" />
              </View>
            ) : (
              <>
                {user?.photoURL ? (
                  <Image source={{ uri: user.photoURL }} style={styles.profileImage} />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: primaryColor }]}>
                    <Text style={styles.profileImagePlaceholderText}>
                      {user?.displayName ? user.displayName.charAt(0).toUpperCase() : "U"}
                    </Text>
                  </View>
                )}
                
                {isEditing && (
                  <TouchableOpacity 
                    style={[styles.cameraButton, { backgroundColor: primaryColor }]}
                    onPress={handlePickImage}
                  >
                    <Camera size={18} color="#fff" />
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
          
          <View style={styles.profileInfo}>
            {isEditing ? (
              <TextInput
                style={[styles.nameInput, { color: textColor, borderColor: `${primaryColor}40`, textAlign: 'center' }]}
                value={formData.displayName}
                onChangeText={(text) => setFormData({ ...formData, displayName: text })}
                placeholder={t("common.name")}
                placeholderTextColor={secondaryTextColor}
              />
            ) : (
              <Text style={[styles.userName, { color: textColor, textAlign: 'center' }]}>
                {user?.displayName || t("common.unknown")}
              </Text>
            )}
            
            <View style={[styles.roleBadge, { backgroundColor: `${primaryColor}15` }]}>
              <Text style={[styles.roleText, { color: primaryColor }]}>
                {t(`profile.${user?.role}`)}
              </Text>
            </View>
            
            <Text style={[styles.userEmail, { color: secondaryTextColor, textAlign: 'center' }]}>
              {user?.email}
            </Text>
          </View>
        </View>
        
        {/* Account Information */}
        <View style={[styles.infoCard, { backgroundColor: cardColor }]}>
          <View style={[styles.sectionHeader, isRTL && styles.sectionHeaderRTL]}>
            <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("profile.personalInformation")}
            </Text>
            
            {!isEditing ? (
              <TouchableOpacity 
                style={[styles.editButton, { backgroundColor: `${primaryColor}15` }]}
                onPress={handleEditProfile}
              >
                <Edit3 size={16} color={primaryColor} />
                <Text style={[styles.editButtonText, { color: primaryColor, marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }]}>
                  {t("common.edit")}
                </Text>
              </TouchableOpacity>
            ) : (
              <View style={[styles.editActions, isRTL && styles.editActionsRTL]}>
                <TouchableOpacity 
                  style={[styles.actionButton, { backgroundColor: secondaryTextColor }]}
                  onPress={handleCancelEdit}
                  disabled={loading}
                >
                  <Text style={styles.actionButtonText}>
                    {t("common.cancel")}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.actionButton, { backgroundColor: primaryColor, marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}
                  onPress={handleSaveProfile}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.actionButtonText}>{t("common.save")}</Text>
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
          
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <User size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.name")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, { color: textColor, borderColor: `${primaryColor}40`, textAlign: isRTL ? 'left' : 'right' }]}
                value={formData.displayName}
                onChangeText={(text) => setFormData({ ...formData, displayName: text })}
                placeholder={t("common.name")}
                placeholderTextColor={secondaryTextColor}
              />
            ) : (
              <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
                {user?.displayName || t("profile.notSet")}
              </Text>
            )}
          </View>
          
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Mail size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.email")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {user?.email}
            </Text>
          </View>
          
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Phone size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.phone")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.infoInput, { color: textColor, borderColor: `${primaryColor}40`, textAlign: isRTL ? 'left' : 'right' }]}
                value={formData.phone}
                onChangeText={(text) => setFormData({ ...formData, phone: text })}
                placeholder={t("common.phone")}
                placeholderTextColor={secondaryTextColor}
                keyboardType="phone-pad"
              />
            ) : (
              <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
                {user?.phone || t("profile.notSet")}
              </Text>
            )}
          </View>

          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Calendar size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.memberSince")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {memberSince}
            </Text>
          </View>
          
          <View style={[styles.infoRow, styles.bioRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <FileText size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("common.bio")}
              </Text>
            </View>
            {isEditing ? (
              <TextInput
                style={[styles.bioInput, { color: textColor, borderColor: `${primaryColor}40`, textAlign: isRTL ? 'right' : 'left' }]}
                value={formData.bio}
                onChangeText={(text) => setFormData({ ...formData, bio: text })}
                placeholder={t("common.bio")}
                placeholderTextColor={secondaryTextColor}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            ) : (
              <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
                {user?.bio || t("profile.notSet")}
              </Text>
            )}
          </View>
        </View>
        
        {/* Settings */}
        <View style={[styles.settingsCard, { backgroundColor: cardColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("settings.title")}
          </Text>
          
          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              {isDarkMode ? (
                <Moon size={20} color={secondaryTextColor} />
              ) : (
                <Sun size={20} color={secondaryTextColor} />
              )}
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {isDarkMode ? t("profile.darkMode") : t("profile.lightMode")}
              </Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: "#e2e8f0", true: `${primaryColor}40` }}
              thumbColor={isDarkMode ? primaryColor : "#ffffff"}
            />
          </View>

          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <Globe size={20} color={secondaryTextColor} />
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("common.language")}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.languageButton, { backgroundColor: `${primaryColor}15` }]}
              onPress={handleLanguageToggle}
            >
              <Text style={[styles.languageButtonText, { color: primaryColor }]}>
                {language === 'en' ? t("profile.urdu") : t("profile.english")}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity 
            style={[styles.settingItem, styles.changePasswordItem, isRTL && styles.settingItemRTL]}
            onPress={handleChangePassword}
          >
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <View style={[styles.changePasswordIcon, { backgroundColor: `${primaryColor}15` }]}>
                <Shield size={18} color={primaryColor} />
              </View>
              <View style={[styles.changePasswordContent, { marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                <Text style={[styles.changePasswordTitle, { color: textColor }]}>
                  {t("changePassword.title")}
                </Text>
                {/* <Text style={[styles.changePasswordSubtitle, { color: secondaryTextColor }]}>
                  {t("changePassword.updateSecurityDescription")}
                </Text> */}
              </View>
            </View>
            <View style={[styles.changePasswordButton, { backgroundColor: `${primaryColor}15` }]}>
              <Text style={[styles.changePasswordButtonText, { color: primaryColor }]}>
                {t("changePassword.changePassword")}
              </Text>
              <ChevronRight size={16} color={primaryColor} style={{ marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }} />
            </View>
          </TouchableOpacity>
        </View>
        
        {/* Logout Button */}
        <TouchableOpacity 
          style={[styles.logoutButton, { backgroundColor: '#ef444415' }]}
          onPress={handleLogout}
        >
          <LogOut size={20} color="#ef4444" />
          <Text style={[styles.logoutButtonText, { color: '#ef4444', marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
            {t("common.logout")}
          </Text>
        </TouchableOpacity>

        {/* Version */}
        <Text style={[styles.versionText, { color: secondaryTextColor, textAlign: 'center' }]}>
          {t("common.version")} {Constants.expoConfig?.version || "1.0.0"}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerRTL: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 32,
    fontWeight: "700",
    letterSpacing: -0.5,
  },
  profileCard: {
    marginHorizontal: 24,
    borderRadius: 20,
    padding: 24,
    alignItems: "center",
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileImageContainer: {
    position: "relative",
    marginBottom: 20,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  uploadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholderText: {
    fontSize: 36,
    fontWeight: "700",
    color: "#fff",
  },
  cameraButton: {
    position: "absolute",
    bottom: 2,
    right: 2,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  profileInfo: {
    alignItems: "center",
  },
  userName: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 8,
  },
  nameInput: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 8,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    minWidth: 200,
  },
  roleBadge: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
  },
  roleText: {
    fontSize: 14,
    fontWeight: "600",
  },
  userEmail: {
    fontSize: 16,
  },
  infoCard: {
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionHeaderRTL: {
    flexDirection: "row-reverse",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  editButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  editActions: {
    flexDirection: "row",
    gap: 8,
  },
  editActionsRTL: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  infoRowRTL: {
    flexDirection: 'row-reverse',
  },
  bioRow: {
    alignItems: 'flex-start',
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoRowLeftRTL: {
    flexDirection: 'row-reverse',
  },
  infoLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  infoInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  bioInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
    height: 80,
  },
  settingsCard: {
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  settingItemRTL: {
    flexDirection: "row-reverse",
  },
  changePasswordItem: {
    paddingVertical: 16,
    borderRadius: 12,
    marginVertical: 4,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingLeftRTL: {
    flexDirection: "row-reverse",
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  languageButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  languageButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  changePasswordIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  changePasswordContent: {
    flex: 1,
  },
  changePasswordTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  changePasswordSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  changePasswordButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
  },
  changePasswordButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  logoutButton: {
    marginHorizontal: 24,
    borderRadius: 16,
    padding: 18,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  versionText: {
    fontSize: 14,
    marginBottom: 32,
    fontWeight: '500',
  },
});