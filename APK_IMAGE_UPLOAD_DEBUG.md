# APK Image Upload Debug Guide

## Issue: Image Upload Works in Expo Tunnel but Fails in APK

This is a common issue when moving from development (Expo tunnel) to production (APK) builds. Here are the key differences and solutions:

## Root Causes & Solutions

### 1. **Authentication State Issues** ✅ FIXED
**Problem**: Firebase auth state may not persist correctly in production builds
**Solution**: Added authentication validation before uploads
- Checks if user is authenticated
- <PERSON><PERSON>tes auth token before upload
- Forces token refresh if needed

### 2. **Blob Conversion Differences** ✅ FIXED
**Problem**: Different blob conversion behavior between development and production
**Solution**: Enhanced blob conversion with fallback methods
- Primary: Standard fetch() method
- Fallback: XMLHttpRequest for better production compatibility
- Platform-specific handling for web vs mobile

### 3. **Network Request Handling** ✅ FIXED
**Problem**: Network requests may behave differently in production builds
**Solution**: Added retry logic and better error handling
- 3 retry attempts with exponential backoff
- Specific error messages for common issues
- Network connectivity validation

### 4. **Firebase Storage Rules** ⚠️ CHECK REQUIRED
**Problem**: Storage rules may block uploads in production
**Current Rules Should Be**:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 5. **Platform-Specific Differences**
**Development (Expo Tunnel)**:
- Uses Expo's development server
- More permissive network handling
- Better error reporting
- Direct file system access

**Production (APK)**:
- Standalone app with different runtime
- Stricter security policies
- Limited error reporting
- Different file URI handling

## Debugging Steps

### 1. **Check Console Logs**
Enable remote debugging for APK:
```bash
adb logcat | grep -i "ReactNativeJS"
```

Look for these log messages:
- ✅ `"User authentication verified for storage operation"`
- ✅ `"Platform: android"`
- ✅ `"Mobile platform detected, using production-safe blob conversion"`
- ✅ `"Upload attempt 1/3"`
- ❌ `"No authenticated user found for storage operation"`
- ❌ `"User token validation failed"`
- ❌ `"Blob is empty or invalid"`

### 2. **Test Authentication**
```javascript
// Add this to test auth state in production
import { auth } from '@/services/firebase-config';

const testAuth = async () => {
  const user = auth.currentUser;
  console.log('Current user:', user?.uid);
  if (user) {
    try {
      const token = await user.getIdToken();
      console.log('Token obtained successfully');
    } catch (error) {
      console.error('Token error:', error);
    }
  }
};
```

### 3. **Test Storage Connectivity**
```javascript
import { testStorageConnectivity } from '@/services/storage-service';

// Test in production
testStorageConnectivity().then(success => {
  console.log('Storage connectivity:', success);
});
```

### 4. **Check Firebase Console**
- Go to Firebase Console > Storage
- Check if any files are being created during failed uploads
- Review Storage rules
- Check usage quotas

## Common APK-Specific Issues

### 1. **Network Security Config**
Android APKs may have stricter network policies. Ensure your app allows HTTPS connections to Firebase.

### 2. **File URI Permissions**
Production builds may have different file access permissions. The enhanced blob conversion handles this.

### 3. **Memory Constraints**
APKs may have stricter memory limits. Large images might fail to convert to blobs.

### 4. **Background Processing**
Some Android devices may kill background processes, interrupting uploads.

## Testing Checklist

### Before Release:
- [ ] Test image upload on physical device with APK
- [ ] Test with different image sizes (small, medium, large)
- [ ] Test with images from camera vs gallery
- [ ] Test with poor network conditions
- [ ] Test after app backgrounding/foregrounding
- [ ] Verify Firebase Storage rules allow authenticated uploads
- [ ] Check Firebase project quotas and limits

### If Upload Still Fails:
1. **Check Authentication**: User must be logged in
2. **Check Network**: Device must have internet connectivity
3. **Check Firebase Rules**: Storage rules must allow authenticated writes
4. **Check Image Size**: Very large images may timeout
5. **Check Device Storage**: Device must have sufficient storage
6. **Check Firebase Quotas**: Project must not exceed storage limits

## Enhanced Error Messages

The updated storage service now provides specific error messages:
- `"Authentication failed. Please log out and log back in."`
- `"Network error. Please check your internet connection and try again."`
- `"Storage access denied. Please check your permissions."`
- `"Storage quota exceeded. Please contact support."`

## Production Build Considerations

### 1. **Build Configuration**
Ensure your `eas.json` and `app.config.js` are properly configured for production builds.

### 2. **Firebase Configuration**
Verify that the Firebase configuration in production matches your project settings.

### 3. **Permissions**
Ensure the APK has necessary permissions for camera, storage, and network access.

### 4. **Testing Environment**
Test on multiple devices and Android versions to ensure compatibility.

## Next Steps

1. **Deploy the updated storage service** with enhanced authentication and blob conversion
2. **Test on APK build** with the new error handling and retry logic
3. **Monitor console logs** for specific error messages
4. **Verify Firebase Storage rules** allow authenticated uploads
5. **Test with different image sizes and sources**

The enhanced storage service should resolve most APK-specific image upload issues by addressing authentication, blob conversion, and network handling differences between development and production environments.
