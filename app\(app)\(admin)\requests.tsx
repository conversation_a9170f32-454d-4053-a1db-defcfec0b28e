import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, ActivityIndicator, Al<PERSON>, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useFocusEffect } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { RequestCard } from "@/components/RequestCard";
import { Request, getRequestsByType, deleteRequest } from "@/services/request-service";
import { Search, Plus, ClipboardList, Filter } from "lucide-react-native";
import { FilterButton } from "@/components/FilterButton";

export default function AdminRequestsScreen() {
  const { user } = useAuth();
  const { theme } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  
  const [requests, setRequests] = useState<Request[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<Request[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [activeTab, setActiveTab] = useState("inventory"); // "inventory", "machinery"
  
  const isDarkMode = theme === "dark";
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const primaryColor = colors.primary;
  
  const statusFilterButtons = [
    { key: "status-all", label: t("common.all"), value: "all", activeColor: primaryColor },
    { key: "status-pending", label: t("requests.pending"), value: "pending", activeColor: "#FF9800" },
    { key: "status-approved", label: t("requests.approved"), value: "approved", activeColor: "#4CAF50" },
    { key: "status-rejected", label: t("requests.rejected"), value: "rejected", activeColor: "#F44336" },
  ];
  
  useEffect(() => {
    if (selectedFarm) {
      loadRequests();
    }
  }, [selectedFarm, activeTab]);

  useEffect(() => {
    filterRequests();
  }, [searchQuery, statusFilter, requests]);

  // Auto-refresh when screen comes into focus (e.g., after creating a new request)
  useFocusEffect(
    React.useCallback(() => {
      if (selectedFarm) {
        loadRequests();
      }
    }, [selectedFarm, activeTab])
  );
  
  const loadRequests = async () => {
    try {
      setLoading(true);
      if (!selectedFarm) {
        setRequests([]);
        return;
      }
      
      const farmId = selectedFarm.id;
      
      let fetchedRequests: Request[] = [];
      
      if (activeTab === "inventory") {
        // Get inventory requests from caretakers to admin AND admin to owner
        const caretakerInventoryRequests = await getRequestsByType(farmId, "inventory", "caretaker");
        const caretakerExistingRequests = await getRequestsByType(farmId, "existing", "caretaker");
        const adminInventoryRequests = await getRequestsByType(farmId, "inventory", "admin");
        const adminExistingRequests = await getRequestsByType(farmId, "existing", "admin");
        
        fetchedRequests = [
          ...caretakerInventoryRequests,
          ...caretakerExistingRequests,
          ...adminInventoryRequests,
          ...adminExistingRequests
        ];
      } else if (activeTab === "machinery") {
        // Get machinery requests from caretakers to admin AND admin to owner
        const caretakerMachineryRequests = await getRequestsByType(farmId, "machinery", "caretaker");
        const adminMachineryRequests = await getRequestsByType(farmId, "machinery", "admin");
        
        fetchedRequests = [...caretakerMachineryRequests, ...adminMachineryRequests];
      }
      
      setRequests(fetchedRequests);
    } catch (error) {
      console.error("Error loading requests:", error);
      Alert.alert(t("common.error"), t("requests.loadError"));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const filterRequests = () => {
    let filtered = [...requests];
    
    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((request) => request.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (request) =>
          (request.itemName && request.itemName.toLowerCase().includes(query)) ||
          (request.machineryName && request.machineryName.toLowerCase().includes(query)) ||
          (request.category && request.category.toLowerCase().includes(query)) ||
          (request.reason && request.reason.toLowerCase().includes(query)) ||
          (request.requestedByName && request.requestedByName.toLowerCase().includes(query))
      );
    }
    
    setFilteredRequests(filtered);
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadRequests();
  };
  
  const handleRequestPress = (requestId: string) => {
    router.push(`/request/${requestId}?farmId=${selectedFarm?.id}`);
  };
  
  const handleCreateRequest = () => {
    if (activeTab === "machinery") {
      router.push("/(app)/request/create-machinery");
    } else {
      router.push("/(app)/request/create");
    }
  };
  
  const handleEditRequest = (request: any) => {
    if (request.requestType === "machinery") {
      // Admin users should use the admin machinery request screen
      if (user?.role === 'admin') {
        router.push(`/(app)/request/create-machinery?requestId=${request.id}&farmId=${selectedFarm?.id}`);
      } else {
        // Caretakers use the general machinery request screen
        router.push(`/(app)/machinery/request?requestId=${request.id}&farmId=${selectedFarm?.id}`);
      }
    } else {
      // Navigate to inventory edit screen
      router.push(`/request/edit?id=${request.id}&farmId=${selectedFarm?.id}`);
    }
  };
  
  const handleDeleteRequest = async (requestId: string) => {
    Alert.alert(
      t("common.confirm"),
      t("requests.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          onPress: async () => {
            try {
              if (!selectedFarm) {
                throw new Error("No farm selected");
              }
              
              await deleteRequest(requestId, selectedFarm.id);
              setRequests((prevRequests) => prevRequests.filter((request) => request.id !== requestId));
              Alert.alert(t("common.success"), t("requests.requestDeleted"));
            } catch (error) {
              console.error("Error deleting request:", error);
              Alert.alert(t("common.error"), t("requests.deleteError"));
            }
          },
          style: "destructive",
        },
      ]
    );
  };

  // Request type tabs at the top
  const renderRequestTypeTabs = () => {
    return (
      <View style={[styles.tabsContainer, { backgroundColor: cardColor, borderColor }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "inventory" && [styles.activeTabButton, { borderColor: "#4CAF50" }]
          ]}
          onPress={() => setActiveTab("inventory")}
        >
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === "inventory" ? "#4CAF50" : secondaryTextColor }
            ]}
          >
            Inventory Requests
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "machinery" && [styles.activeTabButton, { borderColor: "#2196F3" }]
          ]}
          onPress={() => setActiveTab("machinery")}
        >
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === "machinery" ? "#2196F3" : secondaryTextColor }
            ]}
          >
            Machinery Requests
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  const renderEmptyList = () => {
    if (loading) return null;
    
    if (!selectedFarm) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: textColor }]}>
            {t("common.noFarmSelected")}
          </Text>
          <Text style={[styles.emptySubtitle, { color: secondaryTextColor }]}>
            {t("common.farmIdRequired")}
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <ClipboardList size={64} color={secondaryTextColor} />
        <Text style={[styles.emptyTitle, { color: textColor }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all" 
            ? t("requests.noRequestsFound") 
            : activeTab === "machinery"
              ? "No machinery requests"
              : "No inventory requests"}
        </Text>
        <Text style={[styles.emptySubtitle, { color: secondaryTextColor }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all" 
            ? t("requests.tryDifferentSearch") 
            : activeTab === "machinery"
              ? "Machinery requests from caretakers and to owner will appear here"
              : "Inventory requests from caretakers and to owner will appear here"}
        </Text>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("requests.title"),
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#FF9800",
          },
          headerTintColor: "#fff",
          headerRight: () => (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleCreateRequest}
            >
              <Plus size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <View style={[styles.searchContainer, { backgroundColor: cardColor, borderColor }]}>
        <Search size={20} color={secondaryTextColor} />
        <TextInput
          style={[
            styles.searchInput, 
            { color: textColor, textAlign: isRTL ? 'right' : 'left' }
          ]}
          placeholder={t("requests.searchRequests")}
          placeholderTextColor={secondaryTextColor}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Request Type Tabs */}
      {renderRequestTypeTabs()}
      
      <View style={[styles.filtersContainer, isRTL && styles.rtlFiltersContainer]}>
        <Text style={[styles.filtersLabel, { color: secondaryTextColor }]}>
          <Filter size={14} color={secondaryTextColor} /> {t("common.filter")}:
        </Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {statusFilterButtons.map((button) => (
            <FilterButton
              key={button.key}
              label={button.label}
              active={statusFilter === button.value}
              onPress={() => setStatusFilter(button.value)}
              activeColor={button.activeColor}
              isDarkMode={isDarkMode}
            />
          ))}
        </ScrollView>
      </View>
      
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      ) : (
        <FlatList
          data={filteredRequests}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <RequestCard
              request={item}
              onPress={() => handleRequestPress(item.id)}
              onEditPress={
                item.status === "pending" && item.requestedBy === user?.uid
                  ? () => handleEditRequest(item)
                  : undefined
              }
              onDeletePress={
                item.status === "pending" && (item.requestedBy === user?.uid || user?.role === "admin")
                  ? () => handleDeleteRequest(item.id)
                  : undefined
              }
              showEditOptions={true}
              isDarkMode={isDarkMode}
              role="admin"
              currentUserId={user?.uid}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      )}
      
      {/* Floating Action Button */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: primaryColor }]}
        onPress={handleCreateRequest}
      >
        <Plus size={24} color="#fff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    zIndex: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  tabsContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
    overflow: "hidden",
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  filtersContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  rtlFiltersContainer: {
    flexDirection: "row-reverse",
  },
  filtersLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  filtersScrollContent: {
    paddingRight: 16,
    flexDirection: "row",
    gap: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1000,
  },
});