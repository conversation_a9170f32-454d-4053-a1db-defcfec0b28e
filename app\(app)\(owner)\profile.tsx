import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, Switch, TextInput, ScrollView, ActivityIndicator, Alert, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import { router, Stack } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { updateUserProfile } from "@/services/user-service";
import { uploadUserProfileImage, deleteImageFromStorage } from "@/services/storage-service";
import { getInventoryItems } from "@/services/inventory-service";
import { getMachineryByFarm } from "@/services/machinery-service";
import { getInventoryRequests } from "@/services/request-service";
import { Moon, Sun, User, Mail, Phone, FileText, LogOut, Camera, Globe, Edit3, Calendar, Lock, Shield, ChevronRight, Package, Truck, ClipboardList, MapPin } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import Constants from "expo-constants";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Helper functions for responsive design
const getResponsivePadding = () => {
  if (screenWidth < 380) return 16;
  if (screenWidth < 420) return 20;
  return 24;
};

const getResponsiveFontSize = (baseSize: number) => {
  if (screenWidth < 380) return baseSize - 2;
  if (screenWidth < 420) return baseSize - 1;
  return baseSize;
};

const getResponsiveImageSize = () => {
  if (screenWidth < 380) return 80;
  if (screenWidth < 420) return 90;
  return 100;
};

const getResponsiveMargin = () => {
  if (screenWidth < 380) return 12;
  if (screenWidth < 420) return 16;
  return 24;
};

const getResponsiveIconSize = () => {
  if (screenWidth < 380) return 16;
  if (screenWidth < 420) return 18;
  return 20;
};

export default function ProfileScreen() {
  const { user, signOut, refreshUserData } = useAuth();
  const { selectedFarm } = useFarm();
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage, isRTL, t } = useLanguage();
  const isDarkMode = theme === "dark";

  const [uploading, setUploading] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);


  const [stats, setStats] = useState({
    totalFarms: 0,
    totalItems: 0,
    totalMachinery: 0,
    pendingRequests: 0,
    memberSince: '',
    lastLogin: '',
  });

  const backgroundColor = isDarkMode ? "#0f0f0f" : "#f8fafc";
  const cardColor = isDarkMode ? "#1a1a1a" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#1e293b";
  const secondaryTextColor = isDarkMode ? "#94a3b8" : "#64748b";
  const borderColor = isDarkMode ? "#374151" : "#e2e8f0";
  const primaryColor = "#2E7D32";

  // Debug user photo URL
  console.log("User photoURL:", user?.photoURL);
  console.log("User data:", {
    displayName: user?.displayName,
    photoURL: user?.photoURL,
    email: user?.email
  });

  useEffect(() => {
    loadProfileStats();
  }, [user, selectedFarm]);



  const loadProfileStats = async () => {
    if (!user) return;

    try {
      let totalItems = 0;
      let totalMachinery = 0;
      let pendingRequests = 0;

      if (selectedFarm) {
        // Get inventory items for selected farm
        const items = await getInventoryItems(selectedFarm.id);
        totalItems = items.length;

        // Get machinery for selected farm
        const machinery = await getMachineryByFarm(selectedFarm.id);
        totalMachinery = machinery.length;

        // Get pending requests for this user
        const pending = await getInventoryRequests(selectedFarm.id, user.uid, 'pending');
        pendingRequests = pending.length;
      }

      // Calculate member since date using createdAt field
      const memberSince = user.createdAt
        ? (typeof user.createdAt === 'string' ? new Date(user.createdAt).toLocaleDateString() : new Date().toLocaleDateString())
        : new Date().toLocaleDateString();

      // Calculate last login using available date field
      const lastLogin = (user as any).lastLogin
        ? (typeof (user as any).lastLogin === 'string' ? new Date((user as any).lastLogin).toLocaleDateString() : new Date().toLocaleDateString())
        : new Date().toLocaleDateString();

      setStats({
        totalFarms: 1, // Owner typically has 1 main farm
        totalItems,
        totalMachinery,
        pendingRequests,
        memberSince,
        lastLogin,
      });
    } catch (error) {
      console.error('Error loading profile stats:', error);
    }
  };


  
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setUploading(true);
        const imageUri = result.assets[0].uri;
        console.log("Selected image URI:", imageUri);

        try {
          // Delete old image from Storage if it exists
          if (user?.photoURL) {
            await deleteImageFromStorage(user.photoURL);
          }

          // Upload new image to Firebase Storage
          const downloadURL = await uploadUserProfileImage(imageUri, user?.uid || "");
          console.log("Image uploaded to Storage:", downloadURL);

          // Update user profile with the Firebase Storage download URL
          await updateUserProfile(user?.uid || "", {
            photoURL: downloadURL,
          });

          // Refresh user data to get the updated photoURL
          await refreshUserData();
          console.log("Profile image updated successfully");
        } catch (error) {
          console.error("Error updating profile image:", error);
          Alert.alert("Error", "Failed to upload image. Please try again.");
        } finally {
          setUploading(false);
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
      setUploading(false);
    }
  };
  
  const handleLogout = () => {
    performLogout();
  };

  const performLogout = async () => {
    console.log("Owner logout initiated");
    setLogoutLoading(true);
    
    try {
      await signOut();
      console.log("Owner logout completed successfully");
    } catch (error) {
      console.error("Error signing out owner:", error);
    } finally {
      // Reset loading state after a delay to prevent UI flicker
      setTimeout(() => {
        setLogoutLoading(false);
      }, 1000);
    }
  };

  const handleLanguageToggle = async () => {
    const newLanguage = language === 'en' ? 'ur' : 'en';
    await setLanguage(newLanguage);
  };

  const handleChangePassword = () => {
    router.push("/(app)/change-password");
  };


  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{
          title: t("profile.title"),
          headerStyle: {
            backgroundColor: primaryColor,
          },
          headerTintColor: "#fff",
        }} 
      />
      
      <ScrollView style={[styles.scrollView, { direction: isRTL ? 'rtl' : 'ltr' }]} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={[styles.header, isRTL && styles.headerRTL]}>
          <Text style={[styles.title, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("profile.title")}
          </Text>
        </View>

        {/* Profile Card */}
        <View style={[styles.profileCard, { backgroundColor: cardColor }]}>
          <View style={styles.profileImageContainer}>
            {uploading ? (
              <View style={[styles.profileImage, styles.uploadingContainer, { backgroundColor: `${primaryColor}20` }]}>
                <ActivityIndicator color={primaryColor} size="large" />
              </View>
            ) : (
              <>
                {user?.photoURL && user.photoURL.trim() !== "" ? (
                  <Image
                    source={{ uri: user.photoURL }}
                    style={styles.profileImage}
                    contentFit="cover"
                    transition={200}
                    onError={(error) => {
                      console.log("Profile image load error:", error);
                    }}
                    onLoad={() => {
                      console.log("Profile image loaded successfully");
                    }}
                  />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: primaryColor }]}>
                    <Text style={styles.profileImagePlaceholderText}>
                      {user?.displayName ? user.displayName.charAt(0).toUpperCase() : "U"}
                    </Text>
                  </View>
                )}
                

              </>
            )}
          </View>
          
          <View style={styles.profileInfo}>
            <Text style={[styles.userName, { color: textColor, textAlign: 'center' }]}>
              {user?.displayName || t("common.unknown")}
            </Text>
            
            <View style={[styles.roleBadge, { backgroundColor: `${primaryColor}15` }]}>
              <Text style={[styles.roleText, { color: primaryColor }]}>
                {t(`profile.${user?.role}`)}
              </Text>
            </View>
            
            <Text style={[styles.userEmail, { color: secondaryTextColor, textAlign: 'center' }]}>
              {user?.email}
            </Text>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#10b98115' }]}>
              <Package size={getResponsiveIconSize()} color="#10b981" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.totalItems}</Text>
            <Text
              style={[styles.statLabel, { color: secondaryTextColor }]}
              numberOfLines={2}
              adjustsFontSizeToFit={screenWidth < 380}
              minimumFontScale={0.8}
            >
              {t("dashboard.totalItems")}
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#3b82f615' }]}>
              <Truck size={getResponsiveIconSize()} color="#3b82f6" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.totalMachinery}</Text>
            <Text
              style={[styles.statLabel, { color: secondaryTextColor }]}
              numberOfLines={2}
              adjustsFontSizeToFit={screenWidth < 380}
              minimumFontScale={0.8}
            >
              {t("machinery.title")}
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: cardColor }]}>
            <View style={[styles.statIcon, { backgroundColor: '#f59e0b15' }]}>
              <ClipboardList size={getResponsiveIconSize()} color="#f59e0b" />
            </View>
            <Text style={[styles.statNumber, { color: textColor }]}>{stats.pendingRequests}</Text>
            <Text
              style={[styles.statLabel, { color: secondaryTextColor }]}
              numberOfLines={2}
              adjustsFontSizeToFit={screenWidth < 380}
              minimumFontScale={0.8}
            >
              {t("common.pending")}
            </Text>
          </View>
        </View>

        {/* Personal Information */}
        <View style={[styles.infoCard, { backgroundColor: cardColor }]}>
          <View style={[styles.sectionHeader, isRTL && styles.sectionHeaderRTL]}>
            <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
              {t("profile.personalInformation")}
            </Text>

            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: `${primaryColor}15` }]}
              onPress={() => router.push('/(app)/profile-details')}
            >
              <ChevronRight size={16} color={primaryColor} />
              <Text style={[styles.editButtonText, { color: primaryColor, marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }]}>
                {t("settings.viewDetails")}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Name */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <User size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.name")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {user?.displayName || t("profile.notSet")}
            </Text>
          </View>

          {/* Phone */}
          <View style={[styles.infoRow, isRTL && styles.infoRowRTL]}>
            <View style={[styles.infoRowLeft, isRTL && styles.infoRowLeftRTL]}>
              <Phone size={18} color={secondaryTextColor} />
              <Text style={[styles.infoLabel, { color: secondaryTextColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("profile.phone")}
              </Text>
            </View>
            <Text style={[styles.infoValue, { color: textColor, textAlign: isRTL ? 'left' : 'right' }]}>
              {user?.phone || t("profile.notSet")}
            </Text>
          </View>
        </View>
        
        {/* Settings */}
        <View style={[styles.settingsCard, { backgroundColor: cardColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}>
            {t("settings.title")}
          </Text>
          
          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              {isDarkMode ? (
                <Moon size={20} color={secondaryTextColor} />
              ) : (
                <Sun size={20} color={secondaryTextColor} />
              )}
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {isDarkMode ? t("profile.darkMode") : t("profile.lightMode")}
              </Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: "#e2e8f0", true: `${primaryColor}40` }}
              thumbColor={isDarkMode ? primaryColor : "#ffffff"}
            />
          </View>

          <View style={[styles.settingItem, isRTL && styles.settingItemRTL]}>
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <Globe size={20} color={secondaryTextColor} />
              <Text style={[styles.settingLabel, { color: textColor, marginLeft: isRTL ? 0 : 12, marginRight: isRTL ? 12 : 0 }]}>
                {t("common.language")}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.languageButton, { backgroundColor: `${primaryColor}15` }]}
              onPress={handleLanguageToggle}
            >
              <Text style={[styles.languageButtonText, { color: primaryColor }]}>
                {language === 'en' ? t("profile.urdu") : t("profile.english")}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.settingItem, styles.changePasswordItem, isRTL && styles.settingItemRTL]}
            onPress={handleChangePassword}
          >
            <View style={[styles.settingLeft, isRTL && styles.settingLeftRTL]}>
              <View style={[styles.changePasswordIcon, { backgroundColor: `${primaryColor}15` }]}>
                <Shield size={screenWidth < 380 ? 16 : 18} color={primaryColor} />
              </View>
              <View style={styles.changePasswordContent}>
                <Text
                  style={[styles.changePasswordTitle, { color: textColor }]}
                  numberOfLines={1}
                  adjustsFontSizeToFit={screenWidth < 380}
                  minimumFontScale={0.8}
                >
                  {t("changePassword.title")}
                </Text>
              </View>
            </View>
            <View style={[styles.changePasswordButton, { backgroundColor: `${primaryColor}15` }]}>
              <Text
                style={[styles.changePasswordButtonText, { color: primaryColor }]}
                numberOfLines={1}
                adjustsFontSizeToFit={screenWidth < 380}
                minimumFontScale={0.7}
              >
                {t("changePassword.changePassword")}
              </Text>
              <ChevronRight
                size={screenWidth < 380 ? 14 : 16}
                color={primaryColor}
                style={{ marginLeft: isRTL ? 0 : 4, marginRight: isRTL ? 4 : 0 }}
              />
            </View>
          </TouchableOpacity>
        </View>
        
        {/* Logout Button */}
        <TouchableOpacity 
          style={[styles.logoutButton, { backgroundColor: '#ef444415' }]}
          onPress={handleLogout}
          disabled={logoutLoading}
        >
          {logoutLoading ? (
            <ActivityIndicator color="#ef4444" />
          ) : (
            <>
              <LogOut size={20} color="#ef4444" />
              <Text style={[styles.logoutButtonText, { color: '#ef4444', marginLeft: isRTL ? 0 : 8, marginRight: isRTL ? 8 : 0 }]}>
                {t("common.logout")}
              </Text>
            </>
          )}
        </TouchableOpacity>

        {/* Version */}
        <Text style={[styles.versionText, { color: secondaryTextColor, textAlign: 'center' }]}>
          {t("common.version")} {Constants.expoConfig?.version || "1.0.0"}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: getResponsivePadding(),
    paddingTop: screenWidth < 380 ? 16 : 20,
    paddingBottom: screenWidth < 380 ? 12 : 16,
  },
  headerRTL: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: getResponsiveFontSize(32),
    fontWeight: "700",
    letterSpacing: -0.5,
  },
  profileCard: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 16 : 20,
    padding: getResponsivePadding(),
    alignItems: "center",
    marginBottom: getResponsiveMargin(),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileImageContainer: {
    position: "relative",
    marginBottom: screenWidth < 380 ? 16 : 20,
  },
  profileImage: {
    width: getResponsiveImageSize(),
    height: getResponsiveImageSize(),
    borderRadius: getResponsiveImageSize() / 2,
  },
  uploadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholder: {
    width: getResponsiveImageSize(),
    height: getResponsiveImageSize(),
    borderRadius: getResponsiveImageSize() / 2,
    justifyContent: "center",
    alignItems: "center",
  },
  profileImagePlaceholderText: {
    fontSize: getResponsiveFontSize(36),
    fontWeight: "700",
    color: "#fff",
  },
  cameraButton: {
    position: "absolute",
    bottom: 2,
    right: 2,
    width: screenWidth < 380 ? 28 : 32,
    height: screenWidth < 380 ? 28 : 32,
    borderRadius: screenWidth < 380 ? 14 : 16,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  profileInfo: {
    alignItems: "center",
  },
  userName: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: "700",
    marginBottom: 8,
  },
  nameInput: {
    fontSize: getResponsiveFontSize(24),
    fontWeight: "700",
    marginBottom: 8,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    minWidth: screenWidth < 380 ? 150 : 200,
  },
  roleBadge: {
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 8,
  },
  roleText: {
    fontSize: getResponsiveFontSize(14),
    fontWeight: "600",
  },
  userEmail: {
    fontSize: getResponsiveFontSize(16),
  },
  infoCard: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: getResponsivePadding(),
    marginBottom: screenWidth < 380 ? 12 : 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: screenWidth < 380 ? "column" : "row",
    justifyContent: "space-between",
    alignItems: screenWidth < 380 ? "flex-start" : "center",
    marginBottom: screenWidth < 380 ? 16 : 20,
    gap: screenWidth < 380 ? 8 : 0,
  },
  sectionHeaderRTL: {
    flexDirection: "row-reverse",
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: '700',
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: screenWidth < 380 ? 8 : 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  editButtonText: {
    fontWeight: "600",
    fontSize: getResponsiveFontSize(14),
  },
  editActions: {
    flexDirection: "row",
    gap: 8,
  },
  editActionsRTL: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  infoRowRTL: {
    flexDirection: 'row-reverse',
  },
  bioRow: {
    alignItems: 'flex-start',
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoRowLeftRTL: {
    flexDirection: 'row-reverse',
  },
  infoLabel: {
    fontSize: getResponsiveFontSize(15),
    fontWeight: '500',
    minWidth: screenWidth < 380 ? 80 : 100,
  },
  infoValue: {
    fontSize: getResponsiveFontSize(15),
    fontWeight: '600',
    flex: 1,
    textAlign: screenWidth < 380 ? 'left' : 'right',
  },
  infoInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  bioInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    fontSize: 15,
    fontWeight: '600',
    height: 80,
  },
  settingsCard: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: getResponsivePadding(),
    marginBottom: getResponsiveMargin(),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  settingItemRTL: {
    flexDirection: "row-reverse",
  },
  changePasswordItem: {
    paddingVertical: screenWidth < 380 ? 14 : 16,
    paddingHorizontal: screenWidth < 380 ? 12 : 16,
    borderRadius: 12,
    marginVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0, // Allow text to shrink
  },
  settingLeftRTL: {
    flexDirection: "row-reverse",
  },
  settingLabel: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: '500',
  },
  languageButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  languageButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
  changePasswordIcon: {
    width: screenWidth < 380 ? 36 : 40,
    height: screenWidth < 380 ? 36 : 40,
    borderRadius: screenWidth < 380 ? 18 : 20,
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
  },
  changePasswordContent: {
    flex: 1,
    marginHorizontal: screenWidth < 380 ? 10 : 12,
    minWidth: 0, // Allow text to shrink
  },
  changePasswordTitle: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  changePasswordSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  changePasswordButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: screenWidth < 380 ? 8 : 12,
    paddingVertical: screenWidth < 380 ? 6 : 8,
    borderRadius: 8,
    flexShrink: 0,
    maxWidth: screenWidth < 380 ? 100 : 140,
  },
  changePasswordButtonText: {
    fontWeight: "600",
    fontSize: screenWidth < 380 ? 12 : 14,
    flexShrink: 1,
  },
  logoutButton: {
    marginHorizontal: getResponsiveMargin(),
    borderRadius: screenWidth < 380 ? 12 : 16,
    padding: screenWidth < 380 ? 16 : 18,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: screenWidth < 380 ? 12 : 16,
  },
  logoutButtonText: {
    fontSize: getResponsiveFontSize(16),
    fontWeight: "600",
  },
  versionText: {
    fontSize: 14,
    marginBottom: 32,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: screenWidth < 380 ? 8 : screenWidth < 420 ? 12 : 16,
    marginBottom: getResponsiveMargin(),
    gap: screenWidth < 380 ? 4 : screenWidth < 420 ? 6 : 8,
  },
  statCard: {
    // Always show 3 cards per row for all screen sizes with responsive width
    flex: 1,
    maxWidth: screenWidth < 380 ? (screenWidth - 32) / 3 : (screenWidth - 48) / 3,
    alignItems: 'center',
    padding: screenWidth < 380 ? 8 : screenWidth < 420 ? 12 : 16,
    borderRadius: screenWidth < 380 ? 8 : screenWidth < 420 ? 10 : 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    // Ensure minimum width for very small screens
    minWidth: screenWidth < 380 ? 90 : 100,
  },
  statIcon: {
    width: screenWidth < 380 ? 28 : screenWidth < 420 ? 32 : 36,
    height: screenWidth < 380 ? 28 : screenWidth < 420 ? 32 : 36,
    borderRadius: screenWidth < 380 ? 14 : screenWidth < 420 ? 16 : 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: screenWidth < 380 ? 6 : screenWidth < 420 ? 8 : 10,
  },
  statNumber: {
    fontSize: screenWidth < 380 ? 14 : screenWidth < 420 ? 16 : 18,
    fontWeight: '700',
    marginBottom: screenWidth < 380 ? 2 : 4,
    textAlign: 'center',
    lineHeight: screenWidth < 380 ? 16 : screenWidth < 420 ? 18 : 20,
  },
  statLabel: {
    fontSize: screenWidth < 380 ? 9 : screenWidth < 420 ? 10 : 11,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: screenWidth < 380 ? 11 : screenWidth < 420 ? 12 : 14,
  },
});