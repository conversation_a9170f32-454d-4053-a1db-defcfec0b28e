import React, { useState, useEffect } from "react";
import { 
  StyleSheet, 
  Text, 
  View, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator, 
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useRouter, Stack, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useFarm } from "@/context/farm-context";
import { createRequest } from "@/services/request-service";
import { getInventoryItemById } from "@/services/inventory-service";
import { useTheme } from "@/context/theme-context";
import ModalDropdown from "@/components/ModalDropdown";
import { useLanguage } from "@/context/language-context";
import { Package, Tag, FileText, Building, ArrowRight, Info } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";

export default function CreateInventoryRequestScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const isOwner = user?.role === "owner";
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [itemName, setItemName] = useState("");
  const [category, setCategory] = useState("");
  const [quantity, setQuantity] = useState("");
  const [unit, setUnit] = useState("pieces");
  const [reason, setReason] = useState("");
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  
  // Redirect owners away from this page
  useEffect(() => {
    if (isOwner) {
      Alert.alert(
        t("common.notAvailable"),
        t("requests.ownersCannotCreateRequests"),
        [{ text: t("common.ok"), onPress: () => router.back() }]
      );
    }
  }, [isOwner, router]);
  
  // Load item data if itemId is provided
  useEffect(() => {
    const loadItemData = async () => {
      if (params.itemId) {
        setLoading(true);
        try {
          const item = await getInventoryItemById(params.itemId as string, selectedFarm?.id);
          if (item) {
            setItemName(item.name || "");
            setCategory(item.category || "");
            setUnit(item.unit || "pieces");
            setQuantity("1");
          }
        } catch (error) {
          console.error("Error loading item data:", error);
        } finally {
          setLoading(false);
        }
      }
    };
    
    loadItemData();
  }, [params.itemId, selectedFarm]);
  
  // Categories matching add inventory screen
  const CATEGORY_OPTIONS = [
    { key: 'inventory.category_seeds', stored: 'Seeds' },
    { key: 'inventory.category_fertilizers', stored: 'Fertilizers' },
    { key: 'inventory.category_pesticides', stored: 'Pesticides' },
    { key: 'inventory.category_tools', stored: 'Tools' },
    { key: 'inventory.category_equipment', stored: 'Equipment' },
    { key: 'inventory.category_feed', stored: 'Feed' },
    { key: 'inventory.category_medication', stored: 'Medication' },
    { key: 'inventory.category_vaccination', stored: 'Vaccination' },
    { key: 'inventory.category_fuel', stored: 'Fuel' },
    { key: 'inventory.category_other_custom', stored: 'Other (Custom)' },
  ];

  // Units matching add inventory screen
  const UNIT_OPTIONS = [
    { key: 'inventory.unit_kg', stored: 'kg' },
    { key: 'inventory.unit_g', stored: 'g' },
    { key: 'inventory.unit_l', stored: 'L' },
    { key: 'inventory.unit_ml', stored: 'mL' },
    { key: 'inventory.unit_units', stored: 'units' },
    { key: 'inventory.unit_bags', stored: 'bags' },
    { key: 'inventory.unit_boxes', stored: 'boxes' },
    { key: 'inventory.unit_bottles', stored: 'bottles' },
    { key: 'inventory.unit_packs', stored: 'packs' },
    { key: 'inventory.unit_pieces', stored: 'pieces' },
    { key: 'inventory.unit_other_custom', stored: 'Other (Custom)' },
  ];

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!itemName.trim()) {
      newErrors.itemName = t("inventory.itemNameRequired");
    }

    if (!quantity || parseInt(quantity) <= 0) {
      newErrors.quantity = t("inventory.validQuantityRequired");
    }

    if (!unit) {
      newErrors.unit = t("inventory.unitRequired");
    }

    if (!category) {
      newErrors.category = t("inventory.categoryRequired");
    }

    // Make reason mandatory for transfer requests
    if (params.itemId && !reason.trim()) {
      newErrors.reason = t("requests.reasonRequired");
    }

    if (!selectedFarm) {
      newErrors.farm = t("common.noFarmSelected");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (isOwner) {
      Alert.alert(t("common.notAvailable"), t("requests.ownersCannotCreateRequests"));
      return;
    }
    
    if (!validateForm()) {
      return;
    }
    
    setSubmitting(true);
    
    try {
      console.log("Creating inventory request for farm:", selectedFarm!.id);
      
      await createRequest({
        requestType: "inventory",
        itemName: itemName.trim(),
        quantity: parseInt(quantity),
        unit,
        farmId: selectedFarm!.id,
        farmName: selectedFarm!.name,
        requestedBy: user?.uid || "",
        requestedByName: user?.displayName || "",
        requestorRole: user?.role || "caretaker",
        reason: reason.trim(),
        category,
        notes: reason.trim(),
        status: "pending",
        createdAt: new Date().toISOString(),
        itemId: params.itemId as string || undefined,
      });
      
      Alert.alert(
        t("common.success"), 
        params.itemId ? t("requests.transferRequestCreated") : t("requests.requestCreated"),
        [
          { 
            text: t("common.ok"), 
            onPress: () => {
              if (user?.role === "caretaker") {
                router.replace("/(app)/(caretaker)/requests");
              } else if (user?.role === "admin") {
                router.replace("/(app)/(admin)/requests");
              } else {
                router.back();
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error("Error creating inventory request:", error);
      Alert.alert(t("common.error"), `${t("requests.createError")}: ${error instanceof Error ? error.message : t("common.unknownError")}`);
    } finally {
      setSubmitting(false);
    }
  };

  // If user is owner, don't render the form
  if (isOwner) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
        <Stack.Screen options={{ 
          title: params.itemId ? t("requests.requestTransfer") : t("requests.requestNewInventoryItem"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }} />
        
        <View style={styles.emptyContainer}>
          <Package size={64} color={colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {t("requests.ownersCannotCreateRequests")}
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
            {t("requests.ownersCanOnlyApproveReject")}
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>{t("common.back")}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
        <Stack.Screen options={{ 
          title: params.itemId ? t("requests.requestTransfer") : t("requests.requestNewInventoryItem"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }} />
        
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top']}>
      <Stack.Screen options={{ 
        title: params.itemId ? t("requests.requestTransfer") : t("requests.requestNewInventoryItem"),
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: "#fff",
      }} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.contentContainer}>
            {/* Header Banner */}
            <LinearGradient
              colors={[colors.primary + '20', colors.primary + '10']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.headerBanner}
            >
              <View style={styles.headerContent}>
                <Package size={32} color={colors.primary} />
                <View style={styles.headerText}>
                  <Text style={[styles.headerTitle, { color: colors.text }]}>
                    {params.itemId ? t("requests.requestTransfer") : t("requests.requestNewInventoryItem")}
                  </Text>
                  <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
                    {params.itemId 
                      ? t("requests.submitTransferRequestDescription") 
                      : t("requests.submitRequestDescription")}
                  </Text>
                </View>
              </View>
            </LinearGradient>
            
            {/* Form Container */}
            <View style={[styles.formContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
              {/* Item Information Section */}
              <View style={styles.formSection}>
                <View style={styles.sectionHeader}>
                  <Tag size={20} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {t("inventory.itemInformation")}
                  </Text>
                </View>
                
                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("inventory.itemName")} *
                  </Text>
                  <TextInput
                    style={[
                      styles.input, 
                      { 
                        backgroundColor: colors.background,
                        borderColor: errors.itemName ? colors.error : colors.border,
                        color: colors.text,
                      }
                    ]}
                    value={itemName}
                    onChangeText={(text) => {
                      setItemName(text);
                      if (errors.itemName) {
                        setErrors(prev => ({ ...prev, itemName: "" }));
                      }
                    }}
                    placeholder={t("inventory.enterItemName")}
                    placeholderTextColor={colors.textSecondary}
                    editable={!params.itemId}
                  />
                  {errors.itemName && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.itemName}
                    </Text>
                  )}
                </View>
                
                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {t("common.category")} *
                  </Text>
                  <ModalDropdown
                    items={CATEGORY_OPTIONS.map(option => {
                      const translatedLabel = t(option.key);
                      return {
                        label: translatedLabel,
                        value: option.stored,
                      };
                    })}
                    selectedValue={category}
                    onValueChange={(value) => {
                      setCategory(value);
                      if (errors.category) {
                        setErrors(prev => ({ ...prev, category: "" }));
                      }
                    }}
                    placeholder={t("inventory.selectCategory")}
                    error={!!errors.category}
                    disabled={!!params.itemId}
                  />
                  {errors.category && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.category}
                    </Text>
                  )}
                </View>
              </View>
              
              {/* Quantity Details Section */}
              <View style={styles.formSection}>
                <View style={styles.sectionHeader}>
                  <FileText size={20} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {t("inventory.quantityDetails")}
                  </Text>
                </View>
                
                <View style={styles.row}>
                  <View style={[styles.formGroup, { flex: 2, marginRight: 12 }]}>
                    <Text style={[styles.label, { color: colors.text }]}>
                      {t("common.quantity")} *
                    </Text>
                    <TextInput
                      style={[
                        styles.input, 
                        { 
                          backgroundColor: colors.background,
                          borderColor: errors.quantity ? colors.error : colors.border,
                          color: colors.text,
                        }
                      ]}
                      value={quantity}
                      onChangeText={(text) => {
                        setQuantity(text);
                        if (errors.quantity) {
                          setErrors(prev => ({ ...prev, quantity: "" }));
                        }
                      }}
                      keyboardType="numeric"
                      placeholder={t("inventory.enterQuantity")}
                      placeholderTextColor={colors.textSecondary}
                    />
                    {errors.quantity && (
                      <Text style={[styles.errorText, { color: colors.error }]}>
                        {errors.quantity}
                      </Text>
                    )}
                  </View>
                  
                  <View style={[styles.formGroup, { flex: 1 }]}>
                    <Text style={[styles.label, { color: colors.text }]}>
                      {t("common.unit")}
                    </Text>
                    <ModalDropdown
                      items={UNIT_OPTIONS.map(option => {
                        const translatedLabel = t(option.key);
                        return {
                          label: translatedLabel,
                          value: option.stored,
                        };
                      })}
                      selectedValue={unit}
                      onValueChange={setUnit}
                      placeholder={t("inventory.selectUnit")}
                      disabled={!!params.itemId}
                    />
                  </View>
                </View>
              </View>
              
              {/* Request Details Section */}
              <View style={styles.formSection}>
                <View style={styles.sectionHeader}>
                  <Building size={20} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {t("requests.requestDetails")}
                  </Text>
                </View>
                
                <View style={styles.formGroup}>
                  <Text style={[styles.label, { color: colors.text }]}>
                    {params.itemId ? t("requests.reasonForTransfer") : t("requests.reasonForRequest")}
                    {params.itemId && " *"}
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      styles.textArea,
                      {
                        backgroundColor: colors.background,
                        borderColor: errors.reason ? colors.error : colors.border,
                        color: colors.text,
                      }
                    ]}
                    value={reason}
                    onChangeText={(text) => {
                      setReason(text);
                      if (errors.reason) {
                        setErrors(prev => ({ ...prev, reason: "" }));
                      }
                    }}
                    placeholder={params.itemId ? t("requests.explainWhyTransferNeeded") : t("requests.explainWhyNeeded")}
                    placeholderTextColor={colors.textSecondary}
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                  />
                  {errors.reason && (
                    <Text style={[styles.errorText, { color: colors.error }]}>
                      {errors.reason}
                    </Text>
                  )}
                </View>
                
                <View style={[styles.farmInfo, { backgroundColor: colors.primary + '10' }]}>
                  <Building size={16} color={colors.primary} />
                  <ArrowRight size={16} color={colors.textSecondary} style={{ marginHorizontal: 8 }} />
                  <Text style={[styles.farmInfoText, { color: colors.text }]}>
                    {t("requests.requestFor")}: {selectedFarm?.name || t("common.unknownFarm")}
                  </Text>
                </View>
              </View>
              
              {/* Info Note */}
              <View style={[styles.infoNote, { backgroundColor: colors.info + '15' }]}>
                <Info size={20} color={colors.info} />
                <Text style={[styles.infoText, { color: colors.text }]}>
                  {params.itemId 
                    ? t("requests.transferRequestNote")
                    : t("requests.requestNote")}
                </Text>
              </View>
              
              {/* Action Buttons */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity 
                  style={[
                    styles.cancelButton, 
                    { 
                      backgroundColor: colors.background,
                      borderColor: colors.border,
                    }
                  ]} 
                  onPress={() => router.back()}
                  disabled={submitting}
                  activeOpacity={0.8}
                >
                  <Text style={[styles.cancelButtonText, { color: colors.textSecondary }]}>
                    {t("common.cancel")}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.submitButton, 
                    { 
                      backgroundColor: colors.primary,
                      opacity: submitting ? 0.7 : 1 
                    }
                  ]} 
                  onPress={handleSubmit}
                  disabled={submitting}
                  activeOpacity={0.8}
                >
                  {submitting ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.submitButtonText}>
                      {params.itemId ? t("common.submit") : t("common.submit")}
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  headerBanner: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  formContainer: {
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  formSection: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 12,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
  },
  textArea: {
    minHeight: 120,
    textAlignVertical: "top",
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: "hidden",
  },
  picker: {
    height: 50,
    width: "100%",
  },
  row: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  farmInfo: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  farmInfoText: {
    fontSize: 14,
    fontWeight: "500",
  },
  infoNote: {
    flexDirection: "row",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: "flex-start",
  },
  infoText: {
    marginLeft: 12,
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  submitButton: {
    flex: 2,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 22,
  },
  backButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});