import { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, router } from "expo-router";
import { createFarm } from "@/services/farm-service";
import { uploadFarmImage } from "@/services/storage-service";
import { MapPin, Camera, X, Plus, ArrowLeft } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import * as ImagePicker from 'expo-image-picker';
import { updateUser } from "@/services/user-service";

const FARM_TYPES = [
  { value: "crop", label: "farm.cropFarm" },
  { value: "livestock", label: "farm.livestockFarm" },
  { value: "mixed", label: "farm.mixedFarm" },
  { value: "dairy", label: "farm.dairyFarm" },
  { value: "poultry", label: "farm.poultyFarm" },
  { value: "fish", label: "farm.fishFarm" },
  { value: "organic", label: "farm.organicFarm" },
];

const SIZE_UNITS = [
  { value: "acres", label: "farm.acres" },
  { value: "hectares", label: "farm.hectares" },
  { value: "square_meters", label: "farm.squareMeters" },
  { value: "square_feet", label: "farm.squareFeet" },
];

export default function CreateFarmScreen() {
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { refreshFarms, setSelectedFarm } = useFarm();
  const { user, refreshUserData } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: "",
    latitude: "",
    longitude: "",
    size: "",
    sizeUnit: "acres",
    type: "mixed",
    photoURL: "",
  });
  
  const [formErrors, setFormErrors] = useState({
    name: "",
    address: "",
    size: "",
  });

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  const validateForm = () => {
    let valid = true;
    const errors = {
      name: "",
      address: "",
      size: "",
    };

    if (!formData.name.trim()) {
      errors.name = t('farm.farmNameRequired');
      valid = false;
    }

    if (!formData.address.trim()) {
      errors.address = t('farm.farmAddressRequired');
      valid = false;
    }

    if (!formData.size.trim()) {
      errors.size = t('farm.farmSizeRequired');
      valid = false;
    } else if (isNaN(Number(formData.size)) || Number(formData.size) <= 0) {
      errors.size = t('farm.invalidFarmSize');
      valid = false;
    }

    setFormErrors(errors);
    return valid;
  };

  const handleImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Remove cropping
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setFormData({ ...formData, photoURL: result.assets[0].uri });
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert(t('common.error'), t('profile.imageUploadError'));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let photoURL = formData.photoURL;

      // Upload image to Firebase Storage if a local image is selected
      if (formData.photoURL && formData.photoURL.startsWith('file://')) {
        console.log("Uploading farm image to Storage...");
        const tempFarmId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        photoURL = await uploadFarmImage(formData.photoURL, tempFarmId);
        console.log("Farm image uploaded:", photoURL);
      }

      const farmData = {
        name: formData.name,
        description: formData.description,
        location: {
          address: formData.address,
          latitude: formData.latitude ? parseFloat(formData.latitude) : undefined,
          longitude: formData.longitude ? parseFloat(formData.longitude) : undefined,
        },
        size: parseFloat(formData.size),
        sizeUnit: formData.sizeUnit,
        type: formData.type,
        status: "active" as const,
        ownerUid: user?.uid,
        createdBy: user?.uid,
        caretakerIds: [], // Empty array for now
        photoURL: photoURL,
      };

      const newFarm = await createFarm(farmData, user?.role);
      
      // Update user to mark as no longer first login and assign the farm
      if (user && newFarm.id) {
        const currentAssignedFarms = user.assignedFarmIds || [];
        const updatedAssignedFarms = [...currentAssignedFarms, newFarm.id];
        
        await updateUser(user.uid, {
          assignedFarmIds: updatedAssignedFarms,
          farmId: newFarm.id,
          isFirstLogin: false, // Mark that the user has completed their first login flow
        });
        
        // Refresh user data to update the context
        await refreshUserData();
      }
      
      // Refresh farms to get the latest data and then set the newly created farm as selected
      await refreshFarms();

      // Set the newly created farm as selected after farms are refreshed
      console.log("Setting newly created farm as selected:", newFarm.name);
      setSelectedFarm(newFarm);

      Alert.alert(t('common.success'), t('farm.farmCreatedSuccessfully'), [
        { text: t('common.ok'), onPress: () => {
          // Just go back to the previous screen (dashboard)
          console.log("Navigating to owner dashboard...");
          router.back();
        }}
      ]);
    } catch (error) {
      console.error("Error creating farm:", error);
      Alert.alert(t('common.error'), t('farm.farmCreationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen 
        options={{ 
          title: t('farm.createFarm'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: "#fff",
          headerTitleStyle: { fontWeight: "bold" },
          headerLeft: () => (
            <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Basic Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.basicDetails')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmName')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' },
                formErrors.name ? styles.inputError : null
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder={t('farm.enterFarmName')}
              placeholderTextColor={secondaryTextColor}
            />
            {formErrors.name ? (
              <Text style={styles.errorText}>{formErrors.name}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmDescription')}</Text>
            <TextInput
              style={[styles.textArea, { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' }]}
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              placeholder={t('farm.enterFarmDescription')}
              placeholderTextColor={secondaryTextColor}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmType')}</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.typeSelector}>
              {FARM_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.typeOption,
                    formData.type === type.value && [styles.selectedType, { backgroundColor: colors.primary }]
                  ]}
                  onPress={() => setFormData({ ...formData, type: type.value })}
                >
                  <Text style={[
                    styles.typeText,
                    { color: formData.type === type.value ? "#fff" : textColor }
                  ]}>
                    {t(type.label)}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>

        {/* Location Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.location')}</Text>
          
          <View style={styles.formGroup}>
            <Text style={[styles.label, { color: textColor }]}>{t('farm.farmAddress')} *</Text>
            <TextInput
              style={[
                styles.input,
                { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' },
                formErrors.address ? styles.inputError : null
              ]}
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              placeholder={t('farm.enterFarmAddress')}
              placeholderTextColor={secondaryTextColor}
            />
            {formErrors.address ? (
              <Text style={styles.errorText}>{formErrors.address}</Text>
            ) : null}
          </View>

          <View style={[styles.row, isRTL && styles.rtlRow]}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.latitude')}</Text>
              <TextInput
                style={[styles.input, { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' }]}
                value={formData.latitude}
                onChangeText={(text) => setFormData({ ...formData, latitude: text })}
                placeholder={t('farm.enterLatitude')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.longitude')}</Text>
              <TextInput
                style={[styles.input, { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' }]}
                value={formData.longitude}
                onChangeText={(text) => setFormData({ ...formData, longitude: text })}
                placeholder={t('farm.enterLongitude')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Size Information */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.size')}</Text>
          
          <View style={[styles.row, isRTL && styles.rtlRow]}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.size')} *</Text>
              <TextInput
                style={[
                  styles.input,
                  { color: textColor, borderColor, textAlign: isRTL ? 'right' : 'left' },
                  formErrors.size ? styles.inputError : null
                ]}
                value={formData.size}
                onChangeText={(text) => setFormData({ ...formData, size: text })}
                placeholder={t('farm.enterFarmSize')}
                placeholderTextColor={secondaryTextColor}
                keyboardType="numeric"
              />
              {formErrors.size ? (
                <Text style={styles.errorText}>{formErrors.size}</Text>
              ) : null}
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={[styles.label, { color: textColor }]}>{t('farm.sizeUnit')}</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.unitSelector}>
                {SIZE_UNITS.map((unit) => (
                  <TouchableOpacity
                    key={unit.value}
                    style={[
                      styles.unitOption,
                      formData.sizeUnit === unit.value && [styles.selectedUnit, { backgroundColor: colors.primary }]
                    ]}
                    onPress={() => setFormData({ ...formData, sizeUnit: unit.value })}
                  >
                    <Text style={[
                      styles.unitText,
                      { color: formData.sizeUnit === unit.value ? "#fff" : textColor }
                    ]}>
                      {t(unit.label)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </View>

        {/* Photo */}
        <View style={[styles.section, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>{t('farm.farmImage')}</Text>
          
          <TouchableOpacity style={[styles.photoUpload, { borderColor }]} onPress={handleImagePicker}>
            {formData.photoURL ? (
              <View style={styles.photoContainer}>
                <Image source={{ uri: formData.photoURL }} style={styles.farmPhoto} />
                <TouchableOpacity 
                  style={styles.removePhoto}
                  onPress={() => setFormData({ ...formData, photoURL: "" })}
                >
                  <X size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.photoPlaceholder}>
                <Camera size={32} color={secondaryTextColor} />
                <Text style={[styles.photoText, { color: secondaryTextColor }]}>
                  {t('farm.addFarmImage')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={[styles.footer, { backgroundColor: cardColor, borderColor }]}>
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: colors.primary }]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Plus size={20} color="#fff" />
              <Text style={styles.submitButtonText}>
                {t('farm.createFarm')}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#D32F2F",
  },
  errorText: {
    color: "#D32F2F",
    fontSize: 12,
    marginTop: 4,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  rtlRow: {
    flexDirection: "row-reverse",
  },
  halfWidth: {
    flex: 1,
  },
  typeSelector: {
    marginTop: 8,
  },
  typeOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedType: {
    borderColor: "transparent",
  },
  typeText: {
    fontSize: 14,
    fontWeight: "500",
  },
  unitSelector: {
    marginTop: 8,
  },
  unitOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  selectedUnit: {
    borderColor: "transparent",
  },
  unitText: {
    fontSize: 12,
    fontWeight: "500",
  },
  photoUpload: {
    height: 120,
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
  },
  photoContainer: {
    position: "relative",
    width: "100%",
    height: "100%",
  },
  farmPhoto: {
    width: "100%",
    height: "100%",
    borderRadius: 6,
  },
  removePhoto: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  photoPlaceholder: {
    alignItems: "center",
  },
  photoText: {
    fontSize: 14,
    marginTop: 8,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  submitButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});