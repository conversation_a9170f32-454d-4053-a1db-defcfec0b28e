import { useEffect } from "react";
import { Redirect, Stack, useSegments } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLookupStore } from "@/services/lookup_service";

export default function AppLayout() {
  const { user, isLoading } = useAuth();
  const segments = useSegments();
  const { colors } = useTheme();

  // Move all hooks to the top before any conditional logic
  const fetchLookups = useLookupStore(state => state.fetchLookups);

  useEffect(() => {
    if (!isLoading && !user) {
      // If not logged in, redirect to the login page
      return;
    }
  }, [user, isLoading, segments]);

  useEffect(() => {
    // This will run once when the app layout mounts,
    // fetching all your lookups in the background.
    fetchLookups();
  }, [fetchLookups]);

  // If the user is not logged in, redirect to login
  if (!isLoading && !user) {
    return <Redirect href="/(auth)/login" />;
  }

  // If still loading, you can show a loading screen
  if (isLoading) {
    // If loading takes too long, proceed anyway if we have a user
    if (user) {
      console.log('⚠️ Still loading but user is available, proceeding with app layout');
    } else {
      return null;
    }
  }
  // User is authenticated, render the app layout
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="(owner)" options={{ headerShown: false }} />
      <Stack.Screen name="(admin)" options={{ headerShown: false }} />
      <Stack.Screen name="(caretaker)" options={{ headerShown: false }} />

      {/* Shared screens */}
      <Stack.Screen name="inventory/[id]" options={{ title: "Item Details" }} />
      <Stack.Screen name="inventory/edit" options={{ title: "Edit Inventory Item" }} />
      <Stack.Screen name="request/[id]" options={{ title: "Request Details" }} />
      <Stack.Screen name="request/create" options={{ title: "Create Request" }} />
      <Stack.Screen name="request/edit" options={{ title: "Edit Request" }} />
      <Stack.Screen name="request/create-inventory" options={{ title: "Create Inventory Request" }} />
      <Stack.Screen name="notes/create" options={{ title: "Add Note" }} />
      <Stack.Screen name="notes/[id]" options={{ title: "Note Details" }} />
      <Stack.Screen name="notes/edit" options={{ title: "Edit Note" }} />
      <Stack.Screen name="profile" options={{ title: "Profile" }} />
      <Stack.Screen name="users/[id]" options={{ title: "User Details" }} />

      {/* Report screens - these are now accessed through the reports tab */}
      <Stack.Screen name="report-details" options={{ title: "Report Details" }} />
    </Stack>
  );
}