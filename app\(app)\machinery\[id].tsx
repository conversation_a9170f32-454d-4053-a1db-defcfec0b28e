import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useLocalSearchParams } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { getMachineryById, deleteMachinery, Machinery } from "@/services/machinery-service";
import { getUserById } from "@/services/user-service";
import { useLookupStore } from "@/services/lookup_service";
import { Truck, Settings, Fuel, AlertTriangle, Wrench, Edit, Trash2, Calendar, Send, Ban } from "lucide-react-native";

export default function MachineryDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { colors } = useTheme();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const { t } = useLanguage();
  
  const [machinery, setMachinery] = useState<Machinery | null>(null);
  const [loading, setLoading] = useState(true);
  const [createdByName, setCreatedByName] = useState<string>("");
  const [machineryTypes, setMachineryTypes] = useState<any[]>([]);
  const [fuelTypes, setFuelTypes] = useState<any[]>([]);
  const { getLookupsByCategory } = useLookupStore();
  
  useEffect(() => {
    if (id && selectedFarm) {
      loadMachinery();
    }
    loadLookupData();
  }, [id, selectedFarm]);

  const loadLookupData = () => {
    try {
      const machineryTypesData = getLookupsByCategory('machineryType');
      const fuelTypesData = getLookupsByCategory('fuelType');
      setMachineryTypes(machineryTypesData);
      setFuelTypes(fuelTypesData);
    } catch (error) {
      console.error('Error loading lookup data:', error);
    }
  };

  const getMachineryTypeTitle = (typeId: string) => {
    const typeItem = machineryTypes.find(item => item.id === typeId);
    if (typeItem) {
      const title = typeItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`machinery.types.${title}`) || typeItem.title;
    }
    return typeId.charAt(0).toUpperCase() + typeId.slice(1);
  };

  const getFuelTypeTitle = (fuelTypeId: string) => {
    const fuelItem = fuelTypes.find(item => item.id === fuelTypeId);
    if (fuelItem) {
      const title = fuelItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`common.${title}`) || fuelItem.title;
    }
    return fuelTypeId.charAt(0).toUpperCase() + fuelTypeId.slice(1);
  };
  
  const loadMachinery = async () => {
    if (!id) {
      Alert.alert(t("common.error"), t("machinery.noIdProvided"));
      router.back();
      return;
    }
    
    try {
      setLoading(true);
      const machineryData = await getMachineryById(id as string, selectedFarm?.id);
      
      if (!machineryData) {
        Alert.alert(t("common.error"), t("machinery.notFound"));
        router.back();
        return;
      }
      
      setMachinery(machineryData);
      
      // Load creator name
      if (machineryData.createdBy) {
        try {
          const creator = await getUserById(machineryData.createdBy);
          setCreatedByName(creator?.name || t("common.unknown"));
        } catch (error) {
          console.error("Error loading creator:", error);
          setCreatedByName(t("common.unknown"));
        }
      }
    } catch (error) {
      console.error("Error loading machinery:", error);
      Alert.alert(t("common.error"), t("machinery.loadError"));
      router.back();
    } finally {
      setLoading(false);
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'working': return '#4CAF50';
      case 'maintenance': return '#FF9800';
      case 'malfunction': return '#F44336';
      case 'in_use':
      case 'in_use_other_farm': return '#2196F3';
      default: return '#757575';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working': return <Truck size={24} color="#4CAF50" />;
      case 'maintenance': return <Wrench size={24} color="#FF9800" />;
      case 'malfunction': return <AlertTriangle size={24} color="#F44336" />;
      case 'in_use':
      case 'in_use_other_farm': return <Ban size={24} color="#2196F3" />;
      default: return <Truck size={24} color="#757575" />;
    }
  };
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'working': return t("machinery.available");
      case 'maintenance': return t("machinery.maintenanceStatus");
      case 'malfunction': return t("machinery.malfunction");
      case 'in_use':
      case 'in_use_other_farm': return t("machinery.inUse");
      default: return status.replace('_', ' ').toUpperCase();
    }
  };
  
  const handleEdit = () => {
    if (machinery) {
      router.push(`/(app)/machinery/edit?id=${machinery.id}`);
    }
  };
  
  const handleDelete = () => {
    if (!machinery) return;
    
    Alert.alert(
      t("machinery.deleteConfirmTitle"),
      t("machinery.deleteConfirmMessage"),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await deleteMachinery(machinery.id, machinery.farmId);
              Alert.alert(t("common.success"), t("machinery.deleteSuccess"), [
                { text: t("common.ok"), onPress: () => router.back() }
              ]);
            } catch (error) {
              console.error("Error deleting machinery:", error);
              Alert.alert(t("common.error"), t("machinery.deleteError"));
            }
          }
        }
      ]
    );
  };
  
  const handleRequest = () => {
    if (!machinery) return;
    
    // Check if machinery is available for request
    if (machinery.status !== 'working') {
      Alert.alert(
        t("common.error"), 
        t("machinery.machineryNotAvailable"),
        [{ text: t("common.ok") }]
      );
      return;
    }
    
    router.push({
      pathname: '/(app)/machinery/request',
      params: {
        machineryId: machinery.id,
        machineryName: machinery.name,
        machineryType: machinery.type,
        machineryModel: machinery.model,
        type: 'use'
      }
    });
  };
  
  const canEditOrDelete = () => {
    return user?.role === "admin" || user?.role === "owner";
  };
  
  const canRequest = () => {
    return user?.role === "caretaker" && machinery?.status === 'working';
  };
  
  const isInUse = () => {
    return machinery?.status === 'in_use' || machinery?.status === 'in_use_other_farm';
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen 
          options={{
            title: t("machinery.details"),
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }} 
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            {t("machinery.loadingDetails")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (!machinery) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
        <Stack.Screen 
          options={{
            title: t("machinery.details"),
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: "#fff",
          }} 
        />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: "#F44336" }]}>
            {t("machinery.notFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  // Default image URL if no image is provided
  const defaultImageUrl = `https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop&crop=center`;
  const imageUrl = machinery.imageUrl || defaultImageUrl;
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: machinery.name,
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerRight: () => canEditOrDelete() ? (
            <View style={styles.headerActions}>
              <TouchableOpacity onPress={handleEdit} style={styles.headerButton}>
                <Edit size={20} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleDelete} style={styles.headerButton}>
                <Trash2 size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ) : null,
        }}
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Image Section */}
        <View style={styles.imageSection}>
          <Image 
            source={{ uri: imageUrl }} 
            style={styles.machineryImage}
            resizeMode="cover"
          />
          {/* In Use overlay on image */}
          {isInUse() && (
            <View style={styles.inUseImageOverlay}>
              <Ban size={24} color="#fff" />
              <Text style={styles.inUseImageOverlayText}>IN USE</Text>
            </View>
          )}
        </View>
        
        <View style={[styles.headerCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <View style={styles.machineryHeader}>
            <View style={styles.machineryInfo}>
              <Text style={[styles.machineryName, { color: colors.text }]}>
                {machinery.name}
              </Text>
              <Text style={[styles.machineryModel, { color: colors.textSecondary }]}>
                {machinery.model} • {machinery.year}
              </Text>
              <Text style={[styles.machineryType, { color: colors.textSecondary }]}>
                {getMachineryTypeTitle(machinery.type)}
              </Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(machinery.status) + '20', borderColor: getStatusColor(machinery.status) }]}>
              {getStatusIcon(machinery.status)}
              <Text style={[styles.statusText, { color: getStatusColor(machinery.status) }]}>
                {getStatusText(machinery.status)}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("machinery.basicInformation")}</Text>
          
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>{t("machinery.serialNumber")}</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {machinery.serialNumber || t("common.notSpecified")}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>{t("machinery.fuelType")}</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {getFuelTypeTitle(machinery.fuelType)}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>{t("machinery.currentLocation")}</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {machinery.currentLocation || selectedFarm?.name || t("common.notSpecified")}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>{t("common.createdBy")}</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {createdByName || t("common.unknown")}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("machinery.fuelInformation")}</Text>
          
          <View style={styles.fuelContainer}>
            <View style={styles.fuelHeader}>
              <Fuel size={20} color="#2196F3" />
              <Text style={[styles.fuelTitle, { color: colors.text }]}>{t("machinery.fuelLevel")}</Text>
            </View>
            
            <View style={styles.fuelBarContainer}>
              <View style={[styles.fuelBar, { backgroundColor: colors.border }]}>
                <View 
                  style={[
                    styles.fuelBarFill, 
                    { 
                      width: `${(machinery.currentFuelLevel / machinery.fuelCapacity) * 100}%`,
                      backgroundColor: machinery.currentFuelLevel < machinery.fuelCapacity * 0.2 ? '#F44336' : '#4CAF50'
                    }
                  ]} 
                />
              </View>
              <Text style={[styles.fuelText, { color: colors.text }]}>
                {machinery.currentFuelLevel}L / {machinery.fuelCapacity}L
              </Text>
            </View>
          </View>
        </View>
        
        <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("machinery.maintenanceInformation")}</Text>
          
          <View style={styles.maintenanceContainer}>
            <View style={styles.maintenanceItem}>
              <View style={styles.maintenanceHeader}>
                <Settings size={18} color="#FF9800" />
                <Text style={[styles.maintenanceLabel, { color: colors.textSecondary }]}>
                  {t("machinery.odometerReading")}
                </Text>
              </View>
              <Text style={[styles.maintenanceValue, { color: colors.text }]}>
                {machinery.odometerReading} {t("common.hours")}
              </Text>
            </View>
            
            <View style={styles.maintenanceItem}>
              <View style={styles.maintenanceHeader}>
                <Calendar size={18} color="#FF9800" />
                <Text style={[styles.maintenanceLabel, { color: colors.textSecondary }]}>
                  {t("machinery.lastMaintenanceDate")}
                </Text>
              </View>
              <Text style={[styles.maintenanceValue, { color: colors.text }]}>
                {new Date(machinery.lastMaintenanceDate).toLocaleDateString()}
              </Text>
            </View>
            
            <View style={styles.maintenanceItem}>
              <View style={styles.maintenanceHeader}>
                <Calendar size={18} color="#F44336" />
                <Text style={[styles.maintenanceLabel, { color: colors.textSecondary }]}>
                  {t("machinery.nextMaintenanceDate")}
                </Text>
              </View>
              <Text style={[styles.maintenanceValue, { color: colors.text }]}>
                {new Date(machinery.nextMaintenanceDate).toLocaleDateString()}
              </Text>
            </View>
            
            <View style={styles.maintenanceItem}>
              <View style={styles.maintenanceHeader}>
                <Wrench size={18} color="#FF9800" />
                <Text style={[styles.maintenanceLabel, { color: colors.textSecondary }]}>
                  {t("machinery.maintenanceIntervalHours")}
                </Text>
              </View>
              <Text style={[styles.maintenanceValue, { color: colors.text }]}>
                {t("machinery.every")} {machinery.maintenanceIntervalHours} {t("common.hours")}
              </Text>
            </View>
          </View>
        </View>
        
        {machinery.notes && (
          <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("common.notes")}</Text>
            <Text style={[styles.notesText, { color: colors.text }]}>
              {machinery.notes}
            </Text>
          </View>
        )}
        
        <View style={[styles.detailsCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t("common.timestamps")}</Text>
          
          <View style={styles.timestampsContainer}>
            <View style={styles.timestampItem}>
              <Text style={[styles.timestampLabel, { color: colors.textSecondary }]}>
                {t("common.createdAt")}
              </Text>
              <Text style={[styles.timestampValue, { color: colors.text }]}>
                {new Date(machinery.createdAt).toLocaleString()}
              </Text>
            </View>
            
            <View style={styles.timestampItem}>
              <Text style={[styles.timestampLabel, { color: colors.textSecondary }]}>
                {t("common.lastUpdated")}
              </Text>
              <Text style={[styles.timestampValue, { color: colors.text }]}>
                {new Date(machinery.updatedAt).toLocaleString()}
              </Text>
            </View>
          </View>
        </View>
        
        {/* Action Buttons - Only show request button for caretakers if machinery is available */}
        {user?.role === "caretaker" && (
          <>
            {canRequest() ? (
              <TouchableOpacity 
                style={[styles.requestButton, { backgroundColor: colors.primary }]}
                onPress={handleRequest}
              >
                <Send size={20} color="#fff" style={styles.requestButtonIcon} />
                <Text style={styles.requestButtonText}>{t("common.requestUse")}</Text>
              </TouchableOpacity>
            ) : (
              <View style={[styles.disabledRequestButton, { backgroundColor: colors.textSecondary + '40' }]}>
                <Ban size={20} color={colors.textSecondary} style={styles.requestButtonIcon} />
                <Text style={[styles.disabledRequestButtonText, { color: colors.textSecondary }]}>
                  {isInUse() ? t("machinery.inUse") : 
                   machinery.status === 'maintenance' ? t("machinery.maintenanceStatus") :
                   machinery.status === 'malfunction' ? t("machinery.malfunction") :
                   t("machinery.unavailable")}
                </Text>
              </View>
            )}
          </>
        )}
        
        {/* Edit and Delete buttons for owners and admins */}
        {canEditOrDelete() && (
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={handleEdit}
            >
              <Edit size={20} color="#fff" style={styles.actionButtonIcon} />
              <Text style={styles.actionButtonText}>{t("common.edit")}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.deleteButton, { backgroundColor: "#F44336" }]}
              onPress={handleDelete}
            >
              <Trash2 size={20} color="#fff" style={styles.actionButtonIcon} />
              <Text style={styles.actionButtonText}>{t("common.delete")}</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    fontWeight: "600",
  },
  headerActions: {
    flexDirection: "row",
    gap: 8,
  },
  headerButton: {
    padding: 8,
  },
  imageSection: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    height: 200,
    position: 'relative',
  },
  machineryImage: {
    width: '100%',
    height: '100%',
  },
  inUseImageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(33, 150, 243, 0.9)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  inUseImageOverlayText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },
  headerCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  machineryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  machineryInfo: {
    flex: 1,
  },
  machineryName: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  machineryModel: {
    fontSize: 16,
    marginBottom: 4,
  },
  machineryType: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    gap: 6,
    borderWidth: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "600",
  },
  detailsCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  detailsGrid: {
    gap: 16,
  },
  detailItem: {
    gap: 4,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  fuelContainer: {
    gap: 12,
  },
  fuelHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  fuelTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  fuelBarContainer: {
    gap: 8,
  },
  fuelBar: {
    height: 8,
    borderRadius: 4,
    overflow: "hidden",
  },
  fuelBarFill: {
    height: "100%",
    borderRadius: 4,
  },
  fuelText: {
    fontSize: 14,
    fontWeight: "500",
  },
  maintenanceContainer: {
    gap: 16,
  },
  maintenanceItem: {
    gap: 8,
  },
  maintenanceHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  maintenanceLabel: {
    fontSize: 14,
  },
  maintenanceValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  notesText: {
    fontSize: 16,
    lineHeight: 24,
  },
  timestampsContainer: {
    gap: 16,
  },
  timestampItem: {
    gap: 4,
  },
  timestampLabel: {
    fontSize: 14,
  },
  timestampValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  requestButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  disabledRequestButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  requestButtonIcon: {
    marginRight: 8,
  },
  requestButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  disabledRequestButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    marginTop: 8,
  },
  editButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
  },
  deleteButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
  },
  actionButtonIcon: {
    marginRight: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});