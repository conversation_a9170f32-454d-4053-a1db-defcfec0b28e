import React from "react";
import { Tabs } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { Redirect } from "expo-router";
import { Home, Package, FileText, User, Truck, MapPin } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";

export default function CaretakerLayout() {
  const { user } = useAuth();
  const { theme, colors } = useTheme();
  const { t } = useLanguage();
  const isDarkMode = theme === "dark";

  // If user is not a caretaker, redirect to the appropriate dashboard
  if (user && user.role !== "caretaker") {
    return <Redirect href={`/(app)/(${user.role})`} />;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: "#757575",
        tabBarStyle: {
          borderTopWidth: 1,
          borderTopColor: isDarkMode ? "#333333" : "#e0e0e0",
          height: 60,
          paddingBottom: 8,
          backgroundColor: isDarkMode ? "#121212" : "#ffffff",
        },
        tabBarLabelStyle: {
          fontSize: 12,
        },
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
        lazy: true, // Only render screens when they are focused
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: t("dashboard.title"),
          tabBarIcon: ({ color }) => <Home size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="inventory"
        options={{
          title: t("inventory.title"),
          tabBarIcon: ({ color }) => <Package size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="machinery"
        options={{
          title: t("machinery.title"),
          tabBarIcon: ({ color }) => <Truck size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="notes"
        options={{
          title: t("notes.title"),
          tabBarIcon: ({ color }) => <FileText size={24} color={color} />,
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: t("profile.title"),
          tabBarIcon: ({ color }) => <User size={24} color={color} />,
        }}
      />

      {/* Hide request and farms screens from the tab bar */}
      <Tabs.Screen
        name="requests"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="farms"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="farms/[id]"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}