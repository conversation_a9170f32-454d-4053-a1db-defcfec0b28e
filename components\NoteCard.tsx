import React from "react";
import { StyleSheet, Text, View, TouchableOpacity, Image } from "react-native";
import { ChevronRight, User, Calendar, Mic, Image as ImageIcon, MoreVertical, Edit, Trash2 } from "lucide-react-native";
import { Note } from "@/services/notes-service";
import { LinearGradient } from "expo-linear-gradient";
import { Menu, MenuOptions, MenuOption, MenuTrigger } from "react-native-popup-menu";
import { useLanguage } from "@/context/language-context";

interface NoteCardProps {
  note: Note;
  onPress: () => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onDelete?: () => void; // Added for backward compatibility
  showEditOptions?: boolean;
  isDarkMode?: boolean;
  role?: "owner" | "admin" | "caretaker";
  currentUserId?: string;
  showFarmName?: boolean;
}

export const NoteCard = ({
  note,
  onPress,
  onEditPress,
  onDeletePress,
  onDelete, // Added for backward compatibility
  showEditOptions = false,
  isDarkMode = false,
  role = "caretaker",
  currentUserId,
  showFarmName = false,
}: NoteCardProps) => {
  const { isRTL, t } = useLanguage();
  
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return t("common.today") + ", " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return t("common.yesterday") + ", " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      const days = [
        t("common.sunday"), 
        t("common.monday"), 
        t("common.tuesday"), 
        t("common.wednesday"), 
        t("common.thursday"), 
        t("common.friday"), 
        t("common.saturday")
      ];
      return days[date.getDay()] + ", " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString() + ", " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get note type color
  const getNoteTypeColor = (): readonly [string, string] => {
    if (note.hasAudio && note.images && note.images.length > 0) {
      return isDarkMode ? ['#7B1FA2', '#4A148C'] as const : ['#BA68C8', '#9C27B0'] as const; // Purple for multimedia
    } else if (note.hasAudio) {
      return isDarkMode ? ['#F57C00', '#E65100'] as const : ['#FFB74D', '#FF9800'] as const; // Orange for audio
    } else if (note.images && note.images.length > 0) {
      return isDarkMode ? ['#1976D2', '#0D47A1'] as const : ['#64B5F6', '#2196F3'] as const; // Blue for images
    } else {
      return isDarkMode ? ['#2E7D32', '#1B5E20'] as const : ['#81C784', '#4CAF50'] as const; // Green for text
    }
  };

  // Check if user can edit/delete this note
  const canModify = note.createdBy === currentUserId || role === "owner" || role === "admin";
  
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const typeColors = getNoteTypeColor();
  
  // Use onDeletePress if provided, otherwise fall back to onDelete
  const handleDelete = onDeletePress || onDelete;
  
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.card,
          {
            backgroundColor: cardColor,
            borderColor,
          },
        ]}
      >
        {/* Image Section - Only show if image exists */}
        {note.images && note.images.length > 0 && (
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: note.images[0] }}
              style={styles.noteImage}
              resizeMode="cover"
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.imageOverlay}
            />
            
            {/* Media Badges */}
            <View style={[styles.mediaBadgesContainer, isRTL && styles.rtlMediaBadgesContainer]}>
              {note.hasAudio && (
                <View style={[styles.mediaBadge, { backgroundColor: "#FF9800" }]}>
                  <Mic size={12} color="#fff" />
                </View>
              )}
              {note.images && note.images.length > 1 && (
                <View style={[styles.mediaBadge, { backgroundColor: "#2196F3" }]}>
                  <ImageIcon size={12} color="#fff" />
                  <Text style={styles.mediaBadgeText}>+{note.images.length - 1}</Text>
                </View>
              )}
            </View>
            
            {/* Type Badge */}
            <View style={[styles.typeBadgeContainer, isRTL && styles.rtlTypeBadgeContainer]}>
              <LinearGradient
                colors={typeColors}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.typeBadge}
              >
                <Text style={styles.typeBadgeText}>{t("notes.title")}</Text>
              </LinearGradient>
            </View>

            {/* Menu for edit options */}
            {showEditOptions && canModify && (
              <View style={[styles.menuContainer, isRTL && styles.rtlMenuContainer]}>
                <Menu>
                  <MenuTrigger>
                    <View style={styles.menuButton}>
                      <MoreVertical size={16} color="#fff" />
                    </View>
                  </MenuTrigger>
                  <MenuOptions customStyles={{
                    optionsContainer: {
                      backgroundColor: isDarkMode ? "#333" : "#fff",
                      borderRadius: 8,
                      padding: 5,
                    }
                  }}>
                    {onEditPress && (
                      <MenuOption onSelect={onEditPress}>
                        <View style={[styles.menuOption, isRTL && styles.rtlMenuOption]}>
                          <Edit size={16} color={isDarkMode ? "#fff" : "#333"} />
                          <Text style={[
                            { color: isDarkMode ? "#fff" : "#333" }, 
                            isRTL ? { marginRight: 8 } : { marginLeft: 8 }
                          ]}>
                            {t("common.edit")}
                          </Text>
                        </View>
                      </MenuOption>
                    )}
                    {handleDelete && (
                      <MenuOption onSelect={handleDelete}>
                        <View style={[styles.menuOption, isRTL && styles.rtlMenuOption]}>
                          <Trash2 size={16} color="#F44336" />
                          <Text style={[
                            { color: "#F44336" }, 
                            isRTL ? { marginRight: 8 } : { marginLeft: 8 }
                          ]}>
                            {t("common.delete")}
                          </Text>
                        </View>
                      </MenuOption>
                    )}
                  </MenuOptions>
                </Menu>
              </View>
            )}
          </View>
        )}
        
        {/* Content Section */}
        <View style={styles.contentContainer}>
          {/* If no image, show badges at top */}
          {(!note.images || note.images.length === 0) && (
            <View style={[styles.topBadgeContainer, isRTL && styles.rtlTopBadgeContainer]}>
              <LinearGradient
                colors={typeColors}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.topTypeBadge}
              >
                <Text style={styles.typeBadgeText}>{t("notes.title")}</Text>
              </LinearGradient>
              
              <View style={styles.mediaBadgesRow}>
                {note.hasAudio && (
                  <View style={[styles.mediaBadge, { backgroundColor: "#FF9800" }]}>
                    <Mic size={12} color="#fff" />
                  </View>
                )}
              </View>
            </View>
          )}

          <View style={[styles.headerSection, isRTL && styles.rtlHeaderSection]}>
            <View style={styles.titleSection}>
              <Text style={[styles.noteTitle, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]} numberOfLines={1}>
                {note.title}
              </Text>
            </View>

            {/* Menu for edit options when no image */}
            {(!note.images || note.images.length === 0) && showEditOptions && canModify && (
              <Menu>
                <MenuTrigger>
                  <MoreVertical size={20} color={secondaryTextColor} style={styles.menuIcon} />
                </MenuTrigger>
                <MenuOptions customStyles={{
                  optionsContainer: {
                    backgroundColor: isDarkMode ? "#333" : "#fff",
                    borderRadius: 8,
                    padding: 5,
                  }
                }}>
                  {onEditPress && (
                    <MenuOption onSelect={onEditPress}>
                      <View style={[styles.menuOption, isRTL && styles.rtlMenuOption]}>
                        <Edit size={16} color={isDarkMode ? "#fff" : "#333"} />
                        <Text style={[
                          { color: isDarkMode ? "#fff" : "#333" }, 
                          isRTL ? { marginRight: 8 } : { marginLeft: 8 }
                        ]}>
                          {t("common.edit")}
                        </Text>
                      </View>
                    </MenuOption>
                  )}
                  {handleDelete && (
                    <MenuOption onSelect={handleDelete}>
                      <View style={[styles.menuOption, isRTL && styles.rtlMenuOption]}>
                        <Trash2 size={16} color="#F44336" />
                        <Text style={[
                          { color: "#F44336" }, 
                          isRTL ? { marginRight: 8 } : { marginLeft: 8 }
                        ]}>
                          {t("common.delete")}
                        </Text>
                      </View>
                    </MenuOption>
                  )}
                </MenuOptions>
              </Menu>
            )}
          </View>
          
          <Text style={[styles.noteContent, { color: secondaryTextColor, textAlign: isRTL ? 'right' : 'left' }]} numberOfLines={2}>
            {note.content}
          </Text>
          
          <View style={[styles.metaSection, isRTL && styles.rtlMetaSection]}>
            <View style={[styles.metaItem, isRTL && styles.rtlMetaItem]}>
              <User size={12} color={secondaryTextColor} />
              <Text style={[styles.metaText, { color: secondaryTextColor }]}>
                {note.createdByName || note.userName}
              </Text>
            </View>
            <View style={[styles.metaItem, isRTL && styles.rtlMetaItem]}>
              <Calendar size={12} color={secondaryTextColor} />
              <Text style={[styles.metaText, { color: secondaryTextColor }]}>
                {formatDate(note.createdAt)}
              </Text>
            </View>
          </View>
          
          <View style={styles.footerSection}>
            <View style={[styles.actionHint, isRTL && styles.rtlActionHint]}>
              <Text style={[styles.hintText, { color: secondaryTextColor }]}>
                {t("common.tapToViewDetails")}
              </Text>
              <ChevronRight size={16} color={secondaryTextColor} />
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  card: {
    borderRadius: 16,
    overflow: "hidden",
    borderWidth: 1,
  },
  imageContainer: {
    width: "100%",
    height: 140,
    position: "relative",
  },
  noteImage: {
    width: "100%",
    height: "100%",
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  mediaBadgesContainer: {
    position: "absolute",
    top: 12,
    left: 12,
    flexDirection: "row",
    gap: 8,
  },
  rtlMediaBadgesContainer: {
    left: 'auto',
    right: 12,
  },
  mediaBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  mediaBadgeText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "600",
  },
  typeBadgeContainer: {
    position: "absolute",
    top: 12,
    right: 12,
  },
  rtlTypeBadgeContainer: {
    right: 'auto',
    left: 12,
  },
  typeBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeBadgeText: {
    color: "#fff",
    fontSize: 11,
    fontWeight: "600",
  },
  menuContainer: {
    position: "absolute",
    bottom: 12,
    right: 12,
  },
  rtlMenuContainer: {
    right: 'auto',
    left: 12,
  },
  menuButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  menuOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
  },
  rtlMenuOption: {
    flexDirection: "row-reverse",
  },
  menuIcon: {
    marginLeft: 8,
  },
  contentContainer: {
    padding: 16,
  },
  topBadgeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  rtlTopBadgeContainer: {
    flexDirection: "row-reverse",
  },
  topTypeBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  mediaBadgesRow: {
    flexDirection: "row",
    gap: 8,
  },
  headerSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  rtlHeaderSection: {
    flexDirection: "row-reverse",
  },
  titleSection: {
    flex: 1,
    marginRight: 12,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: "700",
  },
  noteContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  metaSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  rtlMetaSection: {
    flexDirection: "row-reverse",
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  rtlMetaItem: {
    flexDirection: "row-reverse",
  },
  metaText: {
    fontSize: 11,
    fontWeight: "500",
  },
  footerSection: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.08)",
    paddingTop: 12,
  },
  actionHint: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  rtlActionHint: {
    flexDirection: "row-reverse",
  },
  hintText: {
    fontSize: 11,
    fontStyle: "italic",
  },
});