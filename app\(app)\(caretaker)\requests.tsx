import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, ActivityIndicator, Al<PERSON>, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useRouter, useFocusEffect } from "expo-router";
import { useAuth } from "@/context/auth-context";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { RequestCard } from "@/components/RequestCard";
import { Request, getUserRequests, deleteRequest } from "@/services/request-service";
import { Search, ClipboardList, Filter, Plus } from "lucide-react-native";
import { FilterButton } from "@/components/FilterButton";

export default function CaretakerRequestsScreen() {
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { selectedFarm } = useFarm();
  const router = useRouter();
  
  const [requests, setRequests] = useState<Request[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<Request[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [activeTab, setActiveTab] = useState("inventory"); // "inventory", "machinery"
  
  const statusFilterButtons = [
    { key: "status-all", label: t("common.all"), value: "all", activeColor: colors.primary },
    { key: "status-pending", label: t("common.pending"), value: "pending", activeColor: "#FF9800" },
    { key: "status-approved", label: t("common.approved"), value: "approved", activeColor: "#4CAF50" },
    { key: "status-rejected", label: t("common.rejected"), value: "rejected", activeColor: "#F44336" },
  ];
  
  useEffect(() => {
    if (selectedFarm && user) {
      loadRequests();
    }
  }, [selectedFarm, user, activeTab]);

  useEffect(() => {
    filterRequests();
  }, [searchQuery, statusFilter, requests]);

  // Auto-refresh when screen comes into focus (e.g., after creating a new request)
  useFocusEffect(
    React.useCallback(() => {
      if (selectedFarm && user) {
        loadRequests();
      }
    }, [selectedFarm, user, activeTab])
  );
  
  const loadRequests = async () => {
    try {
      setLoading(true);
      if (!selectedFarm || !user) {
        setRequests([]);
        return;
      }
      
      const farmId = selectedFarm.id;
      
      let fetchedRequests: Request[] = [];
      
      if (activeTab === "inventory") {
        // Get caretaker's own inventory requests
        const caretakerInventoryRequests = await getUserRequests(farmId, user.uid, undefined, "inventory");
        const caretakerExistingRequests = await getUserRequests(farmId, user.uid, undefined, "existing");
        
        fetchedRequests = [
          ...caretakerInventoryRequests,
          ...caretakerExistingRequests
        ];
      } else if (activeTab === "machinery") {
        // Get caretaker's own machinery requests
        const caretakerMachineryRequests = await getUserRequests(farmId, user.uid, undefined, "machinery");
        
        fetchedRequests = caretakerMachineryRequests;
      }
      
      setRequests(fetchedRequests);
    } catch (error) {
      console.error("Error loading requests:", error);
      Alert.alert(t("common.error"), t("requests.loadError"));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const filterRequests = () => {
    let filtered = [...requests];
    
    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((request) => request.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (request) =>
          (request.itemName && request.itemName.toLowerCase().includes(query)) ||
          (request.machineryName && request.machineryName.toLowerCase().includes(query)) ||
          (request.category && request.category.toLowerCase().includes(query)) ||
          (request.reason && request.reason.toLowerCase().includes(query)) ||
          (request.requestedByName && request.requestedByName.toLowerCase().includes(query))
      );
    }
    
    setFilteredRequests(filtered);
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadRequests();
  };
  
  const handleRequestPress = (requestId: string) => {
    router.push(`/request/${requestId}?farmId=${selectedFarm?.id}`);
  };

  const handleCreateRequest = () => {
    if (!selectedFarm) {
      Alert.alert(t("common.error"), t("farm.noFarmSelected"));
      return;
    }

    // Navigate to create request screen
    router.push(`/request/create?farmId=${selectedFarm.id}&type=${activeTab}`);
  };
  

  
  const handleEditRequest = (request: any) => {
    if (request.requestType === "machinery") {
      // Navigate to machinery request screen with edit parameters
      router.push(`/(app)/machinery/request?requestId=${request.id}&farmId=${selectedFarm?.id}`);
    } else {
      // Navigate to inventory edit screen
      router.push(`/request/edit?id=${request.id}&farmId=${selectedFarm?.id}`);
    }
  };
  
  const handleDeleteRequest = async (requestId: string) => {
    Alert.alert(
      t("common.confirm"),
      t("requests.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          onPress: async () => {
            try {
              if (!selectedFarm) {
                throw new Error("No farm selected");
              }
              
              await deleteRequest(requestId, selectedFarm.id);
              setRequests((prevRequests) => prevRequests.filter((request) => request.id !== requestId));
              Alert.alert(t("common.success"), t("requests.requestDeleted"));
            } catch (error) {
              console.error("Error deleting request:", error);
              Alert.alert(t("common.error"), t("requests.deleteError"));
            }
          },
          style: "destructive",
        },
      ]
    );
  };

  const handleReturnRequest = (requestId: string) => {
    if (!selectedFarm) {
      Alert.alert("Error", "No farm selected");
      return;
    }
    
    const request = requests.find(r => r.id === requestId);
    if (!request) {
      Alert.alert("Error", "Request not found");
      return;
    }
    
    // Route to appropriate return screen based on request type
    if (request.requestType === "machinery") {
      router.push(`/machinery/return?requestId=${requestId}&farmId=${selectedFarm.id}`);
    } else {
      router.push(`/request/return?requestId=${requestId}&farmId=${selectedFarm.id}`);
    }
  };

  // Request type tabs at the top
  const renderRequestTypeTabs = () => {
    return (
      <View style={[styles.tabsContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "inventory" && [styles.activeTabButton, { borderColor: "#4CAF50" }]
          ]}
          onPress={() => setActiveTab("inventory")}
        >
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === "inventory" ? "#4CAF50" : colors.textSecondary }
            ]}
          >
            {t("common.inventoryRequests")}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "machinery" && [styles.activeTabButton, { borderColor: "#2196F3" }]
          ]}
          onPress={() => setActiveTab("machinery")}
        >
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === "machinery" ? "#2196F3" : colors.textSecondary }
            ]}
          >
            {t("common.machineryRequests")}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  const renderEmptyList = () => {
    if (loading) return null;
    
    if (!selectedFarm) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {t("common.noFarmSelected")}
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            {t("common.farmIdRequired")}
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.emptyContainer}>
        <ClipboardList size={64} color={colors.textSecondary} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all" 
            ? t("requests.noRequestsFound") 
            : activeTab === "machinery"
              ? t("requests.noMachineryRequests")
              : t("requests.noInventoryRequests")}
        </Text>
        <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
          {searchQuery.trim() !== "" || statusFilter !== "all" 
            ? t("requests.tryDifferentSearch") 
            : activeTab === "machinery"
              ? t("common.yourMachineryRequestsWillAppearHere")
              : t("common.yourInventoryRequestsWillAppearHere")}
        </Text>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t("requests.title"),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
        }}
      />
      
      <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
        <Search size={20} color={colors.textSecondary} />
        <TextInput
          style={[
            styles.searchInput, 
            { color: colors.text, textAlign: isRTL ? 'right' : 'left' }
          ]}
          placeholder={t("requests.searchRequests")}
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Request Type Tabs */}
      {renderRequestTypeTabs()}
      
      <View style={[styles.filtersContainer, isRTL && styles.rtlFiltersContainer]}>
        <Text style={[styles.filtersLabel, { color: colors.textSecondary }]}>
          <Filter size={14} color={colors.textSecondary} /> {t("common.filter")}:
        </Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          {statusFilterButtons.map((button) => (
            <FilterButton
              key={button.key}
              label={button.label}
              active={statusFilter === button.value}
              onPress={() => setStatusFilter(button.value)}
              activeColor={button.activeColor}
              isDarkMode={colors.background === "#121212"}
            />
          ))}
        </ScrollView>
      </View>
      
      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <FlatList
          data={filteredRequests}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <RequestCard
              request={item}
              onPress={() => handleRequestPress(item.id)}
              onEditPress={
                item.status === "pending" && item.requestedBy === user?.uid
                  ? () => handleEditRequest(item)
                  : undefined
              }
              onDeletePress={
                item.status === "pending" && item.requestedBy === user?.uid
                  ? () => handleDeleteRequest(item.id)
                  : undefined
              }
              onReturnPress={
                item.status === "approved" &&
                ((item.requestType === "inventory" || item.requestType === "existing") && item.canReturn && !item.hasReturn) ||
                (item.requestType === "machinery" && (item.requestSubType === "use" || item.requestSubType === "maintenance") && !item.hasReturn) &&
                item.requestedBy === user?.uid
                  ? () => handleReturnRequest(item.id)
                  : undefined
              }
              showEditOptions={true}
              isDarkMode={colors.background === "#121212"}
              role="caretaker"
              currentUserId={user?.uid}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyList}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      )}

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    margin: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    zIndex: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  tabsContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
    overflow: "hidden",
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  filtersContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  rtlFiltersContainer: {
    flexDirection: "row-reverse",
  },
  filtersLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  filtersScrollContent: {
    paddingRight: 16,
    flexDirection: "row",
    gap: 8,
  },
  listContent: {
    padding: 16,
    paddingTop: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 1000,
  },
});