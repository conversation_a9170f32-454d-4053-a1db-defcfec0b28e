import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAuth } from "@/context/auth-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useTheme } from "@/context/theme-context";
import { createExpense } from "@/services/expense-service";
import { getUsersByFarm } from "@/services/user-service";
import { 
  Calendar, 
  DollarSign, 
  FileText, 
  Tag, 
  User, 
  Save,
  ArrowLeft
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import ModalDropdown from "@/components/ModalDropdown";
import DateTimePicker from '@react-native-community/datetimepicker';

const EXPENSE_CATEGORIES = [
  { label: 'Inventory', value: 'inventory' },
  { label: 'Machinery', value: 'machinery' },
  { label: 'Fuel', value: 'fuel' },
  { label: 'Maintenance', value: 'maintenance' },
  { label: 'Labor', value: 'labor' },
  { label: 'Utilities', value: 'utilities' },
  { label: 'Transport', value: 'transport' },
  { label: 'Other', value: 'other' },
];

const EXPENSE_TYPES = [
  { label: 'Farm Expense', value: 'farm' },
  { label: 'User Expense', value: 'user' },
];

export default function AddExpenseScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { selectedFarm } = useFarm();
  const { colors, theme } = useTheme();
  const { t } = useLanguage();

  const [formData, setFormData] = useState({
    type: 'farm',
    category: '',
    amount: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    userId: '',
    notes: '',
  });

  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    if (selectedFarm) {
      loadUsers();
    }
  }, [selectedFarm]);

  const loadUsers = async () => {
    if (!selectedFarm) return;

    try {
      const farmUsers = await getUsersByFarm(selectedFarm.id);
      const userOptions = farmUsers.map(user => ({
        label: `${user.displayName || user.email} (${user.role})`,
        value: user.uid,
      }));
      setUsers(userOptions);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || new Date();
    setShowDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setSelectedDate(currentDate);
      setFormData(prev => ({ ...prev, date: currentDate.toISOString().split('T')[0] }));
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.amount.trim()) {
      newErrors.amount = t('expenses.amountRequired');
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = t('expenses.invalidAmount');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('expenses.descriptionRequired');
    }

    if (!formData.category) {
      newErrors.category = t('expenses.categoryRequired');
    }

    if (formData.type === 'user' && !formData.userId) {
      newErrors.userId = t('expenses.userRequired');
    }

    if (!formData.date) {
      newErrors.date = t('expenses.dateRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!selectedFarm || !user) {
      Alert.alert(t('common.error'), t('common.missingRequiredInformation'));
      return;
    }

    if (!validateForm()) return;

    setLoading(true);
    try {
      const selectedUser = formData.type === 'user' && formData.userId 
        ? users.find(u => u.value === formData.userId)
        : null;

      const expenseData = {
        farmId: selectedFarm.id,
        farmName: selectedFarm.name,
        type: formData.type as 'farm' | 'user',
        category: formData.category as any,
        amount: Number(formData.amount),
        description: formData.description.trim(),
        date: formData.date,
        userId: formData.type === 'user' ? formData.userId : undefined,
        userName: selectedUser ? selectedUser.label.split(' (')[0] : undefined,
        userRole: selectedUser ? selectedUser.label.match(/\(([^)]+)\)/)?.[1] : undefined,
        notes: formData.notes.trim() || undefined,
        createdBy: user.uid,
        createdByName: user.displayName || user.email || 'Unknown User',
        createdByRole: user.role || 'unknown',
      };

      await createExpense(expenseData);

      Alert.alert(
        t('common.success'), 
        t('expenses.expenseCreated'),
        [
          { 
            text: t('common.ok'), 
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error('Error creating expense:', error);
      Alert.alert(t('common.error'), t('expenses.createError'));
    } finally {
      setLoading(false);
    }
  };

  if (!selectedFarm) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen options={{
          title: t('expenses.addExpense'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }} />
        <View style={styles.centerContainer}>
          <Text style={[styles.message, { color: colors.text }]}>
            {t('farm.selectFarmFirst')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('expenses.addExpense'),
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: 'white',
          headerTitleStyle: { color: 'white', fontWeight: '600' },
        }}
      />

      <ScrollView style={styles.scrollView}>
        <LinearGradient
          colors={[colors.primary, colors.primary + '80']}
          style={styles.header}
        >
          <DollarSign size={32} color="white" />
          <Text style={styles.headerTitle}>{t('expenses.addExpense')}</Text>
          <Text style={styles.headerSubtitle}>
            {t('expenses.addExpenseDescription')}
          </Text>
        </LinearGradient>

        <View style={styles.form}>
          {/* Expense Type */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.expenseType')} *
            </Text>
            <ModalDropdown
              items={EXPENSE_TYPES.map(type => ({
                label: t(`expenses.${type.value}Expense`),
                value: type.value,
              }))}
              selectedValue={formData.type}
              onValueChange={(value) => handleInputChange('type', value)}
              placeholder={t('expenses.selectType')}
              error={!!errors.type}
            />
            {errors.type && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors.type}
              </Text>
            )}
          </View>

          {/* Category */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.category')} *
            </Text>
            <ModalDropdown
              items={EXPENSE_CATEGORIES.map(cat => ({
                label: t(`expenses.categories.${cat.value}`),
                value: cat.value,
              }))}
              selectedValue={formData.category}
              onValueChange={(value) => handleInputChange('category', value)}
              placeholder={t('expenses.selectCategory')}
              error={!!errors.category}
            />
            {errors.category && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors.category}
              </Text>
            )}
          </View>

          {/* User Selection (for user expenses) */}
          {formData.type === 'user' && (
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: colors.text }]}>
                {t('expenses.user')} *
              </Text>
              <ModalDropdown
                items={users}
                selectedValue={formData.userId}
                onValueChange={(value) => handleInputChange('userId', value)}
                placeholder={t('expenses.selectUser')}
                error={!!errors.userId}
              />
              {errors.userId && (
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {errors.userId}
                </Text>
              )}
            </View>
          )}

          {/* Amount */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.amount')} *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: errors.amount ? colors.error : colors.border,
                }
              ]}
              value={formData.amount}
              onChangeText={(value) => handleInputChange('amount', value)}
              placeholder={t('expenses.enterAmount')}
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
            />
            {errors.amount && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors.amount}
              </Text>
            )}
          </View>

          {/* Description */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.description')} *
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: errors.description ? colors.error : colors.border,
                }
              ]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder={t('expenses.enterDescription')}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={3}
            />
            {errors.description && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors.description}
              </Text>
            )}
          </View>

          {/* Date */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.date')} *
            </Text>
            <TouchableOpacity
              style={[
                styles.input,
                {
                  backgroundColor: colors.background,
                  borderColor: errors.date ? colors.error : colors.border,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: 12,
                }
              ]}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={{ color: formData.date ? colors.text : colors.textSecondary }}>
                {formData.date ? formatDate(selectedDate) : t('expenses.selectDate')}
              </Text>
              <Calendar size={20} color={colors.textSecondary} />
            </TouchableOpacity>
            {errors.date && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {errors.date}
              </Text>
            )}
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text }]}>
              {t('expenses.notes')}
            </Text>
            <TextInput
              style={[
                styles.input,
                styles.textArea,
                {
                  backgroundColor: colors.background,
                  color: colors.text,
                  borderColor: colors.border,
                }
              ]}
              value={formData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              placeholder={t('expenses.enterNotes')}
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
            />
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              { 
                backgroundColor: colors.primary,
                opacity: loading ? 0.7 : 1,
              }
            ]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <Text style={styles.submitButtonText}>{t('common.saving')}</Text>
            ) : (
              <>
                <Save size={20} color="white" />
                <Text style={styles.submitButtonText}>{t('expenses.addExpense')}</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={onDateChange}
          maximumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  form: {
    padding: 16,
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    minHeight: 50,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
    gap: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
