import { useEffect, useState, useCallback } from "react";
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Platform, RefreshControl } from "react-native";
import { useLocalSearchParams, useRouter, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { getNoteById, deleteNote, Note } from "@/services/notes-service";
import { Image } from "expo-image";
import { Audio } from "expo-av";
import { Edit, Trash2, Play, Pause, User, Calendar, ArrowLeft } from "lucide-react-native";
import { useAuth } from "@/context/auth-context";
import { useFocusEffect } from "@react-navigation/native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";

export default function NoteDetailsScreen() {
  const { id, farmId } = useLocalSearchParams();
  const [note, setNote] = useState<Note | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const router = useRouter();
  const { user } = useAuth();
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const isDarkMode = theme === "dark";

  // Role-based theme colors
  const getRoleColors = () => {
    switch (user?.role) {
      case 'owner':
        return {
          primary: isDarkMode ? '#2E7D32' : '#4CAF50',
          secondary: '#4CAF50',
          light: isDarkMode ? '#2E7D3220' : '#E8F5E8',
        };
      case 'admin':
        return {
          primary: isDarkMode ? '#1976D2' : '#2196F3',
          secondary: '#2196F3',
          light: isDarkMode ? '#1976D220' : '#E3F2FD',
        };
      case 'caretaker':
        return {
          primary: isDarkMode ? '#F57C00' : '#FF9800',
          secondary: '#FF9800',
          light: isDarkMode ? '#FF980020' : '#FFF3E0',
        };
      default:
        return {
          primary: colors.primary,
          secondary: colors.primary,
          light: isDarkMode ? colors.primary + '20' : colors.primary + '10',
        };
    }
  };

  const roleColors = getRoleColors();

  // Theme colors
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  const primaryColor = roleColors.primary;

  const loadNote = useCallback(async () => {
    try {
      // Ensure id and farmId are strings
      const noteId = Array.isArray(id) ? id[0] : id as string;
      const farmIdParam = Array.isArray(farmId) ? farmId[0] : farmId as string;
      
      if (!noteId) {
        console.error("noteId is required");
        setLoading(false);
        return;
      }
      
      if (!farmIdParam) {
        console.error("farmId is required");
        setLoading(false);
        return;
      }
      
      console.log("Loading note:", noteId, "from farm:", farmIdParam);
      const data = await getNoteById(noteId, farmIdParam);
      console.log("Note loaded:", data?.title);
      setNote(data);
    } catch (error) {
      console.error("Error loading note:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [id, farmId]);

  // Load note when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadNote();
      return () => {
        // Clean up sound when screen loses focus
        if (sound) {
          sound.unloadAsync();
          setSound(null);
          setIsPlaying(false);
        }
      };
    }, [loadNote])
  );

  useEffect(() => {
    return () => {
      // Clean up sound when component unmounts
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  const onRefresh = () => {
    setRefreshing(true);
    loadNote();
  };

  const handlePlayPause = async () => {
    if (!note?.audioUrl) return;

    try {
      if (sound) {
        if (isPlaying) {
          await sound.pauseAsync();
          setIsPlaying(false);
        } else {
          await sound.playAsync();
          setIsPlaying(true);
        }
      } else {
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: note.audioUrl },
          { shouldPlay: true }
        );
        
        setSound(newSound);
        setIsPlaying(true);
        
        newSound.setOnPlaybackStatusUpdate((status) => {
          if (status.isLoaded && status.didJustFinish) {
            setIsPlaying(false);
          }
        });
      }
    } catch (error) {
      console.error("Error playing audio:", error);
      Alert.alert(t("common.error"), t("common.failedToPlayAudio"));
    }
  };

  const handleEdit = () => {
    if (!note || !farmId) return;
    
    const farmIdParam = Array.isArray(farmId) ? farmId[0] : farmId as string;
    
    router.push({
      pathname: "/notes/edit",
      params: { id: note.id, farmId: farmIdParam }
    });
  };

  const handleDelete = async () => {
    if (!note || !farmId) return;

    const farmIdParam = Array.isArray(farmId) ? farmId[0] : farmId as string;
    
    if (!farmIdParam) {
      Alert.alert(t("common.error"), t("common.farmIdRequired"));
      return;
    }

    Alert.alert(
      t("common.deleteNote"),
      t("common.areYouSureDeleteNote"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            setDeleting(true);
            try {
              await deleteNote(note.id, farmIdParam);
              Alert.alert(
                t("common.success"), 
                t("common.noteDeletedSuccessfully"),
                [{ text: t("common.ok"), onPress: () => router.back() }]
              );
            } catch (error) {
              console.error("Error deleting note:", error);
              Alert.alert(t("common.error"), t("common.failedToDeleteNote"));
              setDeleting(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };



  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <Stack.Screen options={{
          title: t("notes.noteDetails"),
          headerStyle: { backgroundColor: primaryColor },
          headerTintColor: "#ffffff",
          headerTitleStyle: { color: "#ffffff", fontWeight: '600' },
        }} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={primaryColor} />
        </View>
      </SafeAreaView>
    );
  }

  if (!note) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <Stack.Screen options={{
          title: t("notes.noteDetails"),
          headerStyle: { backgroundColor: primaryColor },
          headerTintColor: "#ffffff",
          headerTitleStyle: { color: "#ffffff", fontWeight: '600' },
        }} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: "#D32F2F" }]}>{t("common.noteNotFound")}</Text>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: primaryColor }]} 
            onPress={() => router.back()}
          >
            <ArrowLeft size={20} color="#fff" />
            <Text style={styles.backButtonText}>{t("common.goBack")}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Admin can edit/delete any note, caretakers can only edit/delete their own notes
  const canModify = user?.role === "admin" || user?.role === "owner" || user?.uid === note.createdBy || user?.uid === note.userId;



  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={['top']}>
      <Stack.Screen options={{
        title: t("notes.noteDetails"),
        headerStyle: { backgroundColor: primaryColor },
        headerTintColor: "#ffffff",
        headerTitleStyle: { color: "#ffffff", fontWeight: '600' },
      }} />
      

      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={primaryColor}
            colors={[primaryColor]}
          />
        }
      >
        <>
          <View style={styles.header}>
            <Text style={[styles.title, { color: textColor }]}>
              {note.title}
            </Text>
              <View style={styles.metaContainer}>
                <View style={styles.metaItem}>
                  <User size={16} color={secondaryTextColor} style={styles.metaIcon} />
                  <Text style={[styles.metaText, { color: secondaryTextColor }]}>
                    {note.createdByName || note.userName || t("common.unknown")}
                  </Text>
                </View>
                <View style={styles.metaItem}>
                  <Calendar size={16} color={secondaryTextColor} style={styles.metaIcon} />
                  <Text style={[styles.metaText, { color: secondaryTextColor }]}>{formatDate(note.createdAt)}</Text>
                </View>
                {note.priority && (
                  <View style={styles.metaItem}>
                    <View style={[
                      styles.priorityBadge, 
                      { 
                        backgroundColor: note.priority === 'high' ? '#FF5722' : 
                                        note.priority === 'medium' ? '#FF9800' : '#4CAF50' 
                      }
                    ]}>
                      <Text style={styles.priorityText}>{note.priority.toUpperCase()}</Text>
                    </View>
                  </View>
                )}
                {note.category && (
                  <View style={styles.metaItem}>
                    <View style={[styles.categoryBadge, { backgroundColor: primaryColor }]}>
                      <Text style={styles.categoryText}>{note.category}</Text>
                    </View>
                  </View>
                )}
              </View>
            </View>

            <View style={[styles.contentContainer, { backgroundColor: cardColor, borderColor }]}>
              <Text style={[styles.content, { color: textColor }]}>
                {note.content}
              </Text>
            </View>

            {note.images && note.images.length > 0 && (
              <View style={styles.imagesContainer}>
                {note.images.map((imageUri, index) => (
                  <Image
                    key={index}
                    source={{ uri: imageUri }}
                    style={styles.image}
                    contentFit="cover"
                  />
                ))}
              </View>
            )}

            {note.hasAudio && note.audioUrl && Platform.OS !== "web" && (
              <View style={styles.audioContainer}>
                <TouchableOpacity 
                  style={[styles.audioButton, { backgroundColor: primaryColor }]} 
                  onPress={handlePlayPause}
                >
                  {isPlaying ? (
                    <Pause size={24} color="#fff" />
                  ) : (
                    <Play size={24} color="#fff" />
                  )}
                  <Text style={styles.audioButtonText}>
                    {isPlaying ? t("common.pauseVoiceNote") : t("common.playVoiceNote")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {canModify && (
              <View style={styles.actionsContainer}>
                <TouchableOpacity 
                  style={[styles.editButton, { backgroundColor: primaryColor }]} 
                  onPress={handleEdit}
                >
                  <Edit size={20} color="#fff" />
                  <Text style={styles.actionButtonText}>{t("common.edit")}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <>
                      <Trash2 size={20} color="#fff" />
                      <Text style={styles.actionButtonText}>{t("common.delete")}</Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            )}
        </>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginBottom: 20,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    paddingHorizontal: 16,
    fontSize: 16,
    marginRight: 12,
    borderWidth: 1,
  },
  searchButton: {
    padding: 8,
    borderRadius: 8,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 12,
  },
  metaContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  metaIcon: {
    marginRight: 6,
  },
  metaText: {
    fontSize: 14,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  contentContainer: {
    borderRadius: 12,
    padding: 16,
    margin: 16,
    marginTop: 0,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
  },
  content: {
    fontSize: 16,
    lineHeight: 24,
  },
  imagesContainer: {
    margin: 16,
    marginTop: 0,
  },
  image: {
    width: "100%",
    height: 250,
    borderRadius: 12,
    marginBottom: 16,
  },
  audioContainer: {
    margin: 16,
    marginTop: 0,
  },
  audioButton: {
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  audioButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  actionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    gap: 12,
  },
  editButton: {
    flex: 1,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: "#D32F2F",
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});