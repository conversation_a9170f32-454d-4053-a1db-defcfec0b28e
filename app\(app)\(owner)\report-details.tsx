import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, ScrollView, ActivityIndicator, RefreshControl, Dimensions, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { useTheme } from "@/context/theme-context";
import { useFarm } from "@/context/farm-context";
import { getInventoryReport, getUsersReport, getRequestsReport } from "@/services/reports-service";
import { getMachineryReport } from "@/services/machinery-service";
import { ArrowLeft } from "lucide-react-native";

export default function ReportDetailsScreen() {
  const { type, farmId } = useLocalSearchParams();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const { selectedFarm } = useFarm();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  
  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";
  
  const screenWidth = Dimensions.get("window").width - 32;
  
  useEffect(() => {
    loadReportData();
  }, [type, farmId, selectedFarm]);
  
  const loadReportData = async () => {
    setLoading(true);
    try {
      const targetFarmId = farmId as string || selectedFarm?.id;
      
      if (type === "inventory") {
        const data = await getInventoryReport(targetFarmId);
        setReportData(data);
      } else if (type === "users") {
        const data = await getUsersReport(targetFarmId);
        setReportData(data);
      } else if (type === "requests") {
        const data = await getRequestsReport(targetFarmId);
        setReportData(data);
      } else if (type === "machinery") {
        const data = await getMachineryReport(targetFarmId);
        setReportData(data);
      } else if (type === "maintenance") {
        const data = await getMachineryReport(targetFarmId);
        setReportData(data);
      } else if (type === "fuel") {
        const data = await getMachineryReport(targetFarmId);
        setReportData(data);
      } else if (type === "utilization") {
        const data = await getMachineryReport(targetFarmId);
        setReportData(data);
      }
    } catch (error) {
      console.error("Error loading report data:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const onRefresh = () => {
    setRefreshing(true);
    loadReportData();
  };
  
  const getReportTitle = () => {
    switch (type) {
      case "inventory":
        return "Inventory Report";
      case "users":
        return "User Activity Report";
      case "requests":
        return "Requests Report";
      case "machinery":
        return "Machinery Status Report";
      case "maintenance":
        return "Maintenance & Service Report";
      case "fuel":
        return "Fuel & Resources Report";
      case "utilization":
        return "Utilization Trends Report";
      default:
        return "Report Details";
    }
  };
  
  const renderInventoryReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Inventory by Category</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.categoryData && reportData.categoryData.length > 0 ? (
              reportData.categoryData.map((item: any, index: number) => (
                <View key={index} style={styles.categoryItem}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={[styles.categoryName, { color: textColor }]}>{item.name}: {item.count}</Text>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No category data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Inventory Levels</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.inventoryLevels && reportData.inventoryLevels.labels.length > 0 ? (
              reportData.inventoryLevels.labels.map((label: string, index: number) => (
                <View key={index} style={styles.barItem}>
                  <Text style={[styles.barLabel, { color: textColor }]}>{label}</Text>
                  <View style={styles.barContainer}>
                    <View 
                      style={[
                        styles.bar, 
                        { 
                          width: `${(reportData.inventoryLevels.datasets[0].data[index] / Math.max(...reportData.inventoryLevels.datasets[0].data)) * 100}%`,
                          backgroundColor: '#4CAF50'
                        }
                      ]} 
                    />
                    <Text style={[styles.barValue, { color: textColor }]}>
                      {reportData.inventoryLevels.datasets[0].data[index]}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No inventory data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Summary</Text>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Items:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.totalItems || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Low Stock Items:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.lowStockItems || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Expiring Soon:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.expiringItems || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Value:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>
                ${(reportData.summary?.totalValue || 0).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  const renderUsersReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Users by Role</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.roleData && reportData.roleData.length > 0 ? (
              reportData.roleData.map((item: any, index: number) => (
                <View key={index} style={styles.categoryItem}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={[styles.categoryName, { color: textColor }]}>{item.name}: {item.count}</Text>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No user role data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Activity by User</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.activityData && reportData.activityData.labels.length > 0 ? (
              reportData.activityData.labels.map((label: string, index: number) => (
                <View key={index} style={styles.barItem}>
                  <Text style={[styles.barLabel, { color: textColor }]}>{label}</Text>
                  <View style={styles.barContainer}>
                    <View 
                      style={[
                        styles.bar, 
                        { 
                          width: `${(reportData.activityData.datasets[0].data[index] / Math.max(...reportData.activityData.datasets[0].data)) * 100}%`,
                          backgroundColor: '#2196F3'
                        }
                      ]} 
                    />
                    <Text style={[styles.barValue, { color: textColor }]}>
                      {reportData.activityData.datasets[0].data[index]}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No user activity data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Summary</Text>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Users:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.totalUsers || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Owners:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.owners || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Admins:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.admins || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Caretakers:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.caretakers || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Active Users:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.activeUsers || 0}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  const renderRequestsReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Requests by Status</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.statusData && reportData.statusData.length > 0 ? (
              reportData.statusData.map((item: any, index: number) => (
                <View key={index} style={styles.categoryItem}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={[styles.categoryName, { color: textColor }]}>{item.name}: {item.count}</Text>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No request status data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Summary</Text>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Requests:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.totalRequests || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Pending:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.pendingRequests || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Approved:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.approvedRequests || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Rejected:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.rejectedRequests || 0}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  const renderMachineryReport = () => {
    if (!reportData) return null;
    
    return (
      <View style={styles.reportContainer}>
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Machinery by Status</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.statusData && reportData.statusData.length > 0 ? (
              reportData.statusData.map((item: any, index: number) => (
                <View key={index} style={styles.categoryItem}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={[styles.categoryName, { color: textColor }]}>{item.name}: {item.count}</Text>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No machinery status data available</Text>
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Machinery by Type</Text>
          <View style={styles.chartPlaceholder}>
            {reportData.typeData && reportData.typeData.length > 0 ? (
              reportData.typeData.map((item: any, index: number) => (
                <View key={index} style={styles.categoryItem}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={[styles.categoryName, { color: textColor }]}>{item.name}: {item.count}</Text>
                </View>
              ))
            ) : (
              <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No machinery type data available</Text>
            )}
          </View>
        </View>
        
        {type === "fuel" && (
          <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
            <Text style={[styles.cardTitle, { color: textColor }]}>Fuel Levels</Text>
            <View style={styles.chartPlaceholder}>
              {reportData.fuelData && reportData.fuelData.labels.length > 0 ? (
                reportData.fuelData.labels.map((label: string, index: number) => (
                  <View key={index} style={styles.barItem}>
                    <Text style={[styles.barLabel, { color: textColor }]}>{label}</Text>
                    <View style={styles.barContainer}>
                      <View 
                        style={[
                          styles.bar, 
                          { 
                            width: `${(reportData.fuelData.datasets[0].data[index] / Math.max(...reportData.fuelData.datasets[0].data)) * 100}%`,
                            backgroundColor: '#F44336'
                          }
                        ]} 
                      />
                      <Text style={[styles.barValue, { color: textColor }]}>
                        {reportData.fuelData.datasets[0].data[index]}L
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No fuel data available</Text>
              )}
            </View>
          </View>
        )}
        
        {type === "utilization" && (
          <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
            <Text style={[styles.cardTitle, { color: textColor }]}>Utilization Rates</Text>
            <View style={styles.chartPlaceholder}>
              {reportData.utilizationData && reportData.utilizationData.labels.length > 0 ? (
                reportData.utilizationData.labels.map((label: string, index: number) => (
                  <View key={index} style={styles.barItem}>
                    <Text style={[styles.barLabel, { color: textColor }]}>{label}</Text>
                    <View style={styles.barContainer}>
                      <View 
                        style={[
                          styles.bar, 
                          { 
                            width: `${reportData.utilizationData.datasets[0].data[index]}%`,
                            backgroundColor: '#9C27B0'
                          }
                        ]} 
                      />
                      <Text style={[styles.barValue, { color: textColor }]}>
                        {reportData.utilizationData.datasets[0].data[index]}%
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <Text style={[styles.emptyText, { color: secondaryTextColor }]}>No utilization data available</Text>
              )}
            </View>
          </View>
        )}
        
        <View style={[styles.card, { backgroundColor: cardColor, borderColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>Summary</Text>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Total Machinery:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.totalMachinery || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Active Machinery:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.activeMachinery || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Maintenance Due:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.maintenanceDue || 0}</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Utilization Rate:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.utilizationRate || 0}%</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Fuel Consumption:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.fuelConsumption || 0}L</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: secondaryTextColor }]}>Inter-farm Requests:</Text>
              <Text style={[styles.summaryValue, { color: textColor }]}>{reportData.summary?.interFarmRequests || 0}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: getReportTitle(),
          headerStyle: {
            backgroundColor: isDarkMode ? "#1e1e1e" : "#2E7D32",
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.scrollContent}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? "#4CAF50" : "#2E7D32"} />
            <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading report data...</Text>
          </View>
        ) : (
          <>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerTitle, { color: textColor }]}>
                {getReportTitle()}
              </Text>
              <Text style={[styles.headerSubtitle, { color: secondaryTextColor }]}>
                {selectedFarm ? `For ${selectedFarm.name}` : "All Farms"}
              </Text>
            </View>
            
            {type === "inventory" && renderInventoryReport()}
            {type === "users" && renderUsersReport()}
            {type === "requests" && renderRequestsReport()}
            {(type === "machinery" || type === "maintenance" || type === "fuel" || type === "utilization") && renderMachineryReport()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  reportContainer: {
    gap: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  chartPlaceholder: {
    padding: 16,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 14,
    fontStyle: "italic",
  },
  categoryItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    width: "100%",
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  categoryName: {
    fontSize: 14,
  },
  barItem: {
    marginBottom: 12,
    width: "100%",
  },
  barLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  barContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 24,
  },
  bar: {
    height: 20,
    borderRadius: 4,
    marginRight: 8,
  },
  barValue: {
    fontSize: 12,
    minWidth: 30,
  },
  summaryContainer: {
    gap: 12,
  },
  summaryItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});