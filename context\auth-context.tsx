import React, { createContext, useContext, useEffect, useState } from "react";
import { router } from "expo-router";
import { initializeApp } from "firebase/app";
import {
  getAuth,
  initializeAuth,
  getReactNativePersistence,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  updateProfile,
  updatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider,
  User as FirebaseUser,
  Auth
} from "firebase/auth";
import { doc, getDoc, getFirestore, setDoc } from "firebase/firestore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import SplashScreen from "@/components/SplashScreen";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDDKOb-qvTF2516cWV_9TEAgk5pukmozjc",
  authDomain: "kissandost-9570f.firebaseapp.com",
  databaseURL: "https://kissandost-9570f-default-rtdb.firebaseio.com",
  projectId: "kissandost-9570f",
  storageBucket: "kissandost-9570f.firebasestorage.app",
  messagingSenderId: "400828673471",
  appId: "1:400828673471:web:ef962bc0150b3bb0e3c50a",
  measurementId: "G-Z91G06JJVD"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with React Native persistence
let auth: Auth;
try {
  // Try to initialize auth with React Native persistence
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
} catch (error) {
  // If already initialized, get the existing instance
  auth = getAuth(app);
}

const db = getFirestore(app);

// Token storage constants
const TOKEN_STORAGE_KEY = 'userToken';
const USER_DATA_STORAGE_KEY = 'userData';

// Token storage helper functions
const storeToken = async (token: string) => {
  try {
    await AsyncStorage.setItem(TOKEN_STORAGE_KEY, token);
    console.log('✅ Token stored successfully');
  } catch (error) {
    console.error('❌ Error storing token:', error);
  }
};

const getStoredToken = async (): Promise<string | null> => {
  try {
    const token = await AsyncStorage.getItem(TOKEN_STORAGE_KEY);
    return token;
  } catch (error) {
    console.error('❌ Error getting stored token:', error);
    return null;
  }
};

const removeStoredToken = async () => {
  try {
    await AsyncStorage.removeItem(TOKEN_STORAGE_KEY);
    await AsyncStorage.removeItem(USER_DATA_STORAGE_KEY);
    console.log('✅ Token and user data removed successfully');
  } catch (error) {
    console.error('❌ Error removing token:', error);
  }
};

const storeUserData = async (userData: User) => {
  try {
    await AsyncStorage.setItem(USER_DATA_STORAGE_KEY, JSON.stringify(userData));
    console.log('✅ User data stored successfully');
  } catch (error) {
    console.error('❌ Error storing user data:', error);
  }
};

const getStoredUserData = async (): Promise<User | null> => {
  try {
    const userData = await AsyncStorage.getItem(USER_DATA_STORAGE_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('❌ Error getting stored user data:', error);
    return null;
  }
};

// Token refresh helper
const refreshToken = async (): Promise<string | null> => {
  try {
    if (auth.currentUser) {
      const newToken = await auth.currentUser.getIdToken(true); // Force refresh
      await storeToken(newToken);
      console.log('✅ Token refreshed successfully');
      return newToken;
    }
    return null;
  } catch (error) {
    console.error('❌ Error refreshing token:', error);
    return null;
  }
};

// Token validation helper
const validateStoredToken = async (): Promise<boolean> => {
  try {
    const storedToken = await getStoredToken();
    if (!storedToken) {
      return false;
    }

    // Try to decode the token to check expiration
    const tokenParts = storedToken.split('.');
    if (tokenParts.length !== 3) {
      console.log('❌ Invalid token format');
      return false;
    }

    const payload = JSON.parse(atob(tokenParts[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    if (payload.exp && payload.exp < currentTime) {
      console.log('❌ Token has expired');
      return false;
    }

    console.log('✅ Token is still valid');
    return true;
  } catch (error) {
    console.error('❌ Error validating token:', error);
    return false;
  }
};

// User type
export interface User {
  uid: string;
  email: string;
  displayName: string;
  role: "owner" | "admin" | "caretaker";
  photoURL?: string;
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  createdAt?: string;
  updatedAt?: string;
  assignedFarmIds?: string[];
  farmId?: string;
  language?: string;
  preferredLanguage?: string;
  isFirstLogin?: boolean;
  // New fields
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
}

// Additional signup data type
export type AdditionalSignupData = {
  phone?: string;
  phoneNumber?: string;
  bio?: string;
  language?: string;
  preferredLanguage?: string;
  assignedFarmIds?: string[];
  farmId?: string;
  photoURL?: string;
  gender?: string;
  dateOfBirth?: string;
  cnic?: string;
  address?: string;
};

// Auth context type
type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, role: string, additionalData?: AdditionalSignupData) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  checkStoredToken: () => Promise<boolean>;
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to clear all user-related caches
const clearUserCaches = async (userId?: string) => {
  try {
    const keysToRemove = [
      'farms',
      'users',
      'selectedFarmId',
      'inventory',
      'notes',
      'requests',
      'machinery',
    ];
    
    // Add user-specific cache keys if userId is provided
    if (userId) {
      keysToRemove.push(`userFarms_${userId}`);
    }
    
    // Get all keys and filter for user-specific ones
    const allKeys = await AsyncStorage.getAllKeys();
    const userSpecificKeys = allKeys.filter(key => 
      key.startsWith('userFarms_') || 
      key.startsWith('user_') ||
      keysToRemove.includes(key)
    );
    
    await AsyncStorage.multiRemove(userSpecificKeys);
    console.log("Cleared user caches:", userSpecificKeys);
  } catch (error) {
    console.error("Error clearing user caches:", error);
  }
};

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [hasCheckedToken, setHasCheckedToken] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Check for stored token and wait for Firebase auth state
  const checkStoredToken = async (): Promise<boolean> => {
    try {
      console.log('🔍 Checking for stored token...');
      const storedToken = await getStoredToken();
      const storedUserData = await getStoredUserData();

      if (storedToken && storedUserData) {
        console.log('✅ Found stored token and user data');

        // Validate token before using it
        const isTokenValid = await validateStoredToken();
        if (!isTokenValid) {
          console.log('❌ Stored token is invalid or expired');
          await removeStoredToken();
          return false;
        }

        // Set user data immediately since we have valid token and user data
        setUser(storedUserData);
        setCurrentUserId(storedUserData.uid);

        // Navigate to appropriate dashboard based on role
        setTimeout(() => {
          try {
            if (storedUserData.role === "owner") {
              router.replace("/(app)/(owner)");
            } else if (storedUserData.role === "admin") {
              router.replace("/(app)/(admin)");
            } else {
              router.replace("/(app)/(caretaker)");
            }
          } catch (navError) {
            console.error("Navigation error:", navError);
          }
        }, 100);

        console.log('✅ User restored from stored data');
        return true;
      } else {
        console.log('❌ No stored token or user data found');
        return false;
      }
    } catch (error) {
      console.error('❌ Error checking stored token:', error);
      await removeStoredToken();
      return false;
    }
  };

  const refreshUserData = async () => {
    if (!auth.currentUser) return;

    try {
      console.log("🔄 Refreshing user data from Firestore...");
      const firebaseUser = auth.currentUser;
      const userDoc = await getDoc(doc(db, "users", firebaseUser.uid));

      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log("📄 Fresh user data from Firestore:", {
          displayName: userData.displayName,
          name: userData.name,
          email: userData.email
        });

        const newUserData = {
          uid: firebaseUser.uid,
          email: firebaseUser.email || "",
          displayName: userData.displayName || userData.name || firebaseUser.displayName || "",
          name: userData.name || userData.displayName || firebaseUser.displayName || "",
          role: userData.role || "caretaker",
          photoURL: userData.photoURL || firebaseUser.photoURL,
          phone: userData.phone || undefined,
          phoneNumber: userData.phoneNumber || userData.phone || undefined,
          bio: userData.bio || undefined,
          createdAt: userData.createdAt || new Date().toISOString(),
          updatedAt: userData.updatedAt,
          assignedFarmIds: userData.assignedFarmIds || [],
          farmId: userData.farmId || undefined,
          language: userData.language || "en",
          preferredLanguage: userData.preferredLanguage || "en",
          isFirstLogin: userData.isFirstLogin || false,
          gender: userData.gender || undefined,
          dateOfBirth: userData.dateOfBirth || undefined,
          cnic: userData.cnic || undefined,
          address: userData.address || undefined,
        };

        console.log("✅ Updated user context with fresh data:", {
          displayName: newUserData.displayName,
          name: newUserData.name,
          email: newUserData.email
        });

        // Force update user data to ensure profile changes are reflected
        setUser(newUserData);
      } else {
        console.log("❌ User document not found in Firestore");
      }
    } catch (error) {
      console.error("❌ Error refreshing user data:", error);
    }
  };

  // Initialize auth and check for stored token
  useEffect(() => {
    const initializeAuth = async () => {
      if (!hasCheckedToken) {
        console.log('🚀 Initializing auth - checking for stored token...');

        try {
          // First check for stored token
          const hasValidToken = await checkStoredToken();

          if (hasValidToken) {
            console.log('✅ Valid token found and user restored');
            setIsInitializing(false);
            setIsLoading(false);
          } else {
            console.log('❌ No valid token found, waiting for Firebase auth state...');
            // Don't stop loading yet, wait for Firebase auth state
            setIsInitializing(false);

            // Set a fallback timeout to prevent infinite loading
            setTimeout(() => {
              if (isLoading && !user) {
                console.log('⚠️ Fallback: Setting loading to false after timeout');
                setIsLoading(false);
              }
            }, 3000);
          }
        } finally {
          // Always mark that we've checked the token, even if there was an error
          setHasCheckedToken(true);
        }
      }
    };

    initializeAuth();
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log("Auth state changed:", firebaseUser ? "User logged in" : "User logged out");

      // Handle logout during signout process
      if (isSigningOut && !firebaseUser) {
        console.log("Auth state null during signout, proceeding to cleanup");
        setUser(null);
        setCurrentUserId(null);
        await clearUserCaches();

        // Use robust navigation with delay
        setTimeout(() => {
          try {
            router.replace("/(auth)/login");
          } catch (navError) {
            console.error("Auth state change navigation error:", navError);
          }
        }, 100);

        setIsLoading(false);
        return;
      }

      // Ignore login events during signout process
      if (isSigningOut && firebaseUser) {
        console.log("Auth state change to logged-in during signout. Ignoring.");
        return;
      }

      // Skip auth state processing if we already have a user from stored token
      if (hasCheckedToken && user && firebaseUser && user.uid === firebaseUser.uid) {
        console.log("User already set from stored token, skipping auth state processing");
        // Ensure loading is set to false if it hasn't been already
        setIsLoading(false);
        return;
      }

      // Don't process logout events if we haven't checked stored token yet
      if (!hasCheckedToken && !firebaseUser) {
        console.log("Auth state logout detected but haven't checked stored token yet, ignoring");
        return;
      }

      if (firebaseUser) {
        // Check if this is a different user than before
        const isNewUser = currentUserId && currentUserId !== firebaseUser.uid;

        if (isNewUser) {
          console.log("Different user detected, clearing caches");
          await clearUserCaches(currentUserId);
        }

        setCurrentUserId(firebaseUser.uid);

        // If we have stored user data but no user set yet, check if this matches
        if (!user && hasCheckedToken) {
          const storedUserData = await getStoredUserData();
          if (storedUserData && storedUserData.uid === firebaseUser.uid) {
            console.log('✅ Firebase auth state now ready, setting user from stored data');
            setUser(storedUserData);

            // Navigate to appropriate dashboard
            setTimeout(() => {
              try {
                if (storedUserData.role === "owner") {
                  router.replace("/(app)/(owner)");
                } else if (storedUserData.role === "admin") {
                  router.replace("/(app)/(admin)");
                } else {
                  router.replace("/(app)/(caretaker)");
                }
              } catch (navError) {
                console.error("Navigation error:", navError);
              }
            }, 100);

            setIsLoading(false);
            return;
          }
        }
        
        try {
          // Get user data from Firestore
          const userDoc = await getDoc(doc(db, "users", firebaseUser.uid));
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            const userObj: User = {
              uid: firebaseUser.uid,
              email: firebaseUser.email || "",
              displayName: firebaseUser.displayName || userData.displayName || "",
              role: userData.role || "caretaker",
              photoURL: firebaseUser.photoURL || userData.photoURL,
              phone: userData.phone || undefined,
              phoneNumber: userData.phoneNumber || undefined,
              bio: userData.bio || undefined,
              createdAt: userData.createdAt || new Date().toISOString(),
              updatedAt: userData.updatedAt,
              assignedFarmIds: userData.assignedFarmIds || [],
              farmId: userData.farmId || undefined,
              language: userData.language || "en",
              preferredLanguage: userData.preferredLanguage || "en",
              isFirstLogin: userData.isFirstLogin || false,
              gender: userData.gender || undefined,
              dateOfBirth: userData.dateOfBirth || undefined,
              cnic: userData.cnic || undefined,
              address: userData.address || undefined,
            };
            
            setUser(userObj);

            // Store user data for persistence (only if not from stored token)
            if (!hasCheckedToken || !user) {
              await storeUserData(userObj);
            }

            // Only navigate if we're not currently signing out
            if (!isSigningOut) {
              // Check if this is a new owner account (first login) - navigate to farms/create
              if (userData.role === "owner" && userData.isFirstLogin) {
                // Navigate to farms/create for new owners
                router.replace("/(app)/(owner)/farms/create");
              } else {
                // Navigate to the appropriate dashboard based on role
                if (userData.role === "owner") {
                  router.replace("/(app)/(owner)");
                } else if (userData.role === "admin") {
                  router.replace("/(app)/(admin)");
                } else {
                  router.replace("/(app)/(caretaker)");
                }
              }
            }
          } else {
            // If user document doesn't exist, create it with default role
            const createdAt = new Date().toISOString();
            await setDoc(doc(db, "users", firebaseUser.uid), {
              email: firebaseUser.email,
              displayName: firebaseUser.displayName || "",
              role: "caretaker",
              createdAt: createdAt,
              assignedFarmIds: [],
              language: "en",
              preferredLanguage: "en",
              isFirstLogin: false,
            });
            
            const newUser: User = {
              uid: firebaseUser.uid,
              email: firebaseUser.email || "",
              displayName: firebaseUser.displayName || "",
              role: "caretaker",
              photoURL: firebaseUser.photoURL || undefined,
              createdAt: createdAt,
              assignedFarmIds: [],
              language: "en",
              preferredLanguage: "en",
              isFirstLogin: false,
            };
            
            setUser(newUser);

            // Store user data for persistence
            await storeUserData(newUser);

            if (!isSigningOut) {
              router.replace("/(app)/(caretaker)");
            }
          }
        } catch (error) {
          console.error("Error getting user data:", error);
          if (!isSigningOut) {
            setUser(null);
            setCurrentUserId(null);
            router.replace("/(auth)/login");
          }
        }
      } else {
        // User is signed out - but only process if we've checked stored token
        if (hasCheckedToken) {
          console.log("User signed out, clearing user state");
          setUser(null);
          setCurrentUserId(null);

          // Clear all caches when user signs out
          await clearUserCaches();

          // Only navigate to login if we're not already in the auth flow
          try {
            // Check if we're already on an auth screen
            const isOnAuthScreen = typeof window !== 'undefined' &&
              window.location?.pathname?.includes('/(auth)/');

            if (!isOnAuthScreen && !isSigningOut) {
              console.log("Navigating to login screen");
              // Use delayed navigation to ensure Root Layout is mounted
              setTimeout(() => {
                try {
                  router.replace("/(auth)/login");
                } catch (navError) {
                  console.error("Delayed navigation error:", navError);
                }
              }, 150);
            }
          } catch (error) {
            console.error("Error navigating to login:", error);
            // Fallback navigation with longer delay
            setTimeout(() => {
              try {
                router.replace("/(auth)/login");
              } catch (navError) {
                console.error("Fallback navigation error:", navError);
              }
            }, 300);
          }
        } else {
          console.log("User signed out but haven't checked stored token yet, ignoring");
        }
      }

      // Only set loading to false if we've checked the stored token
      if (hasCheckedToken) {
        setIsLoading(false);
      }
    });

    return () => unsubscribe();
  }, [isSigningOut, currentUserId]);

  // Set up token refresh interval (refresh every 50 minutes, tokens expire in 1 hour)
  useEffect(() => {
    let tokenRefreshInterval: NodeJS.Timeout;

    if (user && !isSigningOut && hasCheckedToken) {
      // Only set up refresh if we have a user and have completed initial token check
      tokenRefreshInterval = setInterval(async () => {
        console.log('🔄 Refreshing token automatically...');
        await refreshToken();
      }, 50 * 60 * 1000); // 50 minutes
    }

    return () => {
      if (tokenRefreshInterval) {
        clearInterval(tokenRefreshInterval);
      }
    };
  }, [user, isSigningOut, hasCheckedToken]);



  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);

      // Get the Firebase ID token
      const token = await userCredential.user.getIdToken();

      // Store the token
      await storeToken(token);
      console.log('✅ Login successful, token stored');

      // Mark that we've checked token to prevent loops
      setHasCheckedToken(true);

      // Navigation is handled in the onAuthStateChanged listener
    } catch (error: any) {
      console.error("Sign in error:", error);
      setIsLoading(false);
      throw new Error(error.message || "Failed to sign in");
    }
  };

  const signUp = async (email: string, password: string, name: string, role: string, additionalData?: AdditionalSignupData) => {
    try {
      setIsLoading(true);
      
      // Clear any existing caches before creating new user
      await clearUserCaches();
      
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const { user: firebaseUser } = userCredential;
      
      // Update profile with display name
      await updateProfile(firebaseUser, { displayName: name });
      
      // Create user document in Firestore with all the additional data
      const createdAt = new Date().toISOString();
      const userDocData = {
        email,
        displayName: name,
        name: name,
        role: "owner", // Always set to owner for new signups
        createdAt: createdAt,
        assignedFarmIds: [], // Empty for new users
        farmId: "", // Empty for new users
        phone: additionalData?.phone || "",
        phoneNumber: additionalData?.phoneNumber || additionalData?.phone || "",
        bio: additionalData?.bio || "",
        language: "en", // Default language
        preferredLanguage: "en", // Default language
        photoURL: additionalData?.photoURL || "",
        emailVerified: firebaseUser.emailVerified,
        isFirstLogin: true, // Mark as first login to trigger farms/create navigation
        lastLogin: createdAt,
        id: firebaseUser.uid,
        // New fields
        gender: additionalData?.gender || "",
        dateOfBirth: additionalData?.dateOfBirth || "",
        cnic: additionalData?.cnic || "",
        address: additionalData?.address || "",
      };
      
      await setDoc(doc(db, "users", firebaseUser.uid), userDocData);
      
      // Set the current user ID
      setCurrentUserId(firebaseUser.uid);
      
      // Navigation to farms/create is handled in the onAuthStateChanged listener
    } catch (error: any) {
      console.error("Sign up error:", error);
      setIsLoading(false);
      throw new Error(error.message || "Failed to create account");
    }
  };

  const signOut = async () => {
    console.log("Starting signOut process");

    // Prevent multiple simultaneous signOut calls
    if (isSigningOut) {
      console.log("SignOut already in progress, ignoring duplicate call");
      return;
    }

    try {
      // Set signing out flag first to prevent race conditions
      setIsSigningOut(true);

      // Clear stored token and user data
      await removeStoredToken();
      console.log('✅ Stored token and user data cleared');

      // Clear all user-related caches
      await clearUserCaches(currentUserId || undefined);

      // Clear user state immediately
      setUser(null);
      setCurrentUserId(null);

      // Sign out from Firebase
      await firebaseSignOut(auth);

      console.log("Firebase signOut completed");

      // Use a more robust navigation approach with retry logic
      const navigateToLogin = () => {
        try {
          // Check if router is available and ready
          if (router && typeof router.replace === 'function') {
            router.replace("/(auth)/login");
            console.log("Navigation to login successful");
          } else {
            console.warn("Router not ready, retrying...");
            setTimeout(navigateToLogin, 100);
          }
        } catch (navError) {
          console.error("Navigation error:", navError);
          // Retry navigation after a short delay
          setTimeout(() => {
            try {
              if (router && typeof router.replace === 'function') {
                router.replace("/(auth)/login");
              }
            } catch (retryError) {
              console.error("Retry navigation failed:", retryError);
            }
          }, 200);
        }
      };

      // Delay navigation to ensure Root Layout is mounted
      setTimeout(navigateToLogin, 150);

      console.log("SignOut process completed successfully");

    } catch (error: any) {
      console.error("Sign out error:", error);
      // Even if there's an error, try to navigate to login and clear state
      setUser(null);
      setCurrentUserId(null);
      await clearUserCaches();

      // Use the same robust navigation for error case
      setTimeout(() => {
        try {
          router.replace("/(auth)/login");
        } catch (navError) {
          console.error("Error case navigation failed:", navError);
        }
      }, 200);

      throw new Error(error.message || "Failed to sign out");
    } finally {
      // Reset the signing out flag after a delay to ensure everything is processed
      setTimeout(() => {
        setIsSigningOut(false);
      }, 500);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    if (!auth.currentUser || !user) {
      throw new Error("No user is currently signed in");
    }

    try {
      // Re-authenticate the user with their current password
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(auth.currentUser, credential);
      
      // Update the password
      await updatePassword(auth.currentUser, newPassword);
      
      console.log("Password updated successfully");
    } catch (error: any) {
      console.error("Change password error:", error);
      
      // Provide more specific error messages
      if (error.code === 'auth/wrong-password') {
        throw new Error("Current password is incorrect");
      } else if (error.code === 'auth/weak-password') {
        throw new Error("New password is too weak");
      } else if (error.code === 'auth/requires-recent-login') {
        throw new Error("Please sign out and sign in again before changing your password");
      } else {
        throw new Error(error.message || "Failed to change password");
      }
    }
  };

  // Show splash screen only while checking for stored token initially
  if (!hasCheckedToken) {
    return (
      <AuthContext.Provider value={{ user, isLoading, signIn, signUp, signOut, refreshUserData, changePassword, checkStoredToken }}>
        <SplashScreen message="Checking authentication..." />
      </AuthContext.Provider>
    );
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signUp, signOut, refreshUserData, changePassword, checkStoredToken }}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}