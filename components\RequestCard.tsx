import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { Request } from '@/services/request-service';
import { Edit, Trash2, Clock, CheckCircle, XCircle, Package, Truck, RotateCcw } from 'lucide-react-native';
import { useLanguage } from '@/context/language-context';
import { useLookupStore } from '@/services/lookup_service';

const { width: screenWidth } = Dimensions.get('window');

interface RequestCardProps {
  request: Request;
  onPress: () => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onReturnPress?: () => void;
  showEditOptions?: boolean;
  isDarkMode?: boolean;
  role?: 'owner' | 'admin' | 'caretaker';
  currentUserId?: string;
}

export const RequestCard: React.FC<RequestCardProps> = ({
  request,
  onPress,
  onEditPress,
  onDeletePress,
  onReturnPress,
  showEditOptions = false,
  isDarkMode = false,
  role = 'caretaker',
  currentUserId,
}) => {
  const { t } = useLanguage();
  const { getLookupsByCategory } = useLookupStore();

  // Helper function to resolve machinery type ID to title
  const getMachineryTypeTitle = (typeId: string) => {
    const machineryTypes = getLookupsByCategory('machineryType');
    const typeItem = machineryTypes.find(item => item.id === typeId);
    if (typeItem) {
      const title = typeItem.title.toLowerCase();
      return t(`machinery.${title}`) || t(`machinery.types.${title}`) || typeItem.title;
    }
    return typeId.charAt(0).toUpperCase() + typeId.slice(1);
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'approved':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} color={getStatusColor(status)} />;
      case 'approved':
        return <CheckCircle size={16} color={getStatusColor(status)} />;
      case 'rejected':
        return <XCircle size={16} color={getStatusColor(status)} />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getRequestTypeIcon = (requestType: string) => {
    if (requestType === 'machinery') {
      return <Truck size={20} color={isDarkMode ? '#2196F3' : '#1976D2'} />;
    }
    return <Package size={20} color={isDarkMode ? '#4CAF50' : '#2E7D32'} />;
  };

  const getRequestTitle = () => {
    if (request.requestType === 'machinery') {
      return request.machineryName || t('requests.machinery');
    }
    return request.itemName || t('requests.inventory');
  };

  const getRequestSubtitle = () => {
    if (request.requestType === 'machinery') {
      // Translate machinery type if available
      const machineryType = request.machineryType || '';
      if (machineryType) {
        return getMachineryTypeTitle(machineryType);
      }
      return '';
    }

    if (request.quantity && request.unit) {
      return `${request.quantity} ${request.unit}`;
    }
    
    return request.category || '';
  };

  // Check if this request can be returned
  const canReturn = request.status === 'approved' && 
                   request.canReturn && 
                   !request.hasReturn &&
                   request.requestedBy === currentUserId &&
                   role === 'caretaker';

  // Check if this is a machinery request that can be returned (both use and maintenance)
  const canReturnMachinery = request.status === 'approved' &&
                            request.requestType === 'machinery' &&
                            (request.requestSubType === 'use' || request.requestSubType === 'maintenance') &&
                            !request.hasReturn &&
                            request.requestedBy === currentUserId &&
                            role === 'caretaker';

  const cardColor = isDarkMode ? '#1e1e1e' : '#ffffff';
  const textColor = isDarkMode ? '#ffffff' : '#333333';
  const secondaryTextColor = isDarkMode ? '#aaaaaa' : '#666666';
  const borderColor = isDarkMode ? '#333333' : '#e0e0e0';

  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          backgroundColor: cardColor,
          borderColor: borderColor,
        },
      ]}
      onPress={onPress}
    >
      <View style={styles.cardHeader}>
        <View style={styles.typeIconContainer}>
          {getRequestTypeIcon(request.requestType || 'inventory')}
        </View>
        
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: textColor }]}>
            {getRequestTitle()}
          </Text>
          
          <Text style={[styles.subtitle, { color: secondaryTextColor }]}>
            {getRequestSubtitle()}
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(request.status) + '20' }]}>
            {getStatusIcon(request.status)}
            <Text style={[styles.statusText, { color: getStatusColor(request.status) }]}>
              {t(`requests.${request.status}`)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={[styles.cardBody, { borderTopColor: borderColor }]}>
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
            {t('requests.requestedBy')}:
          </Text>
          <Text style={[styles.infoValue, { color: textColor }]}>
            {request.requestedByName || t('common.unknown')}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={[styles.infoLabel, { color: secondaryTextColor }]}>
            {t('requests.requestedAt')}:
          </Text>
          <Text style={[styles.infoValue, { color: textColor }]}>
            {formatDate(request.createdAt)}
          </Text>
        </View>
        
        {request.reason && (
          <View style={styles.reasonContainer}>
            <Text style={[styles.reasonLabel, { color: secondaryTextColor }]}>
              {t('requests.reason')}:
            </Text>
            <Text style={[styles.reasonText, { color: textColor }]}>
              {request.reason}
            </Text>
          </View>
        )}

        {/* Return Status */}
        {request.hasReturn && request.returnStatus && (
          <View style={styles.returnStatusContainer}>
            <Text style={[styles.returnStatusLabel, { color: secondaryTextColor }]}>
              Return Status:
            </Text>
            <View style={[styles.returnStatusBadge, { backgroundColor: getStatusColor(request.returnStatus) + '20' }]}>
              {getStatusIcon(request.returnStatus)}
              <Text style={[styles.returnStatusText, { color: getStatusColor(request.returnStatus) }]}>
                {request.returnStatus.charAt(0).toUpperCase() + request.returnStatus.slice(1)}
              </Text>
            </View>
          </View>
        )}
      </View>
      
      {(showEditOptions && (onEditPress || onDeletePress || canReturn || canReturnMachinery)) && (
        <View style={[styles.cardFooter, { borderTopColor: borderColor }]}>
          {onEditPress && request.status === 'pending' && request.requestedBy === currentUserId && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: isDarkMode ? '#1976D2' : '#2196F3' }]}
              onPress={onEditPress}
            >
              <Edit size={16} color="#fff" />
              <Text style={styles.actionButtonText}>{t('common.edit')}</Text>
            </TouchableOpacity>
          )}
          
          {(canReturn || canReturnMachinery) && onReturnPress && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: isDarkMode ? '#FF8F00' : '#FF9800' }]}
              onPress={onReturnPress}
            >
              <RotateCcw size={16} color="#fff" />
              <Text style={styles.actionButtonText}>Return</Text>
            </TouchableOpacity>
          )}
          
          {onDeletePress && request.status === 'pending' && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: isDarkMode ? '#d32f2f' : '#f44336' }]}
              onPress={onDeletePress}
            >
              <Trash2 size={16} color="#fff" />
              <Text style={styles.actionButtonText}>{t('common.delete')}</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    padding: screenWidth < 380 ? 12 : 16,
    alignItems: 'center',
  },
  typeIconContainer: {
    marginRight: screenWidth < 380 ? 8 : 12,
  },
  headerContent: {
    flex: 1,
    minWidth: 0, // Allows text to shrink
  },
  title: {
    fontSize: screenWidth < 380 ? 14 : 16,
    fontWeight: '600',
    marginBottom: 4,
    lineHeight: screenWidth < 380 ? 18 : 20,
  },
  subtitle: {
    fontSize: screenWidth < 380 ? 12 : 14,
    lineHeight: screenWidth < 380 ? 16 : 18,
  },
  statusContainer: {
    marginLeft: screenWidth < 380 ? 4 : 8,
    flexShrink: 0,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: screenWidth < 380 ? 6 : 8,
    paddingVertical: screenWidth < 380 ? 3 : 4,
    borderRadius: screenWidth < 380 ? 10 : 12,
    gap: screenWidth < 380 ? 3 : 4,
  },
  statusText: {
    fontSize: screenWidth < 380 ? 10 : 12,
    fontWeight: '600',
  },
  cardBody: {
    padding: screenWidth < 380 ? 12 : 16,
    borderTopWidth: 1,
  },
  infoRow: {
    flexDirection: screenWidth < 380 ? 'column' : 'row',
    justifyContent: 'space-between',
    marginBottom: screenWidth < 380 ? 6 : 8,
    gap: screenWidth < 380 ? 4 : 0,
  },
  infoLabel: {
    fontSize: 14,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  reasonContainer: {
    marginTop: 8,
  },
  reasonLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  reasonText: {
    fontSize: 14,
    lineHeight: 20,
  },
  returnStatusContainer: {
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  returnStatusLabel: {
    fontSize: 14,
  },
  returnStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  returnStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  cardFooter: {
    flexDirection: screenWidth < 380 ? 'column' : 'row',
    padding: screenWidth < 380 ? 8 : 12,
    borderTopWidth: 1,
    justifyContent: 'flex-end',
    gap: screenWidth < 380 ? 6 : 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: screenWidth < 380 ? 10 : 12,
    paddingVertical: screenWidth < 380 ? 5 : 6,
    borderRadius: 8,
    gap: 4,
    justifyContent: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: screenWidth < 380 ? 11 : 12,
    fontWeight: '600',
  },
});