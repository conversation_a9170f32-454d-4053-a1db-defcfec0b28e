import React, { useState, useEffect, useCallback } from "react";
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, RefreshControl, Alert, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useFocusEffect } from "@react-navigation/native";
import { getFarmsByUserId, deleteFarm, Farm } from "@/services/farm-service";
import { getUsersByFarm } from "@/services/user-service";
import { Search, MapPin, Users, Package, Trash2, Edit, Eye, ArrowLeft, Plus } from "lucide-react-native";
import { useTheme } from "@/context/theme-context";
import { useLanguage } from "@/context/language-context";
import { useFarm } from "@/context/farm-context";
import { useAuth } from "@/context/auth-context";
import { router, Stack } from "expo-router";

export default function OwnerFarmsScreen() {
  const { theme, colors } = useTheme();
  const { t, isRTL } = useLanguage();
  const { refreshFarms } = useFarm();
  const { user } = useAuth();
  const isDarkMode = theme === "dark";
  
  const [farms, setFarms] = useState<Farm[]>([]);
  const [filteredFarms, setFilteredFarms] = useState<Farm[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [caretakerCounts, setCaretakerCounts] = useState<{[farmId: string]: number}>({});

  const backgroundColor = isDarkMode ? "#121212" : "#f9f9f9";
  const cardColor = isDarkMode ? "#1e1e1e" : "#ffffff";
  const textColor = isDarkMode ? "#ffffff" : "#333333";
  const secondaryTextColor = isDarkMode ? "#aaaaaa" : "#666666";
  const borderColor = isDarkMode ? "#333333" : "#e0e0e0";

  // Helper function to safely get location display text
  const getLocationDisplay = (farm: Farm): string => {
    if (farm.locationData && typeof farm.locationData === 'object') {
      return farm.locationData.address || t('common.notSet');
    }
    
    if (farm.location) {
      if (typeof farm.location === 'object') {
        return farm.location.address || t('common.notSet');
      }
      if (typeof farm.location === 'string') {
        return farm.location;
      }
    }
    
    return t('common.notSet');
  };

  const loadFarms = async () => {
    try {
      if (!user) {
        setFarms([]);
        setFilteredFarms([]);
        setCaretakerCounts({});
        return;
      }

      // Get only farms assigned to this user
      const data = await getFarmsByUserId(user.uid, user.role);
      setFarms(data);
      setFilteredFarms(data);

      // Get caretaker counts for each farm (each farm has its own count)
      console.log("Loading caretaker counts for each farm...");
      const counts: {[farmId: string]: number} = {};
      for (const farm of data) {
        try {
          const caretakers = await getUsersByFarm(farm.id);
          counts[farm.id] = caretakers.length;
          console.log(`Farm ${farm.name} has ${caretakers.length} caretakers`);
        } catch (error) {
          console.error(`Error getting caretakers for farm ${farm.id}:`, error);
          counts[farm.id] = 0;
        }
      }
      setCaretakerCounts(counts);
    } catch (error) {
      console.error("Error loading farms:", error);
      Alert.alert(t('common.error'), t('farm.loadError'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadFarms();
  }, [user?.uid]); // Only reload when user ID changes, not on every user object change

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log("Farms screen focused, refreshing data...");
      loadFarms();
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFarms();
    // Also refresh the farm context
    await refreshFarms();
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text) {
      const filtered = farms.filter(
        (farm) =>
          farm.name.toLowerCase().includes(text.toLowerCase()) ||
          getLocationDisplay(farm).toLowerCase().includes(text.toLowerCase())
      );
      setFilteredFarms(filtered);
    } else {
      setFilteredFarms(farms);
    }
  };

  const handleCreateFarm = () => {
    router.push("/(app)/(owner)/farms/create");
  };

  const handleEditFarm = (farm: Farm) => {
    router.push({
      pathname: "/(app)/(owner)/farms/edit",
      params: { farmId: farm.id }
    });
  };

  const handleDeleteFarm = async (farm: Farm) => {
    Alert.alert(
      t('farm.deleteFarm'),
      t('farm.deleteConfirm').replace('{farmName}', farm.name),
      [
        { text: t('common.cancel'), style: "cancel" },
        {
          text: t('common.delete'),
          style: "destructive",
          onPress: async () => {
            try {
              await deleteFarm(farm.id, user?.role);
              Alert.alert(t('common.success'), t('farm.farmDeleted'));
              await loadFarms();
              await refreshFarms(); // Refresh farm context
            } catch (error) {
              console.error("Error deleting farm:", error);
              Alert.alert(t('common.error'), t('farm.deleteError'));
            }
          }
        }
      ]
    );
  };

  const handleViewFarmDetails = (farm: Farm) => {
    // Navigate to farm detail screen
    router.push({
      pathname: "/(app)/(owner)/farms/[id]",
      params: { id: farm.id }
    });
  };

  const renderFarmCard = ({ item }: { item: Farm }) => {
    return (
      <TouchableOpacity 
        style={[styles.farmCard, { backgroundColor: cardColor, borderColor }]}
        onPress={() => handleViewFarmDetails(item)}
      >
        <View style={[styles.farmCardHeader, isRTL && styles.rtlFarmCardHeader]}>
          <Text style={[styles.farmName, { color: textColor }]}>{item.name}</Text>
          <View style={[styles.farmActions, isRTL && styles.rtlFarmActions]}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary + "20" }]}
              onPress={() => handleViewFarmDetails(item)}
            >
              <Eye size={16} color={colors.primary} />
            </TouchableOpacity>
            {user?.role === 'owner' && (
              <>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: "#E3F2FD" }]}
                  onPress={() => handleEditFarm(item)}
                >
                  <Edit size={16} color="#1976D2" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, { backgroundColor: "#FFEBEE" }]}
                  onPress={() => handleDeleteFarm(item)}
                >
                  <Trash2 size={16} color="#D32F2F" />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
        
        <View style={[styles.farmInfo, isRTL && styles.rtlFarmInfo]}>
          <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
            <MapPin size={16} color={colors.primary} />
            <Text style={[styles.infoText, { color: secondaryTextColor }, isRTL && styles.rtlInfoText]}>
              {getLocationDisplay(item)}
            </Text>
          </View>
          {item.size && (
            <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
              <Text style={[styles.infoText, { color: secondaryTextColor }]}>
                {item.size} {item.sizeUnit || t('farm.acres')}
              </Text>
            </View>
          )}
          {item.type && (
            <View style={[styles.infoItem, isRTL && styles.rtlInfoItem]}>
              <Text style={[styles.farmType, { color: colors.primary }]}>
                {t(`farm.${item.type}Farm`) || item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Text>
            </View>
          )}
        </View>
        
        {item.description && (
          <Text style={[styles.description, { color: secondaryTextColor }, isRTL && styles.rtlDescription]} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        
        <View style={[styles.farmStats, isRTL && styles.rtlFarmStats]}>
          <View style={[styles.statItem, isRTL && styles.rtlStatItem]}>
            <Users size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: textColor }, isRTL && styles.rtlStatText]}>
              {caretakerCounts[item.id] || 0} {t('farm.caretakers')}
            </Text>
          </View>
          <View style={[styles.statItem, isRTL && styles.rtlStatItem]}>
            <Package size={16} color={colors.primary} />
            <Text style={[styles.statText, { color: textColor }, isRTL && styles.rtlStatText]}>
              {item.inventoryCount || 0} {t('common.totalInventory')}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MapPin size={64} color={secondaryTextColor} />
      <Text style={[styles.emptyTitle, { color: textColor }]}>
        {t('farm.noFarms')}
      </Text>
      <Text style={[styles.emptyText, { color: secondaryTextColor }]}>
        {t('farm.addFirstFarm')}
      </Text>
      {user?.role === 'owner' && (
        <TouchableOpacity
          style={[styles.emptyButton, { backgroundColor: colors.primary }]}
          onPress={handleCreateFarm}
        >
          <Plus size={20} color="#fff" />
          <Text style={styles.emptyButtonText}>{t('farm.createFarm')}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]} edges={[]}>
      <Stack.Screen
        options={{
          title: t('farm.myFarms'),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: "#fff",
          headerLeft: () => (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: "rgba(255, 255, 255, 0.2)", marginLeft: 10 }]}
              onPress={() => {
                if (user?.role === 'admin') {
                  router.push("/(app)/(admin)");
                } else {
                  router.push("/(app)/(owner)");
                }
              }}
            >
              <ArrowLeft size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />
      <View style={[styles.header, isRTL && styles.rtlHeader]}>
        <Text style={[styles.title, { color: textColor }]}>{t('farm.myFarms')}</Text>
      </View>

      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: cardColor, borderColor }, isRTL && styles.rtlSearchBar]}>
          <Search size={20} color={secondaryTextColor} style={[styles.searchIcon, isRTL && styles.rtlSearchIcon]} />
          <TextInput
            style={[styles.searchInput, { color: textColor, textAlign: isRTL ? 'right' : 'left' }]}
            placeholder={t('farm.searchFarms')}
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      <FlatList
        data={filteredFarms}
        renderItem={renderFarmCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContent,
          filteredFarms.length === 0 && styles.emptyListContent
        ]}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
                {t('common.loading')}
              </Text>
            </View>
          ) : (
            renderEmptyState()
          )
        }
      />

      {/* Floating Action Button - Only for owners */}
      {user?.role === 'owner' && (
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: colors.primary }]}
          onPress={handleCreateFarm}
        >
          <Plus size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingBottom: 10,
  },
  rtlHeader: {
    flexDirection: "row-reverse",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  rtlSearchBar: {
    flexDirection: "row-reverse",
  },
  searchIcon: {
    marginRight: 8,
  },
  rtlSearchIcon: {
    marginRight: 0,
    marginLeft: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  farmCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  farmCardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  rtlFarmCardHeader: {
    flexDirection: "row-reverse",
  },
  farmName: {
    fontSize: 18,
    fontWeight: "600",
    flex: 1,
  },
  farmActions: {
    flexDirection: "row",
    gap: 8,
  },
  rtlFarmActions: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  farmInfo: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 12,
    gap: 12,
  },
  rtlFarmInfo: {
    flexDirection: "row-reverse",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  rtlInfoItem: {
    flexDirection: "row-reverse",
  },
  infoText: {
    fontSize: 14,
    marginLeft: 4,
  },
  rtlInfoText: {
    marginLeft: 0,
    marginRight: 4,
  },
  farmType: {
    fontSize: 12,
    fontWeight: "600",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: "rgba(46, 125, 50, 0.1)",
  },
  description: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  rtlDescription: {
    textAlign: 'right',
  },
  farmStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
    paddingTop: 12,
  },
  rtlFarmStats: {
    flexDirection: "row-reverse",
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  rtlStatItem: {
    flexDirection: "row-reverse",
  },
  statText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  rtlStatText: {
    marginLeft: 0,
    marginRight: 6,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  emptyButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  fab: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});